warning: unused import: `serde::Serialize`
 --> src/http_server/routes.rs:8:5
  |
8 | use serde::Serialize;
  |     ^^^^^^^^^^^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: variable does not need to be mutable
   --> src/expert/runner.rs:719:25
    |
719 |                     for mut edit_to_check in final_proposed_edits {
    |                         ----^^^^^^^^^^^^^
    |                         |
    |                         help: remove this `mut`
    |
    = note: `#[warn(unused_mut)]` on by default

warning: unused variable: `expectations`
  --> src/prompt_builder.rs:20:5
   |
20 |     expectations: &[BlockExpectation], // These are the expectations for parsing the LLM's *response* to this prompt.
   |     ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_expectations`
   |
   = note: `#[warn(unused_variables)]` on by default

warning: `lledit` (bin "lledit") generated 3 warnings (run `cargo fix --bin "lledit"` to apply 2 suggestions)
    Finished `dev` profile [unoptimized + debuginfo] target(s) in 0.11s
     Running `target/debug/lledit`
Error: Os { code: 6, kind: Uncategorized, message: "No such device or address" }
