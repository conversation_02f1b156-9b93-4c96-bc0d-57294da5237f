# Implementation Plan for geddit

This document outlines the development steps to build the `geddit` tool as described in the `README.md`.

## Core Principles

*   **Incremental Development:** Implement features step-by-step.
*   **Modularity:** Design components (argument parsing, file handling, LLM interaction) to be as independent as possible.
*   **Error Handling:** Implement robust error handling at each stage.
*   **Testing:** (Implicit) Add tests for key functionalities as they are developed.

## Phases and Steps

### Phase 1: Core CLI, Argument Parsing, and File Handling

**Goal:** Set up the command-line interface, parse all necessary inputs, and prepare file contents.

1.  **Step 1.1: Advanced Argument Parsing**
    *   **Task:** Integrate the `clap` crate for robust command-line argument parsing.
*   **Details:**
        *   Define arguments for:
            *   Positional `<USER_PROMPT>` (string).
            *   Positional `<FILE_PATHS>` (one or more `PathBuf`).
            *   Optional `--provider <PROVIDER>` (string, e.g., "openai", "anthropic", default: "openai").
            *   Optional `--provider-url <URL>` (string).
            *   Optional `--provider-api-key <KEY>` (string).
            *   Optional `--stream` (boolean flag, enable streaming LLM output).
        *   Parse these arguments at the start of `main`.
        *   Store parsed values in a configuration struct or pass them down as needed.
    *   **Depends on:** `clap` crate.

2.  **Step 1.2: File Reading and Line Numbering**
    *   **Task:** Read specified files and prepend line numbers to their content.
    *   **Details:**
        *   For each file path obtained from argument parsing:
            *   Asynchronously read the file content into a string.
            *   Implement a utility function `add_line_numbers(content: &str) -> String` that returns the content with each line prefixed (e.g., `1 | original line`).
            *   Store the line-numbered content, associated with its original file path (e.g., in a `Vec<(PathBuf, String)>` or `HashMap<PathBuf, String>`).
        *   Handle file I/O errors gracefully (e.g., file not found, permission denied).
    *   **Depends on:** Step 1.1.

### Phase 2: First LLM Interaction (Identifying Edit Ranges)

**Goal:** Communicate with the LLM to determine which parts of the files it intends to edit.

3.  **Step 2.1: Constructing the Initial LLM Prompt**
    *   **Task:** Create the prompt that asks the LLM to identify edit locations.
    *   **Details:**
        *   Develop a function that takes the user's high-level prompt and the collection of line-numbered file contents.
        *   Assemble these into a single string formatted as described in `README.md` (section "Initial LLM Prompt").
    *   **Depends on:** Step 1.1, Step 1.2.

4.  **Step 2.2: Sending Initial Prompt and Parsing LLM Response**
    *   **Task:** Send the constructed prompt to the configured LLM and parse its response. Handle streaming if enabled.
    *   **Details:**
        *   Use the `llm` crate instance (initialized based on `--provider` flags from Step 1.1).
        *   If `--stream` is enabled:
            *   Use the `complete_stream` method of the LLM provider.
            *   Print response chunks to `stdout` as they arrive.
            *   Collect the full response string for parsing.
        *   If `--stream` is disabled, use the `complete` method.
*   Implement a parser for the LLM's response (the full collected string), which is expected to be a series of blocks like:
```edit
            path/to/file1.rs
            10-15

*   The `graniet/llm` library will be used for all LLM communications.
*   The `LLMBuilder` will be configured based on the `--provider`, `--provider-url`, and `--provider-api-key` arguments.
*   Ensure that the `ChatProvider` or `CompletionProvider` trait from the `llm` crate is used appropriately for sending prompts and receiving responses.
This plan provides a structured approach. Each step can be broken down further into smaller tasks.