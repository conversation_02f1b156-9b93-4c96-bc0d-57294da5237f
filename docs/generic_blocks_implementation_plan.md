# Generic Block Parsing and Retry Mechanism Implementation Plan

## 1. Introduction & Goals

### 1.1. Problem Statement
Currently, LLEdit possesses a retry mechanism for parsing malformed code blocks, but this logic is tightly coupled with "range" blocks (`range_fixer.rs`, now `block_parsing::range_blocks.rs`). As we introduce more block types (e.g., "summary", "replace") and potentially others in the future, we need a more generic and extensible system for:
1.  Defining how different block types are parsed.
2.  Identifying malformed blocks that seem intended for a specific parser.
3.  Requesting the LLM to fix these malformed blocks in a structured way.
4.  Specifying expectations for the number of each block type in an LLM response.

### 1.2. Goals
The primary goal is to refactor the block parsing and retry logic into a generic framework that can handle various block types with minimal boilerplate for adding new ones. This involves:
-   Defining a common interface (`ParsableBlock` trait) for all block parsers.
-   Implementing a generic processing loop that can use these parsers.
-   Allowing LLM calls to specify expectations for different block types (e.g., "expect exactly 1 summary block and 0 to N range blocks").
-   Centralizing the retry logic, making it applicable to any block type that implements the trait.

### 1.3. Key Benefits
-   **Code Reusability**: Common parsing, validation, and retry logic is shared.
-   **Extensibility**: Adding new block types becomes significantly easier by implementing the `ParsableBlock` trait.
-   **Consistency**: Error handling and LLM interaction for block corrections will be consistent across types.
-   **Clarity**: Prompts to the LLM (both initial and for fixes) can be more clearly structured by composing format descriptions from each expected block type.

## 2. Core Concepts

### 2.1. `RawCodeBlock` (Existing)
This struct, already defined in `src/block_parsing/utils.rs`, represents a syntactically valid fenced code block extracted from the LLM's response.
```rust
// In src/block_parsing/utils.rs
#[derive(Debug, Clone, PartialEq, Eq)]
pub struct RawCodeBlock {
    pub keyword: String,                 // e.g., "range", "summary", or "" if no keyword
    pub content_after_keyword: String,   // Content within the backticks, after the keyword and initial whitespace
    pub full_block_text: String,         // The complete raw text of the block, e.g., "```range\npath\n1-2\n```"
}
```
The `extract_raw_code_blocks(response_text: &str) -> Vec<RawCodeBlock>` function will continue to be the first step in processing an LLM response.

### 2.2. `MalformedBlockInfo`
This struct will represent information about a `RawCodeBlock` that a specific parser identifies as an attempt to conform to its format but is syntactically incorrect.
```rust
// In src/block_parsing/traits.rs (new file)
#[derive(Debug, Clone)]
pub struct MalformedBlockInfo {
    pub raw_text: String,        // The full text of the malformed block.
    pub fix_suggestion: String,  // A parser-specific suggestion on how to fix it.
    pub parser_id: String,       // Identifier of the parser that flagged this block.
}
```

### 2.3. `ParsableBlock` Trait
This new trait will be the cornerstone of the generic system. Each block type (range, replace, summary, etc.) will have a corresponding struct that implements this trait.
```rust
// In src/block_parsing/traits.rs (new file)
use crate::block_parsing::utils::RawCodeBlock;
use crate::file_handler::LabeledFile; // For method signatures needing file context
use std::error::Error;
use std::fmt::Debug; // Keep Debug for MalformedBlockInfo if it derives Debug

// Trait for any parsable block type
pub trait ParsableBlock: Send + Sync {
    // A unique identifier for this parser instance/type. Can be the keyword.
    fn id(&self) -> String;

    // Returns the primary keyword that identifies this block type (e.g., "range").
    // Used to quickly associate RawCodeBlocks with their potential parsers.
    fn keyword(&self) -> &'static str;

    // A human-readable description of the block, its purpose, and its expected format.
    // Used in prompts to guide the LLM. Example: "A 'range' block specifies a file path and line numbers (e.g., path/to/file.ext\nSTART-END)."
    fn block_format_description(&self) -> String;

    // Validates if a RawCodeBlock can be successfully parsed according to this parser's rules.
    // Returns Ok(()) if validation/parsing is successful.
    // Returns Err(Box<dyn Error>) if parsing fails, allowing the error to be inspected.
    // This should typically be called if raw_block.keyword matches self.keyword().
    // Implementers will call their concrete parsing logic internally.
    // `available_files` provides context for validation (e.g., file existence for range blocks).
    fn validate_raw_block(&self, raw_block: &RawCodeBlock, available_files: &[LabeledFile])
        -> Result<(), Box<dyn Error + Send + Sync>>;

    // Identifies if a RawCodeBlock (that might not have the correct keyword or failed parsing via validate_raw_block)
    // appears to be an attempt at this block type but is malformed.
    // This allows for more targeted error messages and retry prompts.
    // `available_files` provides context for identifying malformations (e.g., path checks).
    fn identify_malformed(&self, raw_block: &RawCodeBlock, available_files: &[LabeledFile]) -> Option<MalformedBlockInfo>;

    // Constructs a part of the prompt to ask the LLM to fix specifically identified malformed blocks
    // relevant to *this* parser.
    // `available_files` provides context for fixer prompts (e.g., listing valid file paths).
    fn construct_fixer_prompt_segment(&self, malformed_blocks: &[MalformedBlockInfo], available_files: &[LabeledFile]) -> Option<String>;
}
```

### 2.4. `BlockCount` Enum
This enum defines the expected or allowed number of occurrences for a specific block type.
```rust
// In src/block_parsing/processor.rs (new file)
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum BlockCount {
    Optional,      // 0 or 1
    Exact(usize),
    AtLeast(usize),
    AtMost(usize), // If 0, means block should not be present
    Any,           // Any number, including 0 (e.g., for range blocks)
}
```

### 2.5. `BlockExpectation` Struct
This struct defines what the system expects for a particular type of block in an LLM response.
```rust
// In src/block_parsing/processor.rs (new file)
use crate::block_parsing::traits::ParsableBlock; // Import the ParsableBlock trait
use std::error::Error; // Ensure Error is in scope (used by ParsableBlock)

pub struct BlockExpectation {
    // Each expectation owns its parser instance via a Boxed trait object.
    // Parsers are stateless, so creating instances like Box::new(RangeBlockParser{}) is fine.
    pub parser: Box<dyn ParsableBlock>,
    pub expected_count: BlockCount,
    // is_critical is removed; BlockCount (e.g., Exact(1), AtLeast(1)) implies criticality.
    // Optional or Any allow for zero occurrences without it being a processor error.
}
```

### 2.6. Generic Parsing & Retry Loop (`process_llm_response_with_blocks`)
A new central function responsible for orchestrating the parsing and retry logic.

**Location**: `src/block_parsing/processor.rs`

**Signature Sketch**:
```rust
use crate::llm::{ChatMessage, LLMClient};
use crate::config::AppConfig;
use std::collections::HashMap;

pub struct ProcessedBlocksOutput {
    // Key: parser.id()
    // Value: Vec of RawCodeBlocks that this parser has successfully validated.
    // The caller will use the parser's concrete parsing method to get typed data.
    pub successfully_parsed_blocks: HashMap<String, Vec<RawCodeBlock>>,
    pub remaining_raw_blocks: Vec<RawCodeBlock>, // Blocks not claimed by any parser
    // Add conversation_log if it's modified and needs to be returned
}

pub async fn process_llm_response_with_blocks(
    llm_response_text: &str,
    expectations: &[BlockExpectation],
    original_user_prompt: &str, // For context in retry prompts
    available_files: &[LabeledFile], // For context if parsers need it
    llm_instance: &dyn LLMClient,
    conversation_log: &mut Vec<ChatMessage>, // Modified with retries
    app_config: &AppConfig,
) -> Result<ProcessedBlocksOutput, String>; // String is error message
```

**Logic Outline**:
1.  Initialize `successfully_parsed_blocks: HashMap<String, Vec<RawCodeBlock>>`.
2.  Loop up to `MAX_FIX_ATTEMPTS` (e.g., 3):
    a.  Extract all `RawCodeBlock`s from the current `llm_response_text` using `extract_raw_code_blocks`.
    b.  Initialize `all_malformed_for_this_attempt: Vec<MalformedBlockInfo>` and `all_claimed_raw_blocks_indices: HashSet<usize>`.
    c.  Clear `successfully_parsed_blocks` for the current attempt (or manage updates carefully if accumulating). For simplicity, let's re-parse all expected blocks each attempt.

    d.  **Parsing Pass**: For each `expectation` in `expectations`:
        i.  Initialize `current_parser_validated_raw_blocks: Vec<RawCodeBlock>`.
        ii. For each `(idx, raw_block)` in `RawCodeBlock`s (enumerated):
            1.  If `raw_block.keyword == expectation.parser.keyword()`:
                -   Attempt `expectation.parser.validate_raw_block(raw_block, available_files)`.
                -   On success (i.e., `Ok(())`), add the `raw_block` itself to `current_parser_validated_raw_blocks` and mark `raw_block` as claimed (add `idx` to `all_claimed_raw_blocks_indices`).
                -   On failure (i.e., `Err(parse_err)`), call `expectation.parser.identify_malformed(raw_block, available_files)` (potentially passing `parse_err` if `identify_malformed` is adapted to use it). If it returns `Some(info)`, add to `all_malformed_for_this_attempt` and mark as claimed.
        iii. Store `current_parser_validated_raw_blocks` in `successfully_parsed_blocks` for `expectation.parser.id()`.

    e.  **Malformed Identification Pass (for unclaimed blocks)**: For each `(idx, raw_block)` not yet in `all_claimed_raw_blocks_indices`:
        i.  For each `expectation` in `expectations`:
            1.  If `expectation.parser.identify_malformed(raw_block, available_files)` returns `Some(info)`, add to `all_malformed_for_this_attempt`, mark as claimed, and break from inner loop (block claimed by one parser for malformation).

    f.  **Validation Pass**:
        i.  `found_issues_for_retry = false`.
        ii. For each `expectation`:
            1.  Check count of successfully parsed blocks for `expectation.parser.id()` against `expectation.expected_count`.
            2.  If mismatch: `found_issues_for_retry = true`. Log warning/debug about count mismatch.
        iii. If `!all_malformed_for_this_attempt.is_empty()`: `found_issues_for_retry = true`.

    g.  **Decision**:
        i.  If `!found_issues_for_retry`: Break loop, parsing successful.
        ii. If `fix_attempts >= MAX_FIX_ATTEMPTS`: Break loop, max attempts reached. Log warnings for outstanding issues.
        iii. Else (issues found and attempts remain):
            1.  Construct `retry_prompt_content`. Start with a general message.
            2.  For each parser that had malformed blocks (group `all_malformed_for_this_attempt` by `parser_id`):
                -   Find the corresponding `parser` from `expectations` (e.g., by matching `parser_id` with `parser.id()`).
                -   Call `parser.construct_fixer_prompt_segment(&relevant_malformed_blocks_for_this_parser, available_files)` and append to `retry_prompt_content`.
            3.  For each parser with count mismatches:
                -   Append a message like: "For '{parser.id()}' blocks (format: {parser.block_format_description()}), expected {count_rule}, but found {actual_count}. Please provide the correct blocks."
            4.  Append overall instructions: "Please review the feedback and provide a corrected full response containing all necessary blocks. Ensure all blocks adhere to their specified formats: \n {list of all parser.block_format_description()}"
            5.  Send `retry_prompt_content` to LLM (updating `conversation_log` and `llm_response_text`). Increment `fix_attempts`.

3.  After loop, collect `remaining_raw_blocks` (those not in `all_claimed_raw_blocks_indices` from the *last successful parsing attempt* or *final attempt*).
4.  **Final Result**:
    a.  If `found_issues_for_retry` is `false` after the loop (meaning the last attempt was successful or no issues were found initially): Return `Ok(ProcessedBlocksOutput)`.
    b.  Else (issues persisted through all retries):
        i.  Construct a comprehensive error message detailing:
            -   Any remaining malformed blocks (from `all_malformed_for_this_attempt` of the final attempt).
            -   Any `BlockExpectation`s whose `expected_count` was not met (e.g., `Exact(N)` but found `M != N` blocks; `AtLeast(N)` but found `< N` blocks; `AtMost(N)` but found `> N` blocks). `Optional` and `Any` counts are inherently satisfied even if 0 blocks are found, so they wouldn't cause an error here unless the issue is malformation.
        ii. Return `Err(comprehensive_error_message)`.


## 3. Implementation of `ParsableBlock` for Existing Types

### 3.1. `RangeBlockParser` (in `src/block_parsing/range_blocks.rs`)
-   **Struct**: `pub struct RangeBlockParser;`
-   **Concrete Parsing Method for `RangeBlockParser`**:
    -   `pub fn parse_to_edit_target(&self, raw_block: &RawCodeBlock) -> Result<EditTarget, RangeParsingError>`
        -   This method contains the core logic to parse `raw_block.content_after_keyword` into an `EditTarget`.
        -   It will use a local `RangeParsingError` enum (e.g., `PathMissing`, `InvalidLinesFormat`, etc.) that implements `std::error::Error`, `Debug`, and `Display`.
        -   Logic adapted from `check_and_extract_ranges`.
-   **`impl ParsableBlock for RangeBlockParser`**:
    -   `id()`: `"range_blocks"`.
    -   `keyword()`: `"range"`.
    -   `block_format_description()`: `"A 'range' block specifies a file path and line numbers to be edited, or '0-0' for new file creation. Format: path/to/file.ext\\nSTART_LINE-END_LINE"`.
    -   `validate_raw_block(&self, raw_block: &RawCodeBlock, available_files: &[LabeledFile]) -> Result<(), Box<dyn Error + Send + Sync>>`:
        -   Calls `self.parse_to_edit_target(raw_block, available_files)` (concrete method needs `available_files` for path validation).
        -   If `Ok(_)`, returns `Ok(())`.
        -   If `Err(e)`, returns `Err(Box::new(e) as Box<dyn Error + Send + Sync>)`.
    -   `identify_malformed(&self, raw_block: &RawCodeBlock, available_files: &[LabeledFile])`: (Logic remains similar to original plan)
        -   Input: Any `raw_block`, and `available_files` for context.
        -   If `raw_block.keyword` is empty or slightly misspelled (e.g., "rnge"), check its `content_after_keyword` against range patterns (path + lines).
        -   Adapt existing heuristics from `check_and_extract_ranges`, using `available_files` to check path validity if relevant to malformation identification.
        -   Return `Some(MalformedBlockInfo)` with appropriate `fix_suggestion`.
    -   `construct_fixer_prompt_segment(&self, malformed_blocks: &[MalformedBlockInfo], available_files: &[LabeledFile])`: (Logic remains similar to original plan)
        -   Adapt `construct_fixer_prompt` from `range_blocks.rs`, using `available_files` to list valid paths if needed.

### 3.2. `RangeReplaceBlockParser` (in `src/block_parsing/range_replace_block.rs`)
-   **Struct**: `pub struct RangeReplaceBlockParser;`
-   **Concrete Parsing Method for `RangeReplaceBlockParser`**:
    -   `pub fn parse_to_target_and_content(&self, raw_block: &RawCodeBlock, available_files: &[LabeledFile]) -> Result<ParsedRangeReplace, RangeReplaceParsingError>`
        -   This method contains the core logic to parse `raw_block.content_after_keyword` into a `ParsedRangeReplace` struct, which contains an `EditTarget` and the replacement content string.
        -   It will use a local `RangeReplaceParsingError` enum.
-   **`impl ParsableBlock for RangeReplaceBlockParser`**:
    -   `id()`: `"range_replace_block"`.
    -   `keyword()`: `"range-replace"`.
    -   `block_format_description()`: `"A 'range-replace' block specifies a file, a 1-indexed line range (or 0-0 for new file), and the new content using special markers. Format:\n```range-replace\npath/to/file.ext\nSTART_LINE-END_LINE\n<<<<<<< RANGE\n(optional original code, which is ignored)\n=======\nnew_content_here\n>>>>>>> REPLACE\n```"`.
    -   `validate_raw_block`: Calls `self.parse_to_target_and_content`.
    -   `identify_malformed`: Checks for misspellings and structural issues.
    -   `construct_fixer_prompt_segment`: Provides specific feedback for `range-replace` blocks.

### 3.3. `ReplaceBlockParser` (in `src/block_parsing/replace_blocks.rs`)

-   **Struct**: `pub struct ReplaceBlockParser;`
-   **Concrete Parsing Method for `ReplaceBlockParser`**:
    -   `pub fn parse_to_string(&self, raw_block: &RawCodeBlock) -> Result<String, ReplaceParsingError>`
        -   Core logic: returns `raw_block.content_after_keyword`.
        -   `ReplaceParsingError` (e.g., `BlockIsEmpty`) defined locally, implementing `std::error::Error`, `Debug`, `Display`. This might be an infallible conversion if empty content is fine.
-   **`impl ParsableBlock for ReplaceBlockParser`**:
    -   `id()`: `"replace_block"`.
    -   `keyword()`: `"replace"`.
    -   `block_format_description()`: `"A 'replace' block provides the new code content. Format: ```replace\\n<new_code_here>\\n```"`.
    -   `validate_raw_block(&self, raw_block: &RawCodeBlock, _available_files: &[LabeledFile]) -> Result<(), Box<dyn Error + Send + Sync>>`:
        -   Calls `self.parse_to_string(raw_block)`. `_available_files` is unused for this simple parser.
        -   If `Ok(_)`, returns `Ok(())`.
        -   If `Err(e)`, returns `Err(Box::new(e) as Box<dyn Error + Send + Sync>)`.
    -   `identify_malformed(&self, raw_block: &RawCodeBlock, _available_files: &[LabeledFile])`: (Logic remains similar to original plan)
        -   Could check for `raw_block.keyword` being a common misspelling like "replac" or "repalce".
    -   `construct_fixer_prompt_segment(&self, _malformed_blocks: &[MalformedBlockInfo], _available_files: &[LabeledFile])`: (Logic remains similar to original plan)
        -   "The 'replace' block was malformed. Ensure it starts with ```replace and contains the code to be inserted."

### 3.3. `SummaryBlockParser` (in `src/block_parsing/summary_blocks.rs`)
-   **Struct**: `pub struct SummaryBlockParser;`
-   **Concrete Parsing Method for `SummaryBlockParser`**:
    -   `pub fn parse_to_string(&self, raw_block: &RawCodeBlock) -> Result<String, SummaryParsingError>`
        -   Core logic: returns `raw_block.content_after_keyword.trim().to_string()`.
        -   `SummaryParsingError` (e.g., `BlockIsEmpty`) defined locally, implementing `std::error::Error`, `Debug`, `Display`.
-   **`impl ParsableBlock for SummaryBlockParser`**:
    -   `id()`: `"summary_block"`.
    -   `keyword()`: `"summary"`.
    -   `block_format_description()`: `"A 'summary' block provides a brief summary. Format: ```summary\\n<summary_text_here>\\n```"`.
    -   `validate_raw_block(&self, raw_block: &RawCodeBlock, _available_files: &[LabeledFile]) -> Result<(), Box<dyn Error + Send + Sync>>`:
        -   Calls `self.parse_to_string(raw_block)`. `_available_files` is unused for this simple parser.
        -   If `Ok(_)`, returns `Ok(())`.
        -   If `Err(e)`, returns `Err(Box::new(e) as Box<dyn Error + Send + Sync>)`.
    -   `identify_malformed(&self, raw_block: &RawCodeBlock, _available_files: &[LabeledFile])`: (Logic remains similar to original plan)
        -   Similar to `ReplaceBlockParser`, check for misspellings.
    -   `construct_fixer_prompt_segment(&self, _malformed_blocks: &[MalformedBlockInfo], _available_files: &[LabeledFile])`: (Logic remains similar to original plan)
        -   "The 'summary' block was malformed. Ensure it starts with ```summary and contains the summary text."

## 4. Detailed Integration Plan

### Phase 1: Trait Definition and Core Structs
1.  **Create `src/block_parsing/traits.rs`**:
    -   Define `MalformedBlockInfo` struct.
    -   Define the simplified `ParsableBlock` trait (as per section 2.3, updated).
2.  **Create `src/block_parsing/processor.rs`**:
    -   Define `BlockCount` enum.
    -   Define `BlockExpectation` struct using `Box<dyn ParsableBlock>`.
    -   Define `ProcessedBlocksOutput` struct.
    -   Add `mod traits;` and `mod processor;` to `src/block_parsing/mod.rs`.
    -   (The `ParsableBlockUntyped` trait and its blanket implementation are no longer needed).

### Phase 2: Implement `ParsableBlock` for Existing Types
(Each parser struct will define its core parsing logic in a public method returning concrete types. The `ParsableBlock::validate_raw_block` trait method will call this concrete method and convert the result to `Ok(())` or a boxed error. Each parser will define its own specific error type that implements `std::error::Error`, `Debug`, and `Display`.)
1.  **Refactor `src/block_parsing/range_blocks.rs`**:
    -   Define `pub struct RangeBlockParser;`.
    -   Define `pub enum RangeParsingError { ... }`. Implement `std::error::Error`, `Debug`, `Display`.
    -   Implement `pub fn parse_to_edit_target(&self, raw_block: &RawCodeBlock, available_files: &[LabeledFile]) -> Result<EditTarget, RangeParsingError>`. This method will need `available_files` to check for existence of non-new files.
    -   Implement `ParsableBlock` for `RangeBlockParser`, where `validate_raw_block` calls `parse_to_edit_target` (passing `available_files`) and adapts the result.
    -   Adapt logic from `check_and_extract_ranges` and `construct_fixer_prompt`. The `MalformedRangeBlock` struct will be replaced by `MalformedBlockInfo`.
2.  **Refactor `src/block_parsing/replace_blocks.rs`**:
    -   Define `pub struct ReplaceBlockParser;`.
    -   Define `pub enum ReplaceParsingError { BlockIsEmpty }` (or similar). Implement `Error`, `Debug`, `Display`.
    -   Implement `pub fn parse_to_string(&self, ...) -> Result<String, ReplaceParsingError>`.
    -   Implement `ParsableBlock` for `ReplaceBlockParser`, where `validate_raw_block` calls `parse_to_string` and adapts the result.
    -   Adapt `parse_replacement_code`.
3.  **Refactor `src/block_parsing/summary_blocks.rs`**:
    -   Define `pub struct SummaryBlockParser;`.
    -   Define `pub enum SummaryParsingError { BlockIsEmpty }` (or similar). Implement `Error`, `Debug`, `Display`.
    -   Implement `pub fn parse_to_string(&self, ...) -> Result<String, SummaryParsingError>`.
    -   Implement `ParsableBlock` for `SummaryBlockParser`, where `validate_raw_block` calls `parse_to_string` and adapts the result.
    -   Adapt `extract_summary_content`.

### Phase 3: Implement Generic Processor Function
1.  **Implement `process_llm_response_with_blocks` in `src/block_parsing/processor.rs`**:
    -   Follow the logic outlined in section 2.6.
    -   Pay close attention to managing `conversation_log` during retries (add user fix prompt, add new assistant response).
    -   Ensure `MAX_FIX_ATTEMPTS` is respected (perhaps from `app_config`).

### Phase 4: Update Callers to Use the New Processor
1.  **Modify `src/editor/initial_plan.rs` (`_handle_get_initial_plan`)**:
    -   Remove the existing loop that calls `range_blocks::check_and_extract_ranges` and `range_blocks::construct_fixer_prompt`.
    -   **Update initial prompt construction**: The prompt sent to the LLM (currently by `prompt_builder::construct_initial_prompt`) must be generalized. It should iterate through the `block_format_description()` of all parsers in `expectations` to inform the LLM about all expected block types and their formats.
    -   Create `Vec<BlockExpectation>`:
        -   One for `RangeBlockParser` (e.g., `expected_count = BlockCount::Any`).
        -   One for `SummaryBlockParser` (e.g., `expected_count = BlockCount::Optional` or `BlockCount::Exact(1)` if the summary is now mandatory).
    -   Call `block_parsing::processor::process_llm_response_with_blocks`.
    -   Handle the `Result<ProcessedBlocksOutput, String>`:
        -   On `Ok`, for `output.successfully_parsed_blocks["range_blocks"]` (which is `Vec<RawCodeBlock>`):
            -   Instantiate `RangeBlockParser{}`.
            -   For each `raw_block`, call `range_parser.parse_to_edit_target(raw_block, &labeled_files_vec)` to get `Result<EditTarget, _>`. Collect these. (Note: `labeled_files_vec` or equivalent context needs to be passed).
        -   Similarly for summary, using `SummaryBlockParser{}.parse_to_string(raw_block)`.
        -   Log any `output.remaining_raw_blocks` if unexpected.
        -   On `Err`, propagate the error.
    -   The temporal inference call for summary will be subsumed by this if summary is an expectation. For now, assume it's part of the main block expectations.
2.  **Modify `src/editor/target_processor.rs` (`_handle_process_target`)**:
    -   Remove the direct call to `replace_blocks::parse_replacement_code`.
    -   Create `Vec<BlockExpectation>`:
        -   One for `Box::new(ReplaceBlockParser{})` (`expected_count = BlockCount::Exact(1)`).
    -   Call `block_parsing::processor::process_llm_response_with_blocks`.
    -   Handle the `Result`: For `output.successfully_parsed_blocks["replace_block"]`:
        -   Instantiate `ReplaceBlockParser{}`.
        -   Call `replace_parser.parse_to_string(raw_block)` for the (expected single) `raw_block` to get `Result<String, _>`.
    -   If `process_llm_response_with_blocks` returns `Err`, propagate it.
    -   If `process_llm_response_with_blocks` returns `Ok(output)` but the `replace_block` (which has `BlockCount::Exact(1)`) is not present exactly once in `output.successfully_parsed_blocks` (or its parsing fails), `_handle_process_target` should return an `Err(String)`, e.g., "Replace block (expected exactly 1) missing or invalid after retries."
3.  **Review `src/llm_client.rs`**:
    -   `temporal_chat_inference` might still be useful for simple one-off LLM calls that don't involve complex block parsing. If it's used for calls that *do* expect blocks, it should also be updated to use the new processor. The current use for summary in `initial_plan.rs` will be replaced.

### Phase 5: Testing
1.  **Unit Tests**:
    -   For each `XxxBlockParser`'s implementation of `ParsableBlock` methods.
    -   Test `parse_from_raw_block` with valid raw blocks.
    -   Test `identify_malformed` with various malformed inputs.
    -   Test `construct_fixer_prompt_segment`.
2.  **Integration Tests for `process_llm_response_with_blocks`**:
    -   Create mock `ParsableBlock` implementations or use the real ones.
    -   Test scenarios:
        -   All blocks correct on the first try.
        -   Some blocks malformed, fixed after 1 retry.
        -   Incorrect counts (e.g., for `Exact(1)`), fixed after 1 retry.
        -   Mixed issues (malformed and count), fixed after retries.
        -   Issues persist after max retries (e.g., `Exact(1)` expectation not met, or malformed blocks remain).
        -   No blocks found when `Exact(N > 0)` or `AtLeast(N > 0)` were expected.
        -   Too many blocks found for `Exact(N)` or `AtMost(N)`.
        -   Correct number of blocks for `Optional` or `Any` (including zero).
        -   Unexpected blocks present (handled by `remaining_raw_blocks`).
    -   Mock the `llm_instance.chat()` calls to control LLM responses during tests.
3.  **Update End-to-End Tests**:
    -   Ensure existing application behavior is preserved or correctly modified.
    -   Test new scenarios if the generic parsing enables new capabilities easily.

### Phase 6: Documentation and Cleanup
1.  Update any internal documentation or comments related to block parsing.
2.  Remove old, now unused functions like `range_blocks::check_and_extract_ranges` and `range_blocks::construct_fixer_prompt` (once their logic is fully migrated).
3.  Remove the `MalformedRangeBlock` struct from `src/block_parsing/range_blocks.rs` as it's replaced by `MalformedBlockInfo` from `src/block_parsing/traits.rs`.
4.  Ensure `generic_blocks_implementation_plan.md` is accurate and reflects the final implementation.

## 5. Prompt Engineering Considerations

-   **Initial Prompts**: When an LLM call is expected to return blocks, the initial prompt must clearly state all expected block types and their formats. This can be achieved by concatenating `parser.block_format_description()` for all relevant `BlockExpectation`s.
    Example snippet for an initial prompt:
    ```
    Please provide your response using the following blocks:
    1. Summary Block: {SummaryBlockParser.block_format_description()}
    2. Range Edit Blocks: {RangeBlockParser.block_format_description()} (You can provide multiple of these)
    ```
-   **Retry Prompts**: The generic retry prompt built by `process_llm_response_with_blocks` should:
    -   Start with a clear statement that there were issues.
    -   Include specific feedback for each block type using `parser.construct_fixer_prompt_segment()`.
    -   Include feedback on count mismatches.
    -   Reiterate the expected formats for all blocks involved in the expectations.
    -   Clearly ask the LLM to provide a *complete and corrected* set of all necessary blocks.

This detailed plan should provide a good roadmap for the refactoring.
