# lledit: *L*ocal *L*LM Code *Edit*or

**`lledit` is a CLI tool optimized for getting the absolute most out of code editing with locally hosted
LLMs. It is specifically designed to guarantee high edit success rates,
facilitate faster edit submissions and retries, and empower local LLMs to perform
at their best when editing code.

## What Makes Lledit Unique?

1.   **Utilizes a novel local-only iterative code editing model** which takes great advantage of prompt caching free from service-provider multi-second delays and high costs for every single inference.
2.   **Significantly higher edit success rate compared to existing code editors** as models like Qwen3 consistently fail to submit successful edits in tools like <PERSON><PERSON> even with multiple fix requests (addressing the slowness and unreliability).Aider
3.   **Extremely fast startup in under a second** unlike most python-based alternatives which take many seconds to get up and running.
4.   **(Future) Structured Generation integration to guarantee succesful edits** and provide significant speed and code quality gains throughout the process.

## Features

*   **Multi-File Support:** Process and request edits for multiple files in a single command.
*   **New File Creation:** LLM can request to create new files as part of its plan.
*   **Line Numbering:** Automatically prepends line numbers to file contents for precise LLM 
interaction.
*   **Targeted Editing:** LL<PERSON> first suggests *where* to edit, then *how* to edit, one section at a 
time.
*   **Range Block Self-Correction:** If the LLM provides malformed range blocks, `lledit` 
prompts the LLM to fix them.
*   **Focused Prompts:** Each LLM request is scoped, improving accuracy and reducing token usage.
*   **Flexible Configuration:** Settings can be managed via CLI flags, a YAML configuration file, 
and environment variables.
*   **JSON Output:** Optionally output a summary of operations (chat history, edits made) as JSON.

## Installation

```bash
git clone https://github.com/your-repo/lledit.git
cd lledit
cargo build --release
# The executable will be in target/release/lledit
```

## Configuration

`lledit` uses a hierarchical configuration system. Settings are resolved in the following order
of precedence (highest to lowest):

1.  **Command-Line Flags:** Explicitly passed flags override all other settings.
2.  **YAML Configuration File:** A file named `.lledit.config.yml` in your user's home 
directory (`~/.lledit.config.yml`).
3.  **Environment Variables:** Specific environment variables for provider settings.
4.  **Default Values:** Hardcoded defaults within the application.

### YAML Configuration File (`~/.lledit.config.yml`)

If this file does not exist when `lledit` is run, it will be automatically created with all 
available settings commented out, along with descriptions. You can then uncomment and modify these 
settings to set your preferred defaults.

Example of a generated `~/.lledit.config.yml`:
```yaml
# LLEdit Configuration File
#
# This file allows you to set default values for LLEdit's command-line options.
# Settings in this file are overridden by command-line flags.
# Environment variables (like OPENAI_API_KEY) are overridden by settings here and by CLI flags.

# --- LLM Provider Settings ---

# API Keys are configured per model in the `models_list` below using `provider_api_key`,
# or sourced from environment variables if not specified in the model entry.

# Default model alias to use from the models_list below.
# This alias must match one of the aliases defined in the models_list.
# Example: "local-qwen3-32b"
# default_model: "local-qwen3-32b"

# Model alias (from models_list) to use for expert prompts.
# If not set, and auto_expert is 'true' or 'forced', an error will occur.
# Example: "cloud-openai-gpt4o"
# expert_model: "cloud-openai-gpt4o"

# List of available model configurations.
# Each entry requires an 'alias', 'provider', and 'model'.
# 'provider_url' is optional and will override the default for that provider if set.
# 'provider_api_key' is optional. If provided, it overrides any environment variable for this specific model.
# Setting provider_api_key to "none" (case-insensitive string) is equivalent to it being absent or null (falling back to environment variables).
# models_list:
#   - alias: "local-qwen3-32b" # Example for a locally hosted Qwen model via Ollama
#     provider: "ollama" # Use 'ollama' provider for GenAI's Ollama integration
#     model: "qwen/qwen2:72b-instruct-q4_K_M" # Example model name for Ollama
#     provider_url: "http://localhost:11434" # Ollama typically runs here, GenAI expects host:port
#     # provider_api_key: "none" # Ollama usually doesn't require an API key; will check OLLAMA_HOST env var.
#
#   - alias: "cloud-openai-gpt4o" # Example for a cloud-hosted OpenAI model
#     provider: "openai" # Use 'openai' provider for GenAI's OpenAI integration
#     model: "gpt-4o"
#     # provider_url: "https://api.openai.com/v1" # Optional: GenAI's default for OpenAI is usually fine
#     # provider_api_key: "sk-yourOpenAIKeyHere" # Can be set per model, overrides OPENAI_API_KEY env var.
#
#   - alias: "openrouter-mistral-large" # Example for OpenRouter
#     provider: "openrouter"
#     model: "mistralai/mistral-large-latest" # Model identifier as used by OpenRouter
#     # provider_url: "https://openrouter.ai/api/v1" # This is fixed for OpenRouter and will be ignored if set
#     provider_api_key: "sk-or-yourOpenRouterApiKeyHere" # Specific API key for OpenRouter, overrides OPENROUTER_API_KEY env var.

# --- Results Input (replaces Message History) ---

# Path to a JSON file containing results data from a previous run to load as initial state.
# Example: "/path/to/your/message_history.json"
# Example: "C:\\Users\\<USER>\\Documents\\message_history.json"
# message_history_file: null

# --- Task & Prompting ---

# Additional detailed information about the task to be provided at the start of the initial prompt.
# Example: "Always respond in a formal tone."
# task_info: null

# Set the 'no_think' modes. This is a list of modes.
# Valid modes:
# - "none": Disables /no_think prefix entirely.
# - "all": Adds /no_think prefix to all LLM prompts (unless "none" is present).
# - "editing-planning": Adds /no_think for initial editing plan generation.
# - "editing-coding": Adds /no_think for specific code generation prompts during editing.
# - "research-auto-decision": Adds /no_think for the prompt asking LLM to decide if auto-research is needed.
# - "research-planning": Adds /no_think for initial research planning.
# - "research-searching": Adds /no_think for subsequent prompts in a research cycle.
# - "question": Adds /no_think for general question/answer prompts.
# - "summaries": Adds /no_think for prompts requesting summaries (e.g., plan summary, task summary).
# Example: ["editing-planning", "research-planning"]
# Default: [] (empty list, meaning /think prefix is used by default if not in list)
# no_think: []

# --- Output ---

# Output results as JSON to terminal (true/false).
# Default: false
# results_output: false

# Path to file to output JSON results.
# Example: "/path/to/your/results.json"
# results_output_file: null

# --- Other Settings ---

# Set the logging level.
# 1 = Limited logs.
# 2 = Default logging that provides a solid overview of what is happening and reads well.
# 3 = Extremely verbose logging, including raw LLM responses.
# Default: 2
# log_level: 2

# --- Interactive Mode ---

# Run LLEdit in an interactive TUI mode (true/false).
# Default: false
# interactive: false

# Enable or disable timestamps in logs.
# Default: true (timestamps are enabled)
# timestamps: true

# --- Auto-test Settings ---

# Command to run automatically after edits are applied.
# Example: "cargo test" or "npm run test"
# auto_test_command: null

# Enable the auto-test feature by default (true/false).
# Default: false
# auto_test_toggle: false

# --- Notification Settings ---

# Command to run for notifications. LLEdit will set LLEDIT_NOTIFICATION_TITLE and LLEDIT_NOTIFICATION_CONTENTS environment variables.
# Example for macOS: "osascript -e 'display notification \"${LLEDIT_NOTIFICATION_CONTENTS}\" with title \"${LLEDIT_NOTIFICATION_TITLE}\"'"
# Example for Linux (notify-send): "notify-send \"${LLEDIT_NOTIFICATION_TITLE}\" \"${LLEDIT_NOTIFICATION_CONTENTS}\""
# notification_command: null
```

### Environment Variables

Provider-specific API keys and base URLs can be set via environment variables:

*   `OPENAI_API_KEY`: API key for OpenAI.
*   `OPENAI_API_BASE`: Base URL for OpenAI (e.g., for proxies or compatible services).
*   Other providers might have similar `PROVIDER_API_KEY` and `PROVIDER_API_BASE` patterns if 
supported by them.

Environment variables are overridden by settings in the YAML file and by CLI flags.

## Usage

```bash
lledit [OPTIONS] "<USER_PROMPT>" <FILE_PATHS>...
```

### Arguments

*   `<USER_PROMPT>`: (Required) Your high-level instruction for the LLM (e.g., "Refactor this 
function to improve performance").
*   `<FILE_PATHS>...`: (Required) One or more paths to the files you want the LLM to process. If a 
path points to a non-existent file, the LLM can be instructed to create it using a `0-0` range.

### Options

| Flag                    | Short | YAML Key                | Env Var (Example)  | Description                                                                                                | Default             |
| ----------------------- | ----- | ----------------------- | ------------------ | ---------------------------------------------------------------------------------------------------------- | ------------------- |
| `--message <MESSAGE>`   | `-m`  | (CLI only)              |                    | The high-level instruction for the LLM. Optional in interactive mode.                                      |                     |
| `--default-model <ALIAS>`| `-M` | `default_model`         |                    | Specify the alias of the model configuration to use (from `models_list` in config).                        | (See config)        |
| `--input <JSON_STRING>` | `-i`  | `results_input`         |                    | JSON string containing results data from a previous run. Conflicts with `--input-file`.                    |                     |
| `--input-file <FILE_PATH>` | `-I` | `results_input_file`    |                    | Path to a JSON file containing results data from a previous run. Conflicts with `--input`.                 |                     |
| `--task-info <TEXT>`    | `-t`  | `task_info`             |                    | Additional detailed information about the task for the initial prompt.                                     |                     |
| `--output <MODE>`       | `-o`  | `results_output_mode`   |                    | Set results output mode to stdout: `none`, `simple`, `advanced`.                                           | `none`              |
| `--results-output-file <FILE_PATH>` | `-R` | `results_output_file` |         | Path to file to output JSON results (always `advanced` format).                                            |                     |
| `--log-level <LEVEL>`   | `-l`  | `log_level`             |                    | Set logging: 1=Limited, 2=Default/Info, 3=Verbose/Debug.                                                   | `2` (Info)          |
| `--no-think <MODES>`    | `-n`  | `no_think`              |                    | Space-separated list of 'no_think' modes. See below for details.                                           | (empty list)        |
| `--no-timestamps`       | `-T`  | `timestamps` (inverted) |                    | Disable timestamps in logs. (YAML `timestamps: false`)                                                       | Timestamps enabled  |
| `--interactive`         | `-i`  | `interactive`           |                    | Run in interactive TUI mode.                                                                               | `false`             |
| `--auto-test-command <COMMAND_STRING>` | `-a` | `auto_test_command` |         | Command to run automatically after edits are applied if auto-test is toggled on.                             |                     |
| `--auto-test-toggle`    | `-A`  | `auto_test_toggle`      |                    | Toggle the auto-test feature on.                                                                           | `false`             |
| `--notification-command <COMMAND_STRING>` | `-N` | `notification_command` |  | Command to execute for system notifications.                                                               |                     |

### Examples

**Basic Usage:**
```bash
lledit "Refactor the process_data function to use iterators more efficiently." 
src/data_processor.rs
```

**Using a specific provider and model, with task info:**
```bash
lledit \
    "Update the User model to include a 'last_login' field and adjust the login function." \
    models/user.py services/auth.py \
    --default-model cloud-openai-gpt4o \
    --task-info "The database is PostgreSQL. Ensure timestamps are timezone-aware."
```

**Loading message history from a file:**
```bash
lledit \
    "Based on our previous discussion, implement the caching layer." \
    src/cache.rs \
    --input-file previous_chat.json
```

**Creating a new file:**
```bash
lledit \
    "Create a new Python utility module 'string_utils.py' with a function to reverse a string." \
    src/string_utils.py 
    # The LLM should be prompted to use range 0-0 for src/string_utils.py if it doesn't exist.
```

**Using specific no_think modes:**
```bash
lledit \
    "Refactor this function." \
    src/complex_module.rs \
    --no-think "editing-planning editing-coding"
```

**Outputting results to a JSON file:**
```bash
lledit "Add docstrings to all public functions." src/lib.rs --results-output-file report.json
```

## Web Server / API

`lledit` includes an HTTP server that exposes an API for interacting with the application programmatically. By default, it runs on `http://localhost:11991`.

### Swagger UI

API documentation and a user interface for interacting with the API are available via Swagger UI. You can access it by navigating to the root URL of the API server in your browser:

*   [http://localhost:11991/](http://localhost:11991/)

This will redirect you to the Swagger UI page, where you can explore all available endpoints, view their request/response schemas, and try them out.

### Example API Requests

Here are a couple of examples using `curl`:

**1. Health Check:**
To check the status and uptime of the server:
```bash
curl http://localhost:11991/health-check
```
Expected response (example):
```json
{
  "status": "ok",
  "uptime": 123
}
```

**2. Submit Input:**
To submit a new task or input to `lledit`:
```bash
curl -X POST -H "Content-Type: application/json" \
     -d '{"input": "Refactor the main function in src/main.rs to improve readability"}' \
     http://localhost:11991/submit-input
```
This endpoint will return a `202 Accepted` status code if the input is successfully queued.
You can submit any command which you would use in the TUI such as `/add <file>`, `/drop <file>`, etc. 

For details on other endpoints and their specific request/response formats, please refer to the Swagger UI.

## How It Works: The Workflow

1.  **Configuration Loading:** `lledit` loads settings from CLI flags, 
`~/.lledit.config.yml`, and environment variables.
2.  **User Invocation:** You run `lledit` with a general instruction and paths to the files.
3.  **File Preparation:**
    *   The application reads the content of each specified existing file.
    *   It adds line numbers to the beginning of each line (e.g., `1 | fn main() {`).
4.  **Initial LLM Prompt (Identifying Edit Ranges):**
    *   `lledit` constructs a prompt for the LLM. This prompt includes:
        *   Your high-level instruction (and any `--task-info` or message history).
        *   The line-numbered content of all specified files.
        *   A specific instruction asking the LLM to first think about the plan, then return a list
of file names and line number ranges it intends to edit (or new files).
    *   Example request format for the LLM:
        ```
        Based on the user's request: "<Your high-level instruction>"
        And the following file(s) with line numbers:

        --- file: path/to/file1.rs ---
        1 | fn factorial(n: u32) -> u32 {
        ... | }
        --- file: path/to/new_file.py ---
        (This file does not exist yet, or is empty)

        Please specify the file and line number ranges you intend to edit.
        Format each segment strictly as follows:
        ```range
        path/to/your/file.ext
        START_LINE-END_LINE
        ```
        If you need to create a new file, use the new file path and the special range `0-0`.
        First think out loud about how you want to go about solving the users request, and then at
the end provide every range block you will need to edit.
        ```
    *   The LLM responds, ideally with blocks like:
        ```range
        path/to/file1.rs
        5-8
        ```
        ```range
        path/to/new_file.py
        0-0
        ```
5.  **Range Block Validation & Correction:**
    *   `lledit` parses these ````range ```` blocks.
    *   If any blocks are malformed (e.g., missing file path, invalid line numbers, file specified 
for edit doesn't exist, 0-0 used for existing file), `lledit` sends a new prompt to the LLM, 
showing the problematic blocks and asking it to provide a corrected list of all range blocks. This 
can repeat a few times.
6.  **Iterative LLM Prompts (Getting Specific Edits):**
    *   For each valid file and line range (or new file target), `lledit` performs a new, 
separate LLM interaction.
    *   It sends a prompt containing:
        *   The original high-level instruction (for context).
        *   The specific line-numbered content of the current range (or an indication it's a new 
file).
        *   An instruction asking the LLM to provide the *replacement code* for that range (or full
content for a new file), enclosed in a ````replace ```` block.
    *   Example request for a specific range:
        ```
        User's current overall goal: "<Your high-level instruction>"
        File: path/to/file1.rs
        Original lines to be edited (lines 5-8):
        ```
        5 |   if n == 0 {
        6 |     return 1
        7 |   } else {
        8 |     return n * factorial(n-1)
        ```
        Please provide the new code for lines 5-8. Respond *only* with the new code, enclosed in a 
single 'replace' code block...
        ```
7.  **Applying Edits:**
    *   The code from the ````replace ```` blocks is extracted.
    *   The changes are applied to the in-memory representation of the files.
    *   The modified files are written to disk.
8.  **Result Output (Optional):** If requested via `--output <mode>` or `--results-output-file`, a
summary of the conversation and edits is produced.
9.  **Notifications (Optional):** If a `notification_command` is configured, `lledit` will execute it at various stages of the process (task start, plan received, edits applied, auto-test results).

## Supported LLM Providers

`lledit` supports various LLM providers through its configuration system. Each provider might have different requirements for API keys, model identifiers, and URLs.

Generally non-local LLMs are only recommended for usage as the expert model due to editing/research flows requiring many inference calls to take advantage of prompt caching. This allows you to strike a great balance between using frontier models for solving hard problems, while taking advantage of local models to drastically lower the net cost by dealing with all retries/fixing code blocks/addressing simple errors after testing/research.

**Key Configuration Fields for `models_list` entries:**
*   `alias`: A short name you choose for this configuration (e.g., "my-local-llm", "gpt4o-cloud").
*   `provider`: The type of provider. See specific examples below.
*   `model`: The model identifier specific to the provider (e.g., "gpt-4o", "qwen/qwen2:72b-instruct").
*   `provider_url`: (Optional) The base URL for the provider's API. Behavior varies by provider.
*   `provider_api_key`: (Optional) An API key specific to this model entry. If provided, it overrides any relevant environment variables for this model. Setting this to the string `"none"` (case-insensitive) is equivalent to the key being absent (null), which means the system will then check environment variables.

**API Key Precedence for a given model:**
1.  `provider_api_key` within the specific `ModelConfigEntry` in `models_list`.
2.  Provider-specific environment variable (e.g., `OPENAI_API_KEY`, `OPENROUTER_API_KEY`, `ANTHROPIC_API_KEY`).

### 1. `openai-compatible`

*   **Description:** For any LLM service that exposes an OpenAI-compatible API.
*   **Provider Type:** `openai-compatible`
*   **Environment Variables:**
    *   `OPENAI_API_KEY` (for API key, if not in `ModelConfigEntry`)
    *   `OPENAI_API_BASE` (for base URL, if not in `ModelConfigEntry`)
*   **Example `models_list` entry:**
    ```yaml
    - alias: "custom-openai-server"
      provider: "openai-compatible"
      model: "custom-model-name"
      provider_url: "http://localhost:8080/v1/" # Must end with /v1/
      # provider_api_key: "your_custom_server_key_if_needed"
    ```

### 2. `openrouter`

*   **Description:** For accessing models via [OpenRouter.ai](https://openrouter.ai/).
*   **Provider Type:** `openrouter`
*   **Base URL:** Fixed to `https://openrouter.ai/api/v1/`. Any `provider_url` in the config will be ignored.
*   **Environment Variables:**
    *   `OPENROUTER_API_KEY` (if API key not in `ModelConfigEntry`)
*   **Example `models_list` entry:**
    ```yaml
    - alias: "deepseek-r1-0528"
      provider: "openrouter"
      model: "deepseek/deepseek-r1-0528:free" # OpenRouter model string
      provider_api_key: "yourOpenRouterKeyHere"
    ```

### 3. `openai

*   **Description:** For accessing official OpenAI models.
*   **Provider Type:** `openai`
*   **Environment Variables (picked up by GenAI library):**
    *   `OPENAI_API_KEY`
    *   `OPENAI_API_BASE` (optional, for proxies)
*   **Example `models_list` entry:**
    ```yaml
    - alias: "official-gpt4o"
      provider: "openai"
      model: "gpt-4o"
      # provider_api_key: "sk-yourOpenAIKeyFromModelEntry" # Overrides env
      # provider_url: "https://your-proxy.com/v1" # Optional
    ```

### 4. `ollama

*   **Description:** For interacting with models hosted by a local Ollama instance.
*   **Provider Type:** `ollama`
*   **Environment Variables (picked up by GenAI library):**
    *   `OLLAMA_HOST`: Base URL of your Ollama server (e.g., `http://localhost:11434`). GenAI expects this to be just `hostname:port` or a full URL. `lledit` will attempt to format it correctly if a full URL is provided in `provider_url`.
*   **Example `models_list` entry:**
    ```yaml
    - alias: "local-ollama-qwen2"
      provider: "ollama"
      model: "qwen/qwen2:7b-instruct-fp16" # Ollama model tag
      provider_url: "http://localhost:11434" # GenAI will use OLLAMA_HOST from this
      # provider_api_key: "none" # Typically not needed for Ollama
    ```

### 5. Other GenAI Supported Providers (e.g., Anthropic, Google Gemini)

*   **Description:** The `genai` library supports other providers like Anthropic and Google. You can use their respective provider names.
*   **Provider Type:** e.g., `anthropic`, `google`
*   **Environment Variables:** GenAI will look for standard environment variables like `ANTHROPIC_API_KEY`, `GOOGLE_API_KEY`.
*   **Example `models_list` entry (Anthropic Claude 3 Opus):**
    ```yaml
    - alias: "claude3-opus"
      provider: "anthropic" # Or whatever GenAI uses for Anthropic
      model: "claude-3-opus-20240229"
      # provider_api_key: "sk-ant-yourAnthropicKey"
      # provider_url: "https://api.anthropic.com" # If needed
    ```
    *Note: Refer to the `genai` library's documentation for the exact provider names and model identifiers it expects for providers other than OpenAI and Ollama.*

## Notifications

`lledit` can send system notifications at key points in its workflow if a `notification_command` is configured via the CLI flag or the YAML configuration file.

When the `notification_command` is executed, `lledit` sets two environment variables:

*   `LLEDIT_NOTIFICATION_TITLE`: The title for the notification.
*   `LLEDIT_NOTIFICATION_CONTENTS`: The main content/body of the notification.

Your specified command can then use these environment variables to display the notification.

**Recommendation:** For flexibility and to handle potential quoting issues with complex commands, it's highly recommended to use a shell script as your `notification_command`.

**Example `notification_command` in `~/.lledit.config.yml`:**
```yaml
notification_command: "/path/to/your/lledit_notify.sh"
# Or for direct command (ensure proper quoting for your shell):
# notification_command: "notify-send \"${LLEDIT_NOTIFICATION_TITLE}\" \"${LLEDIT_NOTIFICATION_CONTENTS}\""
```

**Example Bash Script (`/path/to/your/lledit_notify.sh`):**

Make sure this script is executable (`chmod +x /path/to/your/lledit_notify.sh`).

```bash
#!/bin/bash

# Script to handle lledit notifications

# Ensure environment variables are set
if [ -z "$LLEDIT_NOTIFICATION_TITLE" ] || [ -z "$LLEDIT_NOTIFICATION_CONTENTS" ]; then
  echo "Notification title or contents not set. Exiting."
  exit 1
fi

# --- Option 1: Generic notify-send (Common on many Linux desktops, including Hyprland with a notification daemon) ---
# This is often the simplest and works if a libnotify-compatible daemon is running (e.g., dunst, mako).
notify-send "$LLEDIT_NOTIFICATION_TITLE" "$LLEDIT_NOTIFICATION_CONTENTS"

# --- Option 2: GNOME Desktop ---
# notify-send usually works out of the box on GNOME.
# For more advanced notifications or if notify-send isn't preferred:
# gdbus call --session --dest org.freedesktop.Notifications --object-path /org/freedesktop/Notifications --method org.freedesktop.Notifications.Notify \
#   "lledit" 0 "dialog-information" "$LLEDIT_NOTIFICATION_TITLE" "$LLEDIT_NOTIFICATION_CONTENTS" "[]" "{}" 5000

# --- Option 3: KDE Plasma Desktop ---
# notify-send often works on KDE as well.
# Alternatively, kdialog can be used:
# kdialog --title "$LLEDIT_NOTIFICATION_TITLE" --passivepopup "$LLEDIT_NOTIFICATION_CONTENTS" 5
# The '5' at the end is the timeout in seconds.

# --- Option 4: macOS ---
# osascript -e "display notification \"${LLEDIT_NOTIFICATION_CONTENTS}\" with title \"${LLEDIT_NOTIFICATION_TITLE}\""

# Choose and uncomment one of the options above, or add your custom notification command.
```

**Notification Events:**

Notifications are triggered for:

1.  **Task Started:** When a new task begins processing.
    *   Title: `LLEdit Task Started`
    *   Contents: First 50 characters of the user's prompt.
2.  **Plan Received:** After the LLM provides its initial plan/summary.
    *   Title: `📝 LLEdit Plan: X Edit(s)`
    *   Contents: First 50 characters of the LLM's plan summary.
3.  **Edits Applied:** When file edits have been successfully written to disk.
    *   Title: `💾 LLEdit Task Finished [MM:SS]` (includes task duration)
    *   Contents: `Applied X edits to Y files.`
4.  **Auto-test Success:** If the auto-test command runs and succeeds.
    *   Title: `✅ LLEdit Auto-test Success`
    *   Contents: `The auto-test command completed successfully.`
5.  **Auto-test Failed:** If the auto-test command runs and fails.
    *   Title: `❌ LLEdit Auto-test Failed`
    *   Contents: `The auto-test command failed, starting new task asking LLM to fix issues from output.`

## Interactive Mode Commands

The following commands can be used in the input box during interactive mode:

| Command                         | Description                                                                                                |
| ------------------------------- | ---------------------------------------------------------------------------------------------------------- |
| `/add <path>...`                | Adds file(s) or files in folder(s) (recursive up to 5 levels) to context.                                |
| `/auto-test-command <command>`  | Sets the command for auto-testing after edits.                                                             |
| `/auto-test-toggle`             | Toggles the auto-test feature on/off.                                                                      |
| `/cd <directory>`               | Changes the current working directory.                                                                     |
| `/drop <path>`                  | Drops a file from the context.                                                                             |
| `/drop-all`                     | Drops all files from the context.                                                                          |
| `/exit`                         | Exits the application.                                                                                     |
| `/help`                         | Shows this help message.                                                                                   |
| `/model <alias>`                | Sets the default model alias to use (e.g., local-qwen3-32b). This refers to an alias in your `models_list`. |
| `/provider <name>`              | (DEPRECATED - manage via `models_list` and `default_model` alias)                                          |
| `/provider-url <url>`           | (DEPRECATED - manage via `models_list` and `default_model` alias)                                          |
| `/pwd`                          | Shows the current working directory.                                                                       |
| `/quit`                         | Exits the application.                                                                                     |
| `/research <task description>`  | Starts a research flow to find relevant files for the given task using LLM-guided shell commands (`grep`, `find`, `head`, `ls`). `lledit` will attempt to use `rg` if available when `grep` is requested. |
| `/run <command>`                | Executes a shell command and optionally adds output to task info.                                          |
| `/no-think <modes...>`          | Sets 'no_think' modes as a space-separated list (e.g., `/no-think editing-planning research`). Use `/no-think none` to clear. See main README section for all modes. |
| `/task-info`                    | Displays the current task information.                                                                     |
| `/task-info-add <text>`         | Adds text to the task information.                                                                         |
| `/task-info-clear`              | Clears the task information.                                                                               |
| `/task-info-set <text>`         | Sets the task information.                                                                                 |
| `/notification-command <command>` | Sets the command for system notifications.                                                                 |

### `no_think` Modes Explained

The `--no-think` CLI option and `no_think` YAML setting control whether `lledit` prepends `/no_think ` or `/think ` to prompts sent to the LLM. This can influence the LLM's verbosity and reasoning process. You can specify multiple modes as a space-separated list (CLI) or a YAML list.

*   **`none`**: If present, no prefix (`/no_think ` or `/think `) is added to any prompt. This overrides all other modes.
*   **`all`**: If present (and `none` is not), `/no_think ` is added to all prompts.
*   If neither `none` nor `all` is present, `lledit` checks the specific modes:
    *   **`editing-planning`**: Adds `/no_think ` to the initial prompt asking the LLM to plan code edits. Otherwise, `/think ` is added.
    *   **`editing-coding`**: Adds `/no_think ` to prompts asking the LLM to generate specific code replacements. Otherwise, `/think ` is added.
    *   **`research-auto-decision`**: Adds `/no_think ` to the prompt where the LLM decides if automated research is needed. Otherwise, `/think ` is added.
    *   **`research-planning`**: Adds `/no_think ` to the initial prompt for planning a research task. Otherwise, `/think ` is added.
    *   **`research-searching`**: Adds `/no_think ` to subsequent prompts during a research cycle (e.g., interpreting search results). Otherwise, `/think ` is added.
    *   **`question`**: Adds `/no_think ` to general question/answer prompts. Otherwise, `/think ` is added.
    *   **`summaries`**: Adds `/no_think ` to prompts requesting summaries (e.g., plan summary, task summary). Otherwise, `/think ` is added.

If a specific context (e.g., `editing-planning`) is *not* in your `no_think` list (and `all` and `none` are also not active for it), then `/think ` will be prepended to prompts for that context. The default behavior (empty `no_think` list) is that all prompts get the `/think ` prefix.

