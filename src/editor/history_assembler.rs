use crate::files::ordered_files::OrderedFiles;
use crate::llm::ChatMessage;
use crate::task::Task;
use std::collections::HashMap;
use std::path::PathBuf;

// Helper function to assemble messages without the initial files message.
pub(crate) fn assemble_llm_messages_without_files(
    all_tasks: &[Task],
    current_task_messages: &[ChatMessage],
) -> Vec<ChatMessage> {
    const FILE_CONTEXT_MESSAGE_PREFIX: &str = "The following files are relevant to the task.";

    all_tasks
        .iter()
        .flat_map(|task| task.messages.iter())
        .chain(current_task_messages.iter())
        .filter(|message| !message.content.starts_with(FILE_CONTEXT_MESSAGE_PREFIX))
        .cloned()
        .collect()
}

/// Assembles the complete list of ChatMessages to be sent to the LLM.
///
/// This function ensures that:
/// 1. The most up-to-date files message (if any files are in context) is always the first message.
/// 2. Messages from all previous tasks are included.
/// 3. A pending pruned summary message (if any) is inserted appropriately.
/// 4. The current task's specific messages (like user instruction) are appended.
///
/// Returns a tuple:
/// - `Vec<ChatMessage>`: The assembled list of messages for the LLM.
/// - `HashMap<PathBuf, String>`: The map of file paths to their content that was included
///   in the generated files message. This is useful for context outside the LLM call.
pub fn assemble_llm_messages(
    ordered_files: &mut OrderedFiles, // Mutable to update last_sent_content_hashes
    pruned_summaries: &[ChatMessage],
    all_active_tasks: &[Task], // All active (non-pruned) tasks in history
    current_task_messages: &[ChatMessage], // Messages for the current task being processed
    current_display_root: &PathBuf,   // New parameter
) -> (Vec<ChatMessage>, HashMap<PathBuf, String>) {
    let mut final_messages: Vec<ChatMessage> = Vec::new();

    // 1. Generate and add the files message.
    let (files_chat_msg_option, included_content_map) =
        ordered_files.get_files_for_prompt_message(current_display_root);
    if let Some(files_msg) = files_chat_msg_option {
        final_messages.push(files_msg);
    }

    // 2. Add pruned summaries.
    final_messages.extend_from_slice(pruned_summaries);

    // 3. Add messages from active tasks and the current in-progress task.
    let mut other_messages =
        assemble_llm_messages_without_files(all_active_tasks, current_task_messages);
    final_messages.append(&mut other_messages);

    (final_messages, included_content_map)
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::files::ordered_files::OrderedFiles;
    use crate::llm::{ChatMessage, ChatRole, MessageType};
    use crate::task::Task;
    use std::path::PathBuf;
    use tempfile::tempdir;

    async fn create_temp_file_for_test(
        dir: &tempfile::TempDir,
        name: &str,
        content: &str,
    ) -> PathBuf {
        let path = dir.path().join(name);
        tokio::fs::write(&path, content).await.unwrap();
        dunce::canonicalize(path).unwrap()
    }

    #[tokio::test]
    async fn test_assemble_llm_messages_no_history_no_files() {
        let mut ordered_files = OrderedFiles::new();
        let all_tasks: Vec<Task> = Vec::new();
        let current_task_messages = vec![ChatMessage {
            role: ChatRole::User,
            content: "User instruction".to_string(),
            message_type: MessageType::Text,
        }];
        let test_current_dir = PathBuf::from("."); // Dummy current dir for test

        let (messages, included_map) = assemble_llm_messages(
            &mut ordered_files,
            &[], // no pruned summaries
            &all_tasks,
            &current_task_messages,
            &test_current_dir,
        );

        assert_eq!(messages.len(), 1);
        assert_eq!(messages[0].content, "User instruction");
        assert!(included_map.is_empty());
    }

    #[tokio::test]
    async fn test_assemble_llm_messages_with_files_first_task() {
        let mut ordered_files = OrderedFiles::new();
        let temp_dir = tempdir().unwrap();
        let file_path = create_temp_file_for_test(&temp_dir, "test.txt", "content").await;
        ordered_files.add_user_file(&file_path).await.unwrap();

        let all_tasks: Vec<Task> = Vec::new();
        let current_task_messages = vec![ChatMessage {
            role: ChatRole::User,
            content: "User instruction".to_string(),
            message_type: MessageType::Text,
        }];
        let test_current_dir = temp_dir.path().to_path_buf();

        let (messages, included_map) = assemble_llm_messages(
            &mut ordered_files,
            &[], // no pruned summaries
            &all_tasks,
            &current_task_messages,
            &test_current_dir,
        );

        assert_eq!(messages.len(), 2);
        assert!(messages[0].content.contains(
            "The following files are relevant to the task. Please review their content carefully:"
        ));
        assert!(messages[0].content.contains("test.txt"));
        assert_eq!(messages[1].content, "User instruction");
        assert_eq!(included_map.len(), 1);
        assert_eq!(included_map.get(&file_path).unwrap(), "content");
    }

    #[tokio::test]
    async fn test_assemble_llm_messages_with_history_and_files() {
        let mut ordered_files = OrderedFiles::new();
        let temp_dir = tempdir().unwrap();
        let file_path = create_temp_file_for_test(&temp_dir, "test.txt", "content v1").await;
        ordered_files.add_user_file(&file_path).await.unwrap();

        let mut task1 = Task::new("Old prompt".to_string(), None);
        task1.add_message(ChatMessage {
            role: ChatRole::User,
            content: "Old user msg".to_string(),
            message_type: MessageType::Text,
        });
        task1.add_message(ChatMessage {
            role: ChatRole::Assistant,
            content: "Old assistant msg".to_string(),
            message_type: MessageType::Text,
        });
        let all_tasks = vec![task1];

        let current_task_messages = vec![ChatMessage {
            role: ChatRole::User,
            content: "New instruction".to_string(),
            message_type: MessageType::Text,
        }];

        let test_current_dir = temp_dir.path().to_path_buf();

        // First call (files message will now have the constant intro)
        let (messages1, _map1) = assemble_llm_messages(
            &mut ordered_files,
            &[], // no pruned summaries
            &all_tasks,
            &current_task_messages,
            &test_current_dir,
        );
        assert_eq!(messages1.len(), 1 + 2 + 1); // Files, Task1 (2 msgs), Current (1 msg)
        assert!(messages1[0].content.contains(
            "The following files are relevant to the task. Please review their content carefully:"
        ));

        // Simulate file update
        tokio::fs::write(&file_path, "content v2").await.unwrap();
        ordered_files
            .refresh_single_file_from_filesystem(&file_path)
            .await
            .unwrap();

        // Second call (files message should be "updated/new files")
        let (messages2, map2) = assemble_llm_messages(
            &mut ordered_files,
            &[], // no pruned summaries
            &all_tasks,
            &current_task_messages,
            &test_current_dir,
        );
        assert_eq!(messages2.len(), 1 + 2 + 1);
        assert!(messages2[0].content.contains(
            "The following files are relevant to the task. Please review their content carefully:"
        ));
        assert!(messages2[0].content.contains("test.txt"));
        assert_eq!(messages2[1].content, "Old user msg");
        assert_eq!(messages2[2].content, "Old assistant msg");
        assert_eq!(messages2[3].content, "New instruction");
        assert_eq!(map2.len(), 1);
        assert_eq!(map2.get(&file_path).unwrap(), "content v2");
    }

    #[tokio::test]
    async fn test_assemble_llm_messages_with_pruned_summary_in_current_task() {
        let mut ordered_files = OrderedFiles::new();
        let all_tasks: Vec<Task> = Vec::new(); // No prior tasks

        let current_task_messages = vec![
            ChatMessage { // Pruned summary for the current task
                role: ChatRole::System,
                content: "Summary of previously completed and pruned tasks:\n---\nTask #1: Did stuff.\n---".to_string(),
                message_type: MessageType::Text,
            },
            ChatMessage {
                role: ChatRole::User,
                content: "User instruction for current task".to_string(),
                message_type: MessageType::Text,
            },
        ];

        let test_current_dir = PathBuf::from(".");

        let (messages, included_map) = assemble_llm_messages(
            &mut ordered_files,
            &[], // no pruned summaries
            &all_tasks,
            &current_task_messages,
            &test_current_dir,
        );

        assert_eq!(messages.len(), 2); // No files message, then 2 current_task_messages
        assert_eq!(
            messages[0].content,
            "Summary of previously completed and pruned tasks:\n---\nTask #1: Did stuff.\n---"
        );
        assert_eq!(messages[1].content, "User instruction for current task");
        assert!(included_map.is_empty());

        // Now add a file
        let temp_dir = tempdir().unwrap();
        let file_path = create_temp_file_for_test(&temp_dir, "test.txt", "content").await;
        ordered_files.add_user_file(&file_path).await.unwrap();
        let test_current_dir_with_file = temp_dir.path().to_path_buf();

        let (messages_with_file, _map_with_file) = assemble_llm_messages(
            &mut ordered_files,
            &[], // no pruned summaries
            &all_tasks,
            &current_task_messages,
            &test_current_dir_with_file,
        );

        assert_eq!(messages_with_file.len(), 3); // Files message, then 2 current_task_messages
        assert!(messages_with_file[0].content.contains(
            "The following files are relevant to the task. Please review their content carefully:"
        ));
        assert!(messages_with_file[0].content.contains("test.txt"));
        assert_eq!(
            messages_with_file[1].content,
            "Summary of previously completed and pruned tasks:\n---\nTask #1: Did stuff.\n---"
        );
    }
}