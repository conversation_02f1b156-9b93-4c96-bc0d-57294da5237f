use crate::config::AppConfig;
use crate::editor::types::ProposedEdit;
use crate::files::file_handler::apply_edits_to_lines; // Corrected path, LabeledFile removed
use crate::files::ordered_files::OrderedFiles; // Added for OrderedFiles
use crate::result::Result as AppResult;
use log::error;
use std::collections::HashMap;
use std::env;
use std::path::PathBuf; // For current_dir

// Moved from old src/editor.rs
// Renamed to be pub(crate)
#[allow(clippy::too_many_arguments)] // Keep original allow if present
pub(crate) async fn _apply_all_proposed_edits(
    app_config: &AppConfig,
    proposed_edits_for_apply: &[ProposedEdit],
    ordered_files: &OrderedFiles, // Changed to OrderedFiles (read-only for this version)
    result_handler: &mut AppResult, // Renamed ResultHandler
) -> Result<(), Box<dyn std::error::Error>> {
    if proposed_edits_for_apply.is_empty() {
        // Level 2: Inform if no edits to apply
        log::debug!("No specific code edits were successfully retrieved or parsed to be applied.");
        log::trace!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"); // Level 3
        return Ok(());
    }

    // Print separator line if logging is not Off (interactive mode is always true)
    if app_config.log_level != crate::logger::LogLevel::Off {
        // Condition simplified
        log::debug!("-----------------------------------------------------------------------");
    }
    // Level 1: Announce start of applying edits
    log::info!("Applying Edits...");

    let current_dir = env::current_dir().unwrap_or_else(|_| PathBuf::from("."));
    let mut edits_by_file: HashMap<PathBuf, Vec<ProposedEdit>> = HashMap::new();
    for pe in proposed_edits_for_apply {
        edits_by_file
            .entry(pe.target.file_path.clone())
            .or_default()
            .push(pe.clone());
    }

    for (file_path, edits_for_this_file) in edits_by_file {
        let display_path = file_path.strip_prefix(&current_dir).unwrap_or(&file_path);
        if let Some(labeled_file) = ordered_files.get_labeled_file(&file_path) {
            // Use OrderedFiles
            let mut current_lines = labeled_file.get_original_lines().clone();

            match apply_edits_to_lines(&mut current_lines, &edits_for_this_file) {
                Ok(_lines_inserted_count) => {
                    let new_content = current_lines.join("\n");
                    match tokio::fs::write(&file_path, new_content).await {
                        Ok(_) => {
                            let is_new_file_creation = edits_for_this_file
                                .iter()
                                .any(|pe| pe.target.start_line == 0 && pe.target.end_line == 0);

                            let file_original_lines_in_ranges: usize = edits_for_this_file
                                .iter()
                                .map(|pe| {
                                    if pe.target.start_line == 0 && pe.target.end_line == 0 {
                                        0 // New file creation doesn't "edit" existing lines
                                    } else {
                                        pe.target.end_line - pe.target.start_line + 1
                                    }
                                })
                                .sum();

                            let file_new_lines_inserted: usize = edits_for_this_file
                                .iter()
                                .map(|pe| pe.new_code.lines().count())
                                .sum();

                            // Level 2: Summary of changes for each file
                            if is_new_file_creation {
                                log::debug!(
                                    "{} - new file created with {} lines",
                                    display_path.display(),
                                    file_new_lines_inserted
                                );
                            } else {
                                if file_new_lines_inserted > file_original_lines_in_ranges {
                                    let net_new_lines =
                                        file_new_lines_inserted - file_original_lines_in_ranges;
                                    log::debug!(
                                        "{} - {} lines edited | {} new lines added",
                                        display_path.display(),
                                        file_original_lines_in_ranges,
                                        net_new_lines
                                    );
                                } else {
                                    log::debug!(
                                        "{} - {} lines edited",
                                        display_path.display(),
                                        file_original_lines_in_ranges
                                    );
                                }
                            }

                            for applied_edit in edits_for_this_file {
                                result_handler.add_file_edit(
                                    applied_edit.target.file_path.clone(),
                                    applied_edit.target.start_line,
                                    applied_edit.target.end_line,
                                    applied_edit.new_code.clone(),
                                );
                            }
                        }
                        Err(e) => {
                            error!(
                                "Error writing changes to file {}: {}",
                                display_path.display(),
                                e
                            );
                        }
                    }
                }
                Err(e) => {
                    error!("Error applying edits to {}: {}", display_path.display(), e);
                }
            }
        } else {
            error!(
                "Internal error: Could not find LabeledFile for path {}",
                display_path.display()
            );
        }
    }
    log::trace!("--- Edits Applied ---"); // Level 3
    log::trace!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"); // Level 3
    Ok(())
}

// _apply_all_proposed_edits function removed.

// Moved from old src/editor.rs
// Renamed and made public
#[allow(clippy::too_many_arguments)] // Keep original allow if present
pub async fn apply_and_write_edits_interactive(
    _app_config: &AppConfig,
    proposed_edits_for_apply: &[ProposedEdit],
    ordered_files: &mut OrderedFiles, // Changed to &mut OrderedFiles
    result_handler: &mut AppResult,
) -> Result<(), Box<dyn std::error::Error>> {
    if proposed_edits_for_apply.is_empty() {
        log::debug!("No specific code edits were successfully retrieved or parsed to be applied.");
        return Ok(());
    }
    log::debug!("{}", "-".repeat(70));
    log::info!("Applying Edits...");

    let current_dir = env::current_dir().unwrap_or_else(|_| PathBuf::from("."));
    let mut edits_by_file: HashMap<PathBuf, Vec<ProposedEdit>> = HashMap::new();
    for pe in proposed_edits_for_apply {
        edits_by_file
            .entry(pe.target.file_path.clone())
            .or_default()
            .push(pe.clone());
    }

    for (file_path, edits_for_this_file) in edits_by_file {
        let display_path = file_path.strip_prefix(&current_dir).unwrap_or(&file_path);
        let mut current_lines = if let Some(labeled_file) =
            ordered_files.get_labeled_file(&file_path)
        {
            // Use OrderedFiles
            labeled_file.get_original_lines().clone()
        } else {
            if edits_for_this_file
                .iter()
                .any(|pe| pe.target.start_line == 0 && pe.target.end_line == 0)
            {
                Vec::new()
            } else {
                error!(
                    "Cannot apply edits to file {}: file not found in state and the edit does not indicate a new file",
                    display_path.display()
                );
                continue;
            }
        };

        match apply_edits_to_lines(&mut current_lines, &edits_for_this_file) {
            Ok(_lines_inserted_count) => {
                let new_content_string = current_lines.join("\n");
                match tokio::fs::write(&file_path, &new_content_string).await {
                    Ok(_) => {
                        // Update OrderedFiles instead of direct map insert
                        if let Err(e) = ordered_files
                            .update_file_after_edit(&file_path, &new_content_string)
                            .await
                        {
                            error!(
                                "Failed to update OrderedFiles after edit for {}: {}",
                                display_path.display(),
                                e
                            );
                            // Continue to log success for result_handler, but this is an inconsistency.
                        }

                        log::debug!(
                            "💾 Successfully applied {} edits to {}",
                            edits_for_this_file.len(),
                            display_path.display()
                        );
                        for applied_edit in edits_for_this_file {
                            result_handler.add_file_edit(
                                applied_edit.target.file_path.clone(),
                                applied_edit.target.start_line,
                                applied_edit.target.end_line,
                                applied_edit.new_code.clone(),
                            );
                        }
                    }
                    Err(e) => {
                        error!(
                            "Error writing changes to file {}: {}",
                            display_path.display(),
                            e
                        );
                    }
                }
            }
            Err(e) => {
                error!(
                    "Error applying edits to lines for {}: {}",
                    display_path.display(),
                    e
                );
            }
        }
    }
    log::trace!("--- Edits Applied ---");
    Ok(())
}
