use crate::block_parsing::processor::{
    process_llm_response_with_blocks, BlockCount, BlockExpectation,
};
use crate::block_parsing::range_blocks::RangeBlockParser;
use crate::block_parsing::research_block::ResearchBlockParser; // Added import for research
use crate::block_parsing::summary_blocks::SummaryBlockParser; // Added import for summary
use crate::block_parsing::traits::ParsableBlock;
use crate::config::AppConfig;
use crate::editor::types::EditTarget;
// use crate::files::file_handler::LabeledFile; // Corrected path - LabeledFile is unused here
use crate::llm::client::chat_inference;
use crate::llm::client::temporal_chat_inference;
use crate::llm::{ChatMessage, ChatRole, LLMClient, MessageType};
use crate::prompt_builder::{construct_editor_plan_prompt, construct_plan_summary_prompt}; // Added construct_plan_summary_prompt
                                                                                          // LLMSpinner removed
use super::types::ProcessingSignal; // EditTask removed
use crate::notifications::{execute_notification_command, truncate_with_ellipsis}; // Added for notifications
use log::{debug, error, trace, warn};
use std::collections::{HashMap, HashSet, VecDeque};
use std::path::PathBuf;
use std::sync::Arc; // Added import
use tokio::sync::mpsc;

#[allow(clippy::too_many_arguments)] // Keep original allow if present
pub(crate) async fn _handle_get_initial_plan(
    app_config: &AppConfig,
    llm_instance: Arc<dyn LLMClient>,            // Changed to Arc
    historical_context: &[ChatMessage],          // Added historical_context
    conversation_log: &mut Vec<ChatMessage>,     // This is messages_for_current_task
    _result_handler: &mut crate::result::Result, // Changed ResultHandler to AppResult & Prefixed
    ordered_files: &mut crate::files::ordered_files::OrderedFiles, // Changed to &mut OrderedFiles
    instruction_message_content: &str,           // Content of the user's instruction message
    tasks_to_process: &mut VecDeque<EditTarget>,
    processing_tx_opt: Option<&mpsc::Sender<ProcessingSignal>>,
    initial_plan_response_text_override: Option<String>,
    research_option_available: bool, // New parameter
) -> Result<Option<String>, String> {
    if initial_plan_response_text_override.is_none() {
        if let Some(tx) = processing_tx_opt {
            if tx
                .send(ProcessingSignal::UpdateProgress(0.0))
                .await
                .is_err()
            {
                warn!("Failed to send initial progress update (0.0) from _handle_get_initial_plan");
            }
        }
    }

    let assistant_message_content: String;
    let history_for_plan_parsing_retry: Vec<ChatMessage>; // Context for process_llm_response_with_blocks

    if let Some(override_text) = initial_plan_response_text_override {
        // Use the provided override text (e.g., from expert model's planning mode)
        // The conversation_log should already contain the expert's interaction.
        assistant_message_content = override_text;
        // For parsing an expert's plan, the "history" for the default LLM to retry parsing
        // is the main historical_context plus the user's instruction for this task.
        let mut temp_context = historical_context.to_vec();
        temp_context.push(ChatMessage {
            role: ChatRole::User,
            content: instruction_message_content.to_string(),
            message_type: MessageType::Text,
        });
        history_for_plan_parsing_retry = temp_context;
    } else {
        // Default LLM planning path
        // The pre-planning research decision step that was here has been removed.
        // The decision to research before planning is now handled in the main `run_edit_cycle` loop.

        // Main planning
        // Expectations for the prompt (what LLM is told to produce)
        let main_planning_prompt_expectations = vec![
            BlockExpectation {
                // Only range blocks
                parser: Box::new(RangeBlockParser),
                expected_count: BlockCount::Any,
            },
            // Optionally, include SummaryBlockParser if summaries are still desired here
            // BlockExpectation {
            //     parser: Box::new(SummaryBlockParser),
            //     expected_count: BlockCount::Optional,
            // },
        ];

        let llm_instruction_prompt_content = construct_editor_plan_prompt(
            app_config,
            instruction_message_content,
            app_config.task_info.as_deref(), // Pass task_info
            &ordered_files
                .get_all_labeled_files_map()
                .values()
                .cloned()
                .collect::<Vec<_>>(), // Provide LabeledFile slice
            &main_planning_prompt_expectations, // Pass expectations for prompt construction
            research_option_available,       // Pass the missing boolean argument
        );

        if let Some(tx) = processing_tx_opt.as_ref() {
            if tx
                .send(ProcessingSignal::UpdateProgress(0.05))
                .await
                .is_err()
            {
                warn!("Failed to send progress update (0.05) before main planning LLM call");
            }
        }

        let mut messages_for_default_llm_plan_call = historical_context.to_vec(); // Renamed for clarity
                                                                                  // conversation_log (which is current_task_specific_messages here) is already part of historical_context
                                                                                  // as assembled by run_edit_cycle. We only need to add the new user prompt for this specific call.

        let user_main_planning_prompt_message = ChatMessage {
            role: ChatRole::User,
            content: llm_instruction_prompt_content.clone(),
            message_type: MessageType::Text,
        };
        messages_for_default_llm_plan_call.push(user_main_planning_prompt_message.clone());

        debug!("Requesting editing plan from LLM...");
        match chat_inference(llm_instance.as_ref(), &messages_for_default_llm_plan_call).await {
            Ok(main_plan_response_text) => {
                conversation_log.push(user_main_planning_prompt_message); // Add user prompt to task's conversation
                assistant_message_content = main_plan_response_text.clone();
                let assistant_main_plan_response_message = ChatMessage {
                    role: ChatRole::Assistant,
                    content: assistant_message_content.clone(),
                    message_type: MessageType::Text,
                };
                conversation_log.push(assistant_main_plan_response_message); // Add assistant response to task's conversation
            }
            Err(e) => {
                error!("Error sending initial prompt to LLM: {}", e);
                return Err(format!("Error sending initial prompt to LLM: {}", e));
            }
        }
        history_for_plan_parsing_retry = messages_for_default_llm_plan_call; // This was the actual context sent
    }

    // Common processing logic for the plan response (either from override or new LLM call)
    log::trace!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    log::trace!(
        "Raw LLM Response for Edit Targets (Initial Plan):\n<<<ResponseStart>>>\n{}\n<<<ResponseEnd>>>",
        assistant_message_content
    );
    log::trace!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

    let mut parsed_targets_from_llm: Vec<EditTarget> = Vec::new();
    let mut normalized_targets_for_processing: Vec<EditTarget> = Vec::new();
    // combined_research_content is not expected from the main planning step anymore.

    // Expectations for parsing the main plan response.
    // These depend on whether research is an option for the LLM.
    let mut main_planning_parsing_expectations = vec![BlockExpectation {
        parser: Box::new(RangeBlockParser),
        expected_count: BlockCount::Any,
    }];

    if research_option_available {
        main_planning_parsing_expectations.push(BlockExpectation {
            parser: Box::new(ResearchBlockParser),
            expected_count: BlockCount::Optional, // LLM can EITHER provide ranges OR research
        });
    }
    // Optionally, include SummaryBlockParser if summaries are still desired here
    // main_planning_parsing_expectations.push(BlockExpectation {
    //     parser: Box::new(SummaryBlockParser),
    //     expected_count: BlockCount::Optional,
    // });

    let mut temp_log_for_block_processing = Vec::new();
    match process_llm_response_with_blocks(
        &assistant_message_content,
        &main_planning_parsing_expectations, // Use dynamically built expectations
        instruction_message_content, // This is the content of user_main_planning_prompt_message
        &ordered_files
            .get_all_labeled_files_map()
            .values()
            .cloned()
            .collect::<Vec<_>>(), // Provide LabeledFile slice
        llm_instance.clone(),        // Clone Arc for this call
        &history_for_plan_parsing_retry, // Use the correctly determined context
        &mut temp_log_for_block_processing, // Pass a new, temporary log
        app_config,
    )
    .await
    {
        Ok(processed_output) => {
            // Check for research block first, if it was an option
            if research_option_available {
                if let Some(research_raw_blocks) = processed_output
                    .successfully_parsed_blocks
                    .get(&ResearchBlockParser.id())
                {
                    if let Some(raw_block) = research_raw_blocks.first() {
                        match ResearchBlockParser.parse_to_string(raw_block) {
                            Ok(research_content) if !research_content.is_empty() => {
                                log::info!(
                                    "Standard planner LLM requested further research via a research block."
                                );
                                conversation_log.push(ChatMessage {
                                    role: ChatRole::System,
                                    content: "Standard planner LLM requested further research. Proceeding to research phase.".to_string(),
                                    message_type: MessageType::Text,
                                });
                                return Ok(Some(research_content));
                            }
                            Ok(_) => {
                                trace!("Planner LLM provided an empty research block.");
                            }
                            Err(e) => {
                                warn!("Failed to parse content of validated research block from planner: {}", e);
                            }
                        }
                    }
                }
            }

            // If no research block was processed and returned, proceed with range blocks
            if let Some(range_raw_blocks) = processed_output
                .successfully_parsed_blocks
                .get(&RangeBlockParser.id())
            {
                let range_parser = RangeBlockParser;
                for raw_block in range_raw_blocks {
                    match range_parser.parse_to_edit_target(raw_block, &ordered_files.get_all_labeled_files_map().values().cloned().collect::<Vec<_>>()) // Provide LabeledFile slice
                    {
                        Ok(targets_from_block) => {
                            parsed_targets_from_llm.extend(targets_from_block);
                        }
                        Err(e) => warn!(
                            "Failed to parse validated range block content from main plan: {}. Block: {:?}",
                            e, raw_block
                        ),
                    }
                }
            }

            // Normalization step for range targets (remains the same)
            let current_dir_for_norm = std::env::current_dir().map_err(|e| {
                format!(
                    "Failed to get current directory for path normalization: {}",
                    e
                )
            })?;

            for target_from_llm in parsed_targets_from_llm {
                let mut current_target = target_from_llm.clone();
                let resolved_path = if current_target.file_path.is_absolute() {
                    current_target.file_path.clone()
                } else {
                    current_dir_for_norm.join(&current_target.file_path)
                };

                if current_target.start_line == 0 && current_target.end_line == 0 {
                    current_target.file_path = resolved_path;
                } else {
                    match dunce::canonicalize(&resolved_path) {
                        Ok(canonical_path) => {
                            current_target.file_path = canonical_path;
                        }
                        Err(e) => {
                            warn!(
                                "Failed to canonicalize path for existing file target '{}' (resolved: {}): {}. Skipping this target.",
                                target_from_llm.file_path.display(),
                                resolved_path.display(),
                                e
                            );
                            continue;
                        }
                    }
                }
                normalized_targets_for_processing.push(current_target);
            }

            if !processed_output.remaining_raw_blocks.is_empty() {
                trace!(
                    "{} unclaimed raw blocks found in LLM response after processing initial plan:",
                    processed_output.remaining_raw_blocks.len()
                );
                for (i, block) in processed_output.remaining_raw_blocks.iter().enumerate() {
                    trace!(
                        "  Unclaimed block #{}: Keyword='{}', Content='{}...'",
                        i + 1,
                        block.keyword,
                        block
                            .content_after_keyword
                            .chars()
                            .take(50)
                            .collect::<String>()
                    );
                }
            }
        }
        Err(e) => {
            error!(
                "Error processing LLM response blocks for initial plan: {}",
                e
            );
            // Return early if block processing itself fails critically.
            return Err(format!("Error processing LLM response blocks: {}", e));
        }
    }

    // If research content was found by an expert override, it would have been returned earlier.
    // The default planning path now handles research decision before this point.
    // So, combined_research_content should be None here if we reached this far in default planning.
    // If it's an expert plan override that contained research, it should have been handled by try_run_expert_flow.

    // --- Deduplicate and Merge EditTargets ---
    let mut final_processed_targets: Vec<EditTarget> = Vec::new();
    let mut grouped_targets: HashMap<PathBuf, Vec<EditTarget>> = HashMap::new();
    for target in normalized_targets_for_processing {
        grouped_targets
            .entry(target.file_path.clone())
            .or_default()
            .push(target);
    }

    for (path, targets_for_path) in grouped_targets {
        let mut path_final_targets: Vec<EditTarget> = Vec::new();
        let zero_zero_targets: Vec<EditTarget> = targets_for_path
            .iter()
            .filter(|t| t.start_line == 0 && t.end_line == 0)
            .cloned()
            .collect();
        let mut non_zero_targets: Vec<EditTarget> = targets_for_path
            .iter()
            .filter(|t| t.start_line != 0 || t.end_line != 0)
            .cloned()
            .collect();

        if !zero_zero_targets.is_empty() {
            if zero_zero_targets.len() > 1 {
                warn!(
                    "Found {} '0-0' (new file) range blocks for file '{}'. Keeping only one.",
                    zero_zero_targets.len(),
                    path.display()
                );
            }
            path_final_targets.push(EditTarget {
                file_path: path.clone(),
                start_line: 0,
                end_line: 0,
            });
        }

        if !non_zero_targets.is_empty() {
            non_zero_targets.sort_by_key(|t| (t.start_line, t.end_line));
            let original_count = non_zero_targets.len();
            non_zero_targets.dedup_by_key(|t| (t.start_line, t.end_line));
            if non_zero_targets.len() < original_count {
                warn!(
                    "Discarded {} duplicate range(s) for file '{}'.",
                    original_count - non_zero_targets.len(),
                    path.display()
                );
            }

            if !non_zero_targets.is_empty() {
                let mut merged_ranges_for_file: Vec<EditTarget> = Vec::new();
                let mut current_merge = non_zero_targets[0].clone();
                for i in 1..non_zero_targets.len() {
                    let next_target = &non_zero_targets[i];
                    if next_target.start_line <= current_merge.end_line + 1 {
                        if next_target.end_line > current_merge.end_line {
                            current_merge.end_line = next_target.end_line;
                        }
                    } else {
                        merged_ranges_for_file.push(current_merge);
                        current_merge = next_target.clone();
                    }
                }
                merged_ranges_for_file.push(current_merge);
                if merged_ranges_for_file.len() < non_zero_targets.len() {
                    log::debug!(
                        "Merged {} overlapping range(s) for file '{}'.",
                        non_zero_targets.len() - merged_ranges_for_file.len(),
                        path.display()
                    );
                }
                path_final_targets.extend(merged_ranges_for_file);
            }
        }
        final_processed_targets.extend(path_final_targets);
    }
    // --- End Deduplicate and Merge ---

    if let Some(tx) = processing_tx_opt {
        if tx
            .send(ProcessingSignal::UpdateProgress(0.20))
            .await
            .is_err()
        {
            warn!("Failed to send progress update (0.20) after block processing");
        }
        if !final_processed_targets.is_empty() {
            // Only send EditPlanReady if there are targets
            if tx
                .send(ProcessingSignal::EditPlanReady {
                    plan: final_processed_targets.clone(),
                })
                .await
                .is_err()
            {
                warn!("Failed to send EditPlanReady after block processing");
            }
        }
    }

    // If only research content is provided and no edit targets, this is handled by the Ok(Some(research_text))
    // return path earlier in the function (for default flow) or by expert flow returning OnlyResearch.
    // If we reach here, it means no research was requested by this specific planning phase,
    // or if research was requested, it was already returned.

    if final_processed_targets.is_empty() {
        log::debug!(
            "LLM did not suggest any actionable edit targets or research after processing."
        );
    } else {
        let mut num_targets = 0;
        let mut num_files_set = HashSet::new();

        log::trace!("Final Processed Edit Targets (Pre-processing for new files):");
        for target in &final_processed_targets {
            log::trace!(
                "  - Processed Target: file {}, lines {}-{}",
                target.file_path.display(),
                target.start_line,
                target.end_line
            );
            if target.start_line == 0 && target.end_line == 0 {
                if target.file_path.exists()
                    || ordered_files.get_labeled_file(&target.file_path).is_some()
                // Check in OrderedFiles
                {
                    if target.file_path.exists() {
                        log::warn!(
                            "LLM proposed to create file '{}' with 0-0 range, but it already exists on disk. Skipping creation part of this target.",
                            target.file_path.display()
                        );
                    }
                } else {
                    log::debug!(
                        "LLM requests to create new file: {}",
                        target.file_path.display()
                    );
                    if let Some(parent_dir) = target.file_path.parent() {
                        if !parent_dir.exists() {
                            match std::fs::create_dir_all(parent_dir) {
                                Ok(_) => log::debug!(
                                    "Created parent directory/ies for {}",
                                    target.file_path.display()
                                ),
                                Err(e) => {
                                    error!("Failed to create parent directory/ies for {}: {}. Skipping target.", target.file_path.display(), e);
                                    continue;
                                }
                            }
                        }
                    }
                    match std::fs::File::create(&target.file_path) {
                        Ok(_) => {
                            log::debug!(
                                "Successfully created new file: {}",
                                target.file_path.display()
                            );
                            // Add to OrderedFiles
                            if let Err(e_create_context) =
                                ordered_files.create_new_file_in_context(&target.file_path, "")
                            {
                                error!("Failed to add new file {} to OrderedFiles context: {}. Skipping target.", target.file_path.display(), e_create_context);
                                continue;
                            }
                        }
                        Err(e) => {
                            error!(
                                "Failed to create new file {}: {}. Skipping target.",
                                target.file_path.display(),
                                e
                            );
                            continue;
                        }
                    }
                }
            }
            num_targets += 1;
            num_files_set.insert(target.file_path.clone());
            tasks_to_process.push_back(target.clone()); // Push EditTarget directly
        }

        if num_targets > 0 {
            // Interactive is always true, so condition simplifies
            if app_config.log_level == crate::logger::LogLevel::Debug
                || app_config.log_level == crate::logger::LogLevel::Trace
            {
                log::debug!("{}", "-".repeat(70));
            }
            log::info!(
                "📝 LLM Plan (processed): {} edits in {} files",
                num_targets,
                num_files_set.len()
            );

            let effective_summary_request_prompt =
                construct_plan_summary_prompt(app_config, &final_processed_targets);
            log::trace!(
                "Requesting LLM for plan summary with prompt:\n{}",
                effective_summary_request_prompt
            );

            let (summary_llm_client, context_for_plan_summary_request) = if let Some(
                summary_alias,
            ) =
                &app_config.summary_model
            {
                if summary_alias.to_lowercase() != "none"
                    && summary_alias.to_lowercase() != "default"
                {
                    match crate::config::app_config::setup_specific_llm_client(
                        app_config,
                        summary_alias,
                    ) {
                        Ok(client) => {
                            trace!("Using specific summary model ('{}') for plan summary with minimal context.", summary_alias);
                            (client, vec![]) // Minimal context
                        }
                        Err(e) => {
                            warn!("Failed to set up summary model '{}': {}. Falling back to default LLM and full context for plan summary.", summary_alias, e);
                            // historical_context (llm_messages_for_this_turn) already includes conversation_log.
                            (llm_instance.clone(), historical_context.to_vec()) // Fallback
                        }
                    }
                } else {
                    trace!("Using default LLM for plan summary with full context (summary_model is 'none' or 'default').");
                    // historical_context (llm_messages_for_this_turn) already includes conversation_log.
                    (llm_instance.clone(), historical_context.to_vec()) // Default LLM, full context
                }
            } else {
                trace!(
                    "Using default LLM for plan summary with full context (summary_model not set)."
                );
                // historical_context (llm_messages_for_this_turn) already includes conversation_log.
                (llm_instance.clone(), historical_context.to_vec()) // Default LLM, full context
            };

            // The conversation_log for block processing should reflect what was actually sent to the LLM.
            // If using a dedicated summary model, this context is minimal.
            let mut block_processing_conversation_log_for_summary =
                context_for_plan_summary_request.clone();

            match temporal_chat_inference(
                summary_llm_client.as_ref(),
                &context_for_plan_summary_request, // Use determined context
                &effective_summary_request_prompt,
            )
            .await
            {
                Ok(summary_response_text) => {
                    log::trace!("Raw LLM response for summary:\n{}", summary_response_text);
                    let summary_expectations = vec![BlockExpectation {
                        parser: Box::new(SummaryBlockParser),
                        expected_count: BlockCount::Exact(1),
                    }];

                    match process_llm_response_with_blocks(
                        &summary_response_text,
                        &summary_expectations,
                        &effective_summary_request_prompt,
                        &[],
                        llm_instance.clone(), // Clone Arc for this call
                        &context_for_plan_summary_request, // History prefix for this specific call
                        &mut block_processing_conversation_log_for_summary, // Conversation log for this specific call
                        app_config,
                    )
                    .await
                    {
                        Ok(summary_processed_output) => {
                            if let Some(summary_raw_blocks) = summary_processed_output
                                .successfully_parsed_blocks
                                .get(&SummaryBlockParser.id())
                            {
                                if let Some(raw_block) = summary_raw_blocks.first() {
                                    match SummaryBlockParser.parse_to_string(raw_block) {
                                        Ok(summary_text) if !summary_text.is_empty() => {
                                            log::debug!("LLM's plan summary: {}", summary_text);
                                            let num_targets_for_notification =
                                                final_processed_targets.len();
                                            let plan_title = format!(
                                                "📝 LLEdit Plan: {} Edit{}",
                                                num_targets_for_notification,
                                                if num_targets_for_notification == 1 {
                                                    ""
                                                } else {
                                                    "s"
                                                }
                                            );
                                            let truncated_summary_for_notification =
                                                truncate_with_ellipsis(&summary_text, 150);
                                            let current_dir_for_notification =
                                                std::env::current_dir()
                                                    .unwrap_or_else(|_| PathBuf::from("."));
                                            execute_notification_command(
                                                &app_config.notification_command,
                                                &current_dir_for_notification,
                                                &plan_title,
                                                &truncated_summary_for_notification,
                                            );
                                        }
                                        Ok(_) => {
                                            trace!("LLM provided an empty summary block.");
                                        }
                                        Err(e) => {
                                            trace!("Failed to parse content of validated summary block: {}", e);
                                        }
                                    }
                                } else {
                                    trace!("LLM response for summary did not contain the expected summary block after processing.");
                                }
                            } else {
                                trace!("No summary block found in LLM's response for summary request after processing.");
                            }
                            if !summary_processed_output.remaining_raw_blocks.is_empty() {
                                trace!(
                                    "Unclaimed raw blocks found in LLM summary response: {:?}",
                                    summary_processed_output.remaining_raw_blocks
                                );
                            }
                        }
                        Err(e) => {
                            trace!("Error processing LLM response for summary: {}", e);
                        }
                    }
                }
                Err(e) => {
                    trace!("Failed to get summary from LLM: {}", e);
                }
            }

            // Interactive is always true
            if app_config.log_level == crate::logger::LogLevel::Debug
                || app_config.log_level == crate::logger::LogLevel::Trace
            {
                log::debug!("{}", "-".repeat(70));
            }
            log::trace!("----------------------------------------------------------------------");
            // The else if for !app_config.interactive is no longer reachable.
        }
        log::trace!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    }
    // This function now only returns Ok(None) as research requests are handled by returning Ok(Some(research_text)) earlier.
    // If we reach here, it means no research was requested by this planning phase.
    Ok(None)
}
