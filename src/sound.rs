use crate::config::app_config::{AppConfig, SubmitSoundConfig};
use log::{debug, warn};
use rodio::{Decoder, OutputStream, Sink};
use std::io::Cursor;
use std::path::Path;

/// Sound system for playing audio feedback on user input submission
pub struct SoundSystem {
    /// Audio output stream - kept alive for the duration of the application
    _stream: OutputStream,
    /// Audio sink for playing sounds
    sink: Sink,
}

impl SoundSystem {
    /// Create a new sound system instance
    pub fn new() -> Result<Self, String> {
        let (_stream, stream_handle) = OutputStream::try_default()
            .map_err(|e| format!("Failed to initialize audio output: {}", e))?;
        
        let sink = Sink::try_new(&stream_handle)
            .map_err(|e| format!("Failed to create audio sink: {}", e))?;
        
        Ok(SoundSystem { _stream, sink })
    }

    /// Play submit sound based on configuration
    pub fn play_submit_sound(&self, config: &AppConfig) {
        // Check if sound is disabled
        if matches!(config.submit_sound, SubmitSoundConfig::Disabled) {
            debug!("Submit sound is disabled");
            return;
        }

        // Calculate volume (0-100 to 0.0-1.0)
        let volume = (config.sounds_master_volume as f32) / 100.0;
        self.sink.set_volume(volume);

        match &config.submit_sound {
            SubmitSoundConfig::Disabled => {
                // Already handled above
            }
            SubmitSoundConfig::SystemDefault => {
                debug!("Playing system default confirmation sound");
                self.play_system_default_sound();
            }
            SubmitSoundConfig::CustomPath(path) => {
                debug!("Playing custom sound from path: {}", path);
                self.play_custom_sound(path);
            }
        }
    }

    /// Play the system default confirmation sound
    fn play_system_default_sound(&self) {
        // Use a simple beep sound as the default
        // This is a basic sine wave tone at 800Hz for 200ms
        let sample_rate = 44100;
        let duration_ms = 200;
        let frequency = 800.0;
        let samples = generate_beep_samples(sample_rate, duration_ms, frequency);
        
        match self.play_samples(samples, sample_rate) {
            Ok(_) => debug!("System default sound played successfully"),
            Err(e) => warn!("Failed to play system default sound: {}", e),
        }
    }

    /// Play a custom sound from file path
    fn play_custom_sound(&self, path: &str) {
        let path = Path::new(path);
        
        if !path.exists() {
            warn!("Custom sound file does not exist: {}", path.display());
            // Fallback to system default
            self.play_system_default_sound();
            return;
        }

        match std::fs::File::open(path) {
            Ok(file) => {
                match Decoder::new(file) {
                    Ok(source) => {
                        self.sink.append(source);
                        debug!("Custom sound queued for playback: {}", path.display());
                    }
                    Err(e) => {
                        warn!("Failed to decode audio file {}: {}", path.display(), e);
                        // Fallback to system default
                        self.play_system_default_sound();
                    }
                }
            }
            Err(e) => {
                warn!("Failed to open audio file {}: {}", path.display(), e);
                // Fallback to system default
                self.play_system_default_sound();
            }
        }
    }

    /// Play raw audio samples
    fn play_samples(&self, samples: Vec<f32>, sample_rate: u32) -> Result<(), String> {
        let cursor = Cursor::new(create_wav_bytes(samples, sample_rate)?);
        let source = Decoder::new(cursor)
            .map_err(|e| format!("Failed to create decoder from samples: {}", e))?;
        
        self.sink.append(source);
        Ok(())
    }
}

/// Generate beep sound samples
fn generate_beep_samples(sample_rate: u32, duration_ms: u32, frequency: f32) -> Vec<f32> {
    let num_samples = (sample_rate * duration_ms) / 1000;
    let mut samples = Vec::with_capacity(num_samples as usize);
    
    for i in 0..num_samples {
        let t = i as f32 / sample_rate as f32;
        let sample = (2.0 * std::f32::consts::PI * frequency * t).sin();
        
        // Apply fade in/out to avoid clicks
        let fade_samples = sample_rate / 20; // 50ms fade
        let fade_factor = if i < fade_samples {
            i as f32 / fade_samples as f32
        } else if i > num_samples - fade_samples {
            (num_samples - i) as f32 / fade_samples as f32
        } else {
            1.0
        };
        
        samples.push(sample * fade_factor);
    }
    
    samples
}

/// Create WAV file bytes from raw samples
fn create_wav_bytes(samples: Vec<f32>, sample_rate: u32) -> Result<Vec<u8>, String> {
    let mut wav_data = Vec::new();
    
    // WAV header
    wav_data.extend_from_slice(b"RIFF");
    let file_size = 36 + samples.len() * 2; // 16-bit samples
    wav_data.extend_from_slice(&(file_size as u32).to_le_bytes());
    wav_data.extend_from_slice(b"WAVE");
    
    // Format chunk
    wav_data.extend_from_slice(b"fmt ");
    wav_data.extend_from_slice(&16u32.to_le_bytes()); // Chunk size
    wav_data.extend_from_slice(&1u16.to_le_bytes()); // PCM format
    wav_data.extend_from_slice(&1u16.to_le_bytes()); // Mono
    wav_data.extend_from_slice(&sample_rate.to_le_bytes());
    wav_data.extend_from_slice(&(sample_rate * 2).to_le_bytes()); // Byte rate
    wav_data.extend_from_slice(&2u16.to_le_bytes()); // Block align
    wav_data.extend_from_slice(&16u16.to_le_bytes()); // Bits per sample
    
    // Data chunk
    wav_data.extend_from_slice(b"data");
    wav_data.extend_from_slice(&(samples.len() * 2).to_le_bytes());
    
    // Convert f32 samples to i16 and append
    for sample in samples {
        let sample_i16 = (sample.clamp(-1.0, 1.0) * 32767.0) as i16;
        wav_data.extend_from_slice(&sample_i16.to_le_bytes());
    }
    
    Ok(wav_data)
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::app_config::SubmitSoundConfig;

    fn create_test_config(submit_sound: SubmitSoundConfig, volume: u8) -> AppConfig {
        AppConfig {
            sounds_master_volume: volume,
            submit_sound,
            // Default values for other fields
            user_prompt: String::new(),
            file_paths: Vec::new(),
            default_model: "test".to_string(),
            provider: "test".to_string(),
            model: "test".to_string(),
            effective_provider_api_key: String::new(),
            provider_url: String::new(),
            models_list: None,
            results_input_file: None,
            results_input: None,
            task_info: None,
            no_think: std::collections::HashSet::new(),
            results_output_mode: crate::config::app_config::ResultsOutputMode::None,
            results_output_file: None,
            log_level: crate::logger::LogLevel::Off,
            exit_on_success: false,
            timestamps: false,
            restore_previous_session_on_startup: false,
            restore_previous_session_models: false,
            auto_test_command: String::new(),
            auto_test_toggle: false,
            max_task_retries: 1,
            auto_research_mode: crate::config::app_config::AutoResearchMode::False,
            auto_expert_switch: crate::config::app_config::AutoExpertSwitch::False,
            expert_model: None,
            auto_expert_mode: crate::config::app_config::AutoExpertMode::Planning,
            notification_command: String::new(),
            forced_research_for_next_cycle: None,
            decision_model_auto_research_loop_max: 1,
            expert_model_auto_research_loop_max: 1,
            retry_model: None,
            retry_model_max_attempts: 1,
            summary_model: None,
            research_model: None,
            decision_model: None,
            advanced_language_features: std::collections::HashSet::new(),
            http_api_port: 8080,
        }
    }

    #[test]
    fn test_generate_beep_samples() {
        let samples = generate_beep_samples(44100, 100, 440.0);
        assert_eq!(samples.len(), 4410); // 44100 * 100 / 1000
        
        // Check that samples are within valid range
        for sample in &samples {
            assert!(sample >= &-1.0 && sample <= &1.0);
        }
    }

    #[test]
    fn test_create_wav_bytes() {
        let samples = vec![0.0, 0.5, -0.5, 1.0, -1.0];
        let wav_data = create_wav_bytes(samples, 44100).unwrap();
        
        // Check WAV header
        assert_eq!(&wav_data[0..4], b"RIFF");
        assert_eq!(&wav_data[8..12], b"WAVE");
        assert_eq!(&wav_data[12..16], b"fmt ");
        assert_eq!(&wav_data[36..40], b"data");
    }

    #[test]
    fn test_sound_system_creation() {
        // This test might fail in CI environments without audio
        // but should work in development environments
        match SoundSystem::new() {
            Ok(_) => {
                // Sound system created successfully
            }
            Err(e) => {
                // Expected in headless environments
                assert!(e.contains("audio"));
            }
        }
    }
}
