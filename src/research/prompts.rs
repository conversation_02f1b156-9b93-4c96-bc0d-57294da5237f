use crate::block_parsing::bash_command_block::BashCommandBlockParser;
use crate::block_parsing::complete_files_block::CompleteFilesBlockParser;
use crate::block_parsing::context_files_block::ContextFilesBlockParser;
use crate::block_parsing::edit_files_block::EditFilesBlockParser;
use crate::block_parsing::traits::ParsableBlock;
use crate::config::AppConfig;
use crate::files::file_handler::LabeledFile;
use std::collections::HashMap;
use std::path::PathBuf;

pub fn construct_initial_research_prompt(
    app_config: &AppConfig,
    task_description: &str,
    files_already_in_context: &HashMap<PathBuf, LabeledFile>, // Changed parameter
    bash_parser: &BashCommandBlockParser,
    complete_parser: &CompleteFilesBlockParser,
) -> String {
    // Define allowed commands and examples statically.
    // LLM will always be instructed to use grep, find, head, ls, tail, and xargs.
    // If rg is available, grep commands will be converted later.
    let allowed_commands_list_str_parts =
        vec!["`grep`", "`find`", "`head`", "`ls`", "`tail`", "`xargs`"];
    let command_examples_str_parts = vec![
        "Example for grep:\n```bash\ngrep -r 'my_pattern' ./src\n```",
        "Example for find:\n```bash\nfind ./project -type f -name '*.rs'\n```",
        "Example for head:\n```bash\nhead -n 20 path/to/file.txt\n```",
        "Example for ls:\n```bash\nls -la ./src\n```",
        "Example for tail:\n```bash\ntail -n 20 path/to/file.txt\n```",
        "Example for xargs:\n```bash\nfind . -name '*.log' -print0 | xargs -0 grep 'ERROR'\n```",
    ];

    let allowed_commands_str = allowed_commands_list_str_parts.join(", ");
    let command_examples_str = command_examples_str_parts.join("\n\n");

    let command_guidance = format!(
        "1. To explore the codebase, issue shell commands. You are ONLY allowed to use {}.\n\
           Format for commands:\n{}\n\
           {}\n",
        allowed_commands_str,
        bash_parser.block_format_description(), // Generic ```bash ... ```
        command_examples_str
    );

    let mut base_instruction_prompt = format!(
        "You are an advanced AI research assistant helping a developer research a codebase for a specific task: '{}'.\n\
        Your role is strictly to research and identify files. You **cannot perform any edits or write any code** in your responses. \
        You can only use the provided shell commands to read and search files, or use the `complete-files` block.\n\n\
        Your primary goal is to identify ALL files that provide relevant and important context necessary to achieve the task. \
        This includes: \n\
        a) All files that will likely need to be edited to fulfill the task.\n\
        b) All files that provide essential context for the task, such as definitions of functions, structs, classes, or other relevant code snippets that will be used, modified, or are otherwise crucial for understanding, even if those files themselves won't be directly edited.\n\n\
        You must look through all potentially relevant files before finishing. Remember you must only read parts of files to get enough of an understanding using `head -n` and `tail -n`. Dont read a single file more than 3 times to get an idea and be efficient. \
        It is recommended that you think of all potentially relevant keywords along the way, and constantly use `grep` & `head -n` to make sure you find all relevant and needed files.\n\n\
        You have two ways to communicate actions:\n\
        {}\n\
        2. When you believe you have thoroughly researched and found all necessary files as described above, you MUST respond with a `complete-files` block. \
           Research should only be considered complete once you are confident that all such required and relevant files have been identified, enabling the developer to proceed with the task effectively. \
           Though you are to include all relevant files, you MUST also use judgement by being selective and not just providing all possible files. Be smart but accurate. Too many files or too few are both bad. \
           Do not use the `complete-files` block prematurely.\n\
           Format for completion:\n{}\n\
           Example:\n{}\n\n\
        You must issue one of the two codeblocks in every message (must be only a single block per message).\n\
        {}\n\n\
        Start your research. First think out loud for 1-4 paragraphs without specifying about what kind of files and potential keywords you need to find (without providing any code blocks), and then issue a single first bash code block (you are not allowed to specify more than one per response).",
        task_description,
        command_guidance, // This is point 1
        complete_parser.block_format_description(),
        complete_parser.example_prompt_text(),
        { // Generate the files in context string
            let mut files_list_parts: Vec<String> = Vec::new();
            if !files_already_in_context.is_empty() {
                files_list_parts.push("The following files are already in context (do not read their content again unless absolutely necessary for a specific check):".to_string());
                let current_dir_for_display = std::env::current_dir().unwrap_or_else(|_| PathBuf::from("."));
                for path_key in files_already_in_context.keys() {
                    let display_path = path_key.strip_prefix(&current_dir_for_display)
                                               .unwrap_or(path_key) // Fallback to full path if not relative
                                               .display()
                                               .to_string();
                    files_list_parts.push(format!("- {}", display_path));
                }
            } else {
                files_list_parts.push("No files are currently in context.".to_string());
            }
            files_list_parts.join("\n")
        }
    );

    crate::llm::apply_prefix_based_on_mode(
        &mut base_instruction_prompt,
        app_config,
        crate::config::app_config::NoThinkMode::ResearchPlanning,
    );
    base_instruction_prompt
}

pub fn construct_research_follow_up_prompt(app_config: &AppConfig) -> String {
    let mut user_follow_up_message_content = "Now that you have the output from the command, evaluate if you have gathered enough information. \
If you are confident that you have identified ALL files that need to be edited AND all files providing essential context (like function/struct definitions) to complete the user's task, then submit a single `complete-files` block (remember too many files or too few files are both bad). \
Otherwise, you MUST continue your research by issuing a new `bash` command to explore further. \
Remember to think for 1-2 sentences before providing a single block, and that you are not allowed to read a specific file more than 3 times, to be efficient.".to_string();
    crate::llm::apply_prefix_based_on_mode(
        &mut user_follow_up_message_content,
        app_config,
        crate::config::app_config::NoThinkMode::ResearchSearching,
    );
    user_follow_up_message_content
}

pub fn construct_categorize_files_prompt(
    app_config: &AppConfig,
    task_description: &str,
    discovered_files: &[PathBuf], // These are absolute, canonical paths
    context_parser: &ContextFilesBlockParser,
    edit_parser: &EditFilesBlockParser,
) -> String {
    let mut prompt = format!(
        "You are an AI assistant helping a developer categorize files for a specific task: \"{}\".\n\n",
        task_description
    );

    prompt.push_str(
        "The following files have been discovered as potentially relevant to this task:\n",
    );
    let current_dir_for_display = std::env::current_dir().unwrap_or_else(|_| PathBuf::from("."));
    for (idx, path) in discovered_files.iter().enumerate() {
        let display_path = path
            .strip_prefix(&current_dir_for_display)
            .unwrap_or(path) // Fallback to full path if not relative
            .display()
            .to_string();
        prompt.push_str(&format!("{}. {}\n", idx + 1, display_path));
    }

    prompt.push_str("\nYour goal is to categorize these files into two groups:\n");
    prompt.push_str("1. Context Files: Files that are primarily for understanding the context of the task, providing definitions, or showing related logic. These files are *unlikely* to be directly edited to complete the task.\n");
    prompt.push_str(
        "2. Edit Files: Files that are *likely* to be directly edited to complete the task.\n\n",
    );

    prompt.push_str("You MUST provide your categorization in two separate code blocks: a `context-files` block and an `edit-files` block. Each block should list the full paths of the files belonging to that category, one path per line. Use the exact paths as listed above.\n");
    prompt.push_str("If a category has no files, you can provide an empty block for it (e.g., ```context-files\n```).\n\n");

    prompt.push_str(&format!(
        "Format for Context Files block:\n{}\nExample:\n{}\n\n",
        context_parser.block_format_description(),
        context_parser.example_prompt_text()
    ));
    prompt.push_str(&format!(
        "Format for Edit Files block:\n{}\nExample:\n{}\n\n",
        edit_parser.block_format_description(),
        edit_parser.example_prompt_text()
    ));

    prompt.push_str("Please provide both blocks. Do not include any other explanatory text outside of these blocks in your response.");

    // Apply no_think/think prefix. ResearchCategorizing might be a new NoThinkMode variant.
    // For now, let's use ResearchAutoDecision as it's a decision-making step.
    // Or, create a new one: NoThinkMode::ResearchCategorizing
    crate::llm::apply_prefix_based_on_mode(
        &mut prompt,
        app_config,
        crate::config::app_config::NoThinkMode::ResearchAutoDecision, // Or a new mode like ResearchCategorizing
    );
    prompt
}

pub fn construct_auto_research_decision_prompt(
    app_config: &AppConfig,
    user_edit_task_prompt: &str,
    task_info: Option<&str>, // New parameter
    current_file_paths_in_context: &[String],
    ls_command_output: &str,
    auto_research_mandatory: bool,
) -> String {
    let files_in_context_str = if current_file_paths_in_context.is_empty() {
        "No files are currently in context.".to_string()
    } else {
        format!("- {}", current_file_paths_in_context.join("\n- "))
    };

    let mut task_info_str = String::new();
    if let Some(info) = task_info {
        if !info.trim().is_empty() {
            task_info_str = format!(
                "Additional Task Context (may include previous errors or analysis):\n---\n{}\n---\n\n",
                info.trim()
            );
        }
    }

    let mut auto_research_decision_prompt = format!(
        "You are an advanced coding assistant. Your current task is to help a developer with: \"{}\".\n\n\
        {}{}\
        The following files have already been provided to you:\n{}\n\n\
        Here is the output from running `ls -la` in the project's working directory:\n```\n{}\n```\n\n\
        If you need to request research, provide a `research` code block formatted as follows:\n\
        ```research\n<1-3 paragraph explanation of the task, needed files, likely programming language, and a list of 5-10 keywords/strings for 'grep' to find relevant code elements.>\n```\n\n \
        The research assistant you will be instructing can only read files and search the codebase; it **cannot perform any edits or write code**.\n ",
        user_edit_task_prompt,
        task_info_str, // Prepend task_info_str here
        if !task_info_str.is_empty() { "\n" } else { "" }, // Add a newline if task_info was present
        files_in_context_str,
        ls_command_output
    );

    if auto_research_mandatory {
        auto_research_decision_prompt.push_str(
            "To proceed with the user's task, you MUST request for additional relevant files from the research assistant. \
            Therefore, you MUST provide a single `research` block as described above.",
        );
    } else {
        auto_research_decision_prompt.push_str(
            "Your primary goal is to decide if the files already provided are sufficient to solve the user's task. \
            If you believe the current files are sufficient, respond ONLY with a 1-line confirmation (e.g., 'The current files are sufficient.'). \
            If the current files are NOT sufficient, or if you are unsure, you MUST request further information by providing a single `research` block as described above. \
            Please make your decision and respond accordingly."
        );
    }

    crate::llm::apply_prefix_based_on_mode(
        &mut auto_research_decision_prompt,
        app_config,
        crate::config::app_config::NoThinkMode::ResearchAutoDecision,
    );
    auto_research_decision_prompt
}
