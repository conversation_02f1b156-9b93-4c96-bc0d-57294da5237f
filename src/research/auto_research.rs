use crate::block_parsing::processor::{
    process_llm_response_with_blocks, BlockCount, BlockExpectation,
};
use crate::block_parsing::traits::ParsableBlock;
use crate::config::AppConfig;
use crate::editor::ProcessingSignal;
use crate::files::file_handler::LabeledFile; // Corrected path
use crate::llm::{ChatMessage, ChatRole, LLMClient, MessageType};
use crate::research::prompts::construct_auto_research_decision_prompt;
use log::{debug, trace, warn}; // Added info, then removed unused info
use std::collections::HashMap;
use std::path::PathBuf;
use std::sync::Arc; // Added import
use tokio::sync::mpsc;

/// Handles the automatic research phase within an edit cycle.
///
/// Returns:
/// - `Ok(String)`: A summary message of the auto-research outcome.
/// - `Err(String)`: An error message if a mandatory research step failed critically.
#[allow(clippy::too_many_arguments)]
pub async fn conduct_automatic_research(
    app_config: &AppConfig,
    llm_instance: Arc<dyn LLMClient>,            // Changed to Arc
    all_previous_tasks: &[crate::task::Task],    // New parameter
    historical_messages_for_llm: &[ChatMessage], // This is the full context from the main loop, potentially with files message
    messages_for_current_task: &mut Vec<ChatMessage>, // Current task's evolving dialogue
    ordered_files: &mut crate::files::ordered_files::OrderedFiles, // Added &mut OrderedFiles
    user_edit_task_prompt: &str,
    is_retry: bool,
    processing_tx_opt: Option<&mpsc::Sender<ProcessingSignal>>,
    forced_research_task_description: Option<String>, // New parameter
) -> Result<(String, Option<crate::research::types::CategorizedFilePaths>), String> {
    // Modified return type

    // If forced_research_task_description is provided, bypass LLM decision and run research directly.
    if let Some(forced_desc) = forced_research_task_description {
        if forced_desc.trim().is_empty() {
            warn!("Forced research description was empty. Skipping forced research.");
            return Ok((
                "Forced research skipped due to empty description.".to_string(),
                None,
            ));
        }
        if let Some(tx) = processing_tx_opt.as_ref() {
            if tx
                .send(ProcessingSignal::AutoResearchStarted)
                .await
                .is_err()
            {
                warn!("Failed to send AutoResearchStarted signal for forced research");
            }
        }

        let current_path_for_research =
            std::env::current_dir().unwrap_or_else(|_| PathBuf::from("."));
        let mut temp_sub_research_cycle_messages = Vec::new(); // For run_research_cycle

        let forced_research_llm_client: Arc<dyn LLMClient>;
        let history_for_forced_research_cycle: Vec<ChatMessage>;

        if let Some(alias) = &app_config.research_model {
            if alias.to_lowercase() != "none" && alias.to_lowercase() != "default" {
                match crate::config::app_config::setup_specific_llm_client(app_config, alias) {
                    Ok(client) => {
                        log::info!("Using specific research model ('{}') for forced auto-research. History will be isolated.", alias);
                        forced_research_llm_client = client;
                        history_for_forced_research_cycle = Vec::new();
                    }
                    Err(e) => {
                        log::warn!("Failed to set up research model '{}' for forced auto-research: {}. Falling back to default LLM.", alias, e);
                        forced_research_llm_client = llm_instance.clone();
                        // For fallback, use combined history
                        let mut combined_history = historical_messages_for_llm.to_vec();
                        combined_history.extend_from_slice(messages_for_current_task);
                        history_for_forced_research_cycle = combined_history;
                    }
                }
            } else {
                log::trace!(
                    "Using default LLM for forced auto-research (research_model_alias is '{}').",
                    alias
                );
                forced_research_llm_client = llm_instance.clone();
                let mut combined_history = historical_messages_for_llm.to_vec();
                combined_history.extend_from_slice(messages_for_current_task);
                history_for_forced_research_cycle = combined_history;
            }
        } else {
            log::trace!(
                "Using default LLM for forced auto-research (research_model_alias not set)."
            );
            forced_research_llm_client = llm_instance.clone();
            let mut combined_history = historical_messages_for_llm.to_vec();
            combined_history.extend_from_slice(messages_for_current_task);
            history_for_forced_research_cycle = combined_history;
        }

        let research_result = match crate::research::research_cycle::run_research_cycle(
            app_config,
            forced_research_llm_client,
            &forced_desc,
            &mut temp_sub_research_cycle_messages,
            &history_for_forced_research_cycle,
            processing_tx_opt,
            &format!("forced_research_{}", chrono::Utc::now().timestamp_millis()),
            &current_path_for_research,
            ordered_files.get_all_labeled_files_map(), // Pass current file map from OrderedFiles
        )
        .await
        {
            Ok(categorized_paths) => {
                if !categorized_paths.all_discovered.is_empty() {
                    // Add to ordered_files directly
                    ordered_files
                        .add_categorized_files_from_research(&categorized_paths)
                        .await;
                    ordered_files.apply_research_categorization(&categorized_paths);

                    // Send AutoResearchUpdate signal for immediate TUI update
                    if let Some(tx) = processing_tx_opt.as_ref() {
                        if tx
                            .send(ProcessingSignal::AutoResearchUpdate {
                                categorized_files: categorized_paths.clone(),
                            })
                            .await
                            .is_err()
                        {
                            warn!("Failed to send AutoResearchUpdate signal for forced research");
                        }
                    }

                    let (_, display_names) = process_discovered_paths(
                        // _ is newly_read_map, not used here
                        categorized_paths.all_discovered.clone(),
                        &current_path_for_research,
                    )
                    .await;

                    (
                        format!(
                            "Forced research found and added new files: {}.",
                            display_names.join(", ")
                        ),
                        Some(categorized_paths),
                    )
                } else {
                    // Still send update even if no files, to signal completion of this step if needed by TUI logic.
                    if let Some(tx) = processing_tx_opt.as_ref() {
                        if tx
                            .send(ProcessingSignal::AutoResearchUpdate {
                                categorized_files: categorized_paths.clone(),
                            })
                            .await
                            .is_err()
                        {
                            warn!("Failed to send AutoResearchUpdate signal for forced research (no files found)");
                        }
                    }
                    (
                        "Forced research completed but found no files.".to_string(),
                        Some(categorized_paths),
                    )
                }
            }
            Err(e) => {
                warn!("Forced research flow failed: {}", e);
                (format!("Forced research failed: {}", e), None)
            }
        };

        if let Some(tx) = processing_tx_opt.as_ref() {
            if tx.send(ProcessingSignal::AutoResearchEnded).await.is_err() {
                warn!("Failed to send AutoResearchEnded signal for forced research");
            }
        }
        return Ok(research_result);
    }

    let mut effective_auto_research_mode = app_config.auto_research_mode;
    let mut auto_research_skipped_by_config = false;

    // Handle 'ExpertOnly' mode first, as it bypasses most of this function's logic.
    if effective_auto_research_mode == crate::config::app_config::AutoResearchMode::ExpertOnly {
        debug!("Auto-research mode is '{:?}'. Standard auto-research decision process is skipped. Research may be initiated by the expert model.", effective_auto_research_mode);
        return Ok((
            format!("Auto-research is configured for '{:?}' mode. Research may be initiated by the expert model if configured.", effective_auto_research_mode),
            None,
        ));
    }

    // Handle 'ExpertAndNoFilesOnly' mode: only skip if there ARE files in context
    if effective_auto_research_mode == crate::config::app_config::AutoResearchMode::ExpertAndNoFilesOnly {
        if !ordered_files.is_empty() {
            debug!("Auto-research mode is 'ExpertAndNoFilesOnly' and files are present. Standard auto-research decision process is skipped. Research may be initiated by the expert model.");
            return Ok((
                "Auto-research is configured for 'ExpertAndNoFilesOnly' mode and files are present. Research may be initiated by the expert model if configured.".to_string(),
                None,
            ));
        } else {
            debug!("Auto-research mode is 'ExpertAndNoFilesOnly' and no files are present. This will be handled by forcing expert research.");
            // Don't return early - let the logic continue to force research, but ensure expert model is used
            // We'll modify the logic below to use expert model when this mode is active and no files exist
        }
    }

    if is_retry {
        match effective_auto_research_mode {
            crate::config::app_config::AutoResearchMode::First
            | crate::config::app_config::AutoResearchMode::NoFiles
            | crate::config::app_config::AutoResearchMode::ExpertAndNoFilesOnly => {
                debug!(
                    "Auto-research mode is '{:?}' but this is a retry. Skipping research decision step.",
                    effective_auto_research_mode
                );
                auto_research_skipped_by_config = true;
                effective_auto_research_mode = crate::config::app_config::AutoResearchMode::False;
            }
            crate::config::app_config::AutoResearchMode::FirstForcedThenFalse => {
                debug!("Auto-research mode is 'FirstForcedThenFalse' but this is a retry. Behaving as 'False'.");
                auto_research_skipped_by_config = true;
                effective_auto_research_mode = crate::config::app_config::AutoResearchMode::False;
            }
            crate::config::app_config::AutoResearchMode::FirstForcedThenTrue => {
                debug!("Auto-research mode is 'FirstForcedThenTrue' but this is a retry. Behaving as 'True'.");
                effective_auto_research_mode = crate::config::app_config::AutoResearchMode::True;
            }
            _ => {}
        }
    }

    if effective_auto_research_mode == crate::config::app_config::AutoResearchMode::False {
        if !auto_research_skipped_by_config {
            debug!("Auto-research is set to false. Skipping research step.");
        }
        return Ok(("Auto-research skipped by configuration.".to_string(), None));
        // Return tuple
    }

    if let Some(tx) = processing_tx_opt.as_ref() {
        if tx
            .send(ProcessingSignal::AutoResearchStarted)
            .await
            .is_err()
        {
            warn!("Failed to send AutoResearchStarted signal");
        }
    }

    let ls_command_output_str = match tokio::process::Command::new("ls")
        .current_dir(std::env::current_dir().unwrap_or_else(|_| PathBuf::from(".")))
        .output()
        .await
    {
        Ok(output) => {
            if output.status.success() {
                String::from_utf8_lossy(&output.stdout).to_string()
            } else {
                let stderr = String::from_utf8_lossy(&output.stderr);
                warn!(
                    "'ls' command failed for auto-research prompt. Stderr: {}",
                    stderr
                );
                format!("Failed to execute 'ls': {}", stderr)
            }
        }
        Err(e) => {
            warn!(
                "Failed to spawn 'ls' command for auto-research prompt: {}",
                e
            );
            format!("Error executing 'ls': {}", e)
        }
    };

    let current_file_paths_for_auto_research_prompt: Vec<String> = ordered_files
        .get_all_labeled_files_map()
        .keys()
        .map(|p| p.display().to_string())
        .collect();

    let auto_research_mandatory = match effective_auto_research_mode {
        // Note: AutoResearchMode::False would have caused an early exit by the `if effective_auto_research_mode == AutoResearchMode::False` check.
        // Note: Original AutoResearchMode::ExpertOnly would have caused an early exit by its specific check.
        // Thus, those enum arms are technically not needed here if the prior logic is sound,
        // but are included for exhaustiveness and safety.
        crate::config::app_config::AutoResearchMode::False => false,
        crate::config::app_config::AutoResearchMode::ExpertOnly => false,

        crate::config::app_config::AutoResearchMode::True
        | crate::config::app_config::AutoResearchMode::First // Handled if not retried to False
        | crate::config::app_config::AutoResearchMode::NoFiles // Handled if not retried to False
        | crate::config::app_config::AutoResearchMode::ExpertAndNoFilesOnly // Handled if not retried to False
        => ordered_files.is_empty(),

        crate::config::app_config::AutoResearchMode::Forced
        | crate::config::app_config::AutoResearchMode::FirstForcedThenFalse // Handled if not retried to False
        | crate::config::app_config::AutoResearchMode::FirstForcedThenTrue // Handled if not retried to False or True
        => true,
    };

    let auto_research_decision_prompt = construct_auto_research_decision_prompt(
        app_config,
        user_edit_task_prompt,
        app_config.task_info.as_deref(), // Pass task_info
        &current_file_paths_for_auto_research_prompt,
        &ls_command_output_str,
        auto_research_mandatory,
    );

    let mut temp_auto_research_decision_dialog: Vec<ChatMessage> = Vec::new();
    temp_auto_research_decision_dialog.push(ChatMessage {
        role: ChatRole::User,
        content: auto_research_decision_prompt.clone(),
        message_type: MessageType::Text,
    });

    // Determine LLM and context for the decision call
    let decision_llm_client: Arc<dyn LLMClient>;
    let context_for_decision_llm_call: Vec<ChatMessage>;

    if let Some(alias) = &app_config.decision_model {
        if alias.to_lowercase() != "none" && alias.to_lowercase() != "default" {
            match crate::config::app_config::setup_specific_llm_client(app_config, alias) {
                Ok(client) => {
                    log::debug!(
                        "Using specific decision model ('{}') for auto-research decision.",
                        alias
                    );
                    decision_llm_client = client;
                    // Context for specific decision model: build from all_previous_tasks and messages_for_current_task,
                    // excluding any top-level files message.
                    let mut base_context =
                        crate::editor::history_assembler::assemble_llm_messages_without_files(
                            all_previous_tasks,
                            messages_for_current_task,
                        );
                    // The decision prompt itself is added last
                    base_context.push(temp_auto_research_decision_dialog.last().unwrap().clone());
                    context_for_decision_llm_call = base_context;
                }
                Err(e) => {
                    log::warn!("Failed to set up decision model ('{}'): {}. Falling back to default LLM for auto-research decision.", alias, e);
                    decision_llm_client = llm_instance.clone();
                    // For fallback, use the full historical_messages_for_llm (which includes files message)
                    let mut full_context = historical_messages_for_llm.to_vec();
                    full_context.extend_from_slice(messages_for_current_task);
                    full_context.push(temp_auto_research_decision_dialog.last().unwrap().clone());
                    context_for_decision_llm_call = full_context;
                }
            }
        } else {
            log::trace!(
                "Using default LLM for auto-research decision (decision_model is '{}').",
                alias
            );
            decision_llm_client = llm_instance.clone();
            let mut full_context = historical_messages_for_llm.to_vec();
            full_context.extend_from_slice(messages_for_current_task);
            full_context.push(temp_auto_research_decision_dialog.last().unwrap().clone());
            context_for_decision_llm_call = full_context;
        }
    } else {
        log::trace!("Using default LLM for auto-research decision (decision_model not set).");
        decision_llm_client = llm_instance.clone();
        let mut full_context = historical_messages_for_llm.to_vec();
        full_context.extend_from_slice(messages_for_current_task);
        full_context.push(temp_auto_research_decision_dialog.last().unwrap().clone());
        context_for_decision_llm_call = full_context;
    }

    trace!(
        "Attempting automatic research decision. Prompt (not added to main history):\n{}",
        auto_research_decision_prompt
    );

    let research_outcome_summary: (String, Option<crate::research::types::CategorizedFilePaths>) =
        match crate::llm::client::chat_inference(
            decision_llm_client.as_ref(),
            &context_for_decision_llm_call,
        )
        .await
        {
            Ok(response_text) => {
                temp_auto_research_decision_dialog.push(ChatMessage {
                    role: ChatRole::Assistant,
                    content: response_text.clone(),
                    message_type: MessageType::Text,
                });

                let research_parser = crate::block_parsing::research_block::ResearchBlockParser;
                let research_block_expectation = if auto_research_mandatory {
                    BlockCount::Exact(1)
                } else {
                    BlockCount::Optional
                };
                let expectations = vec![BlockExpectation {
                    parser: Box::new(crate::block_parsing::research_block::ResearchBlockParser),
                    expected_count: research_block_expectation,
                }];

                let mut history_prefix_for_decision_processing =
                    historical_messages_for_llm.to_vec();
                history_prefix_for_decision_processing.extend_from_slice(messages_for_current_task);

                match process_llm_response_with_blocks(
                    &response_text,
                    &expectations,
                    &auto_research_decision_prompt,
                    &ordered_files
                        .get_all_labeled_files_map()
                        .values()
                        .cloned()
                        .collect::<Vec<_>>(), // Use OrderedFiles
                    llm_instance.clone(), // Clone Arc
                    &history_prefix_for_decision_processing,
                    &mut temp_auto_research_decision_dialog,
                    app_config,
                )
                .await
                {
                    Ok(processed_output) => {
                        if let Some(research_block_raw) =
                            processed_output.get_first_block_by_id(&research_parser.id())
                        {
                            match research_parser.parse_to_string(research_block_raw) {
                                Ok(research_block_content_string) => {
                                    trace!(
                                        "Automatic research triggered. Research task: {}",
                                        research_block_content_string
                                    );
                                    let current_path_for_research = std::env::current_dir()
                                        .unwrap_or_else(|_| PathBuf::from("."));
                                    let mut temp_sub_research_cycle_messages = Vec::new();

                                    let auto_research_sub_cycle_llm_client: Arc<dyn LLMClient>;
                                    let history_for_auto_research_sub_cycle: Vec<ChatMessage>;

                                    // Check if we should use expert model for ExpertAndNoFilesOnly mode
                                    if app_config.auto_research_mode == crate::config::app_config::AutoResearchMode::ExpertAndNoFilesOnly
                                        && ordered_files.is_empty() {
                                        // Use expert model for research when ExpertAndNoFilesOnly mode and no files
                                        if let Some(expert_alias) = &app_config.expert_model {
                                            match crate::config::app_config::setup_specific_llm_client(app_config, expert_alias) {
                                                Ok(client) => {
                                                    log::info!("Using expert model ('{}') for auto-research in ExpertAndNoFilesOnly mode with no files. History will be isolated.", expert_alias);
                                                    auto_research_sub_cycle_llm_client = client;
                                                    history_for_auto_research_sub_cycle = Vec::new();
                                                }
                                                Err(e) => {
                                                    log::warn!("Failed to set up expert model '{}' for ExpertAndNoFilesOnly auto-research: {}. Falling back to default LLM.", expert_alias, e);
                                                    auto_research_sub_cycle_llm_client = llm_instance.clone();
                                                    let mut combined_history = historical_messages_for_llm.to_vec();
                                                    combined_history.extend_from_slice(messages_for_current_task);
                                                    history_for_auto_research_sub_cycle = combined_history;
                                                }
                                            }
                                        } else {
                                            log::warn!("ExpertAndNoFilesOnly mode is active with no files, but no expert model is configured. Falling back to default LLM.");
                                            auto_research_sub_cycle_llm_client = llm_instance.clone();
                                            let mut combined_history = historical_messages_for_llm.to_vec();
                                            combined_history.extend_from_slice(messages_for_current_task);
                                            history_for_auto_research_sub_cycle = combined_history;
                                        }
                                    } else if let Some(alias) = &app_config.research_model {
                                        if alias.to_lowercase() != "none"
                                            && alias.to_lowercase() != "default"
                                        {
                                            match crate::config::app_config::setup_specific_llm_client(app_config, alias) {
                                                Ok(client) => {
                                                    log::info!("Using specific research model ('{}') for LLM-decided auto-research. History will be isolated.", alias);
                                                    auto_research_sub_cycle_llm_client = client;
                                                    history_for_auto_research_sub_cycle = Vec::new();
                                                }
                                                Err(e) => {
                                                    log::warn!("Failed to set up research model '{}' for LLM-decided auto-research: {}. Falling back to default LLM.", alias, e);
                                                    auto_research_sub_cycle_llm_client = llm_instance.clone();
                                                    let mut combined_history = historical_messages_for_llm.to_vec();
                                                    combined_history.extend_from_slice(messages_for_current_task);
                                                    history_for_auto_research_sub_cycle = combined_history;
                                                }
                                            }
                                        } else {
                                            log::trace!("Using default LLM for LLM-decided auto-research (research_model_alias is '{}').", alias);
                                            auto_research_sub_cycle_llm_client =
                                                llm_instance.clone();
                                            let mut combined_history =
                                                historical_messages_for_llm.to_vec();
                                            combined_history
                                                .extend_from_slice(messages_for_current_task);
                                            history_for_auto_research_sub_cycle = combined_history;
                                        }
                                    } else {
                                        log::trace!("Using default LLM for LLM-decided auto-research (research_model_alias not set).");
                                        auto_research_sub_cycle_llm_client = llm_instance.clone();
                                        let mut combined_history =
                                            historical_messages_for_llm.to_vec();
                                        combined_history
                                            .extend_from_slice(messages_for_current_task);
                                        history_for_auto_research_sub_cycle = combined_history;
                                    }

                                    match crate::research::research_cycle::run_research_cycle(
                                        app_config,                         // Added missing app_config argument
                                        auto_research_sub_cycle_llm_client, // Added missing llm_client argument
                                        &research_block_content_string,
                                        &mut temp_sub_research_cycle_messages,
                                        &history_for_auto_research_sub_cycle, // Use determined history
                                        processing_tx_opt,
                                        &format!(
                                            "auto_research_{}",
                                            chrono::Utc::now().timestamp_millis()
                                        ),
                                        &current_path_for_research,
                                        &ordered_files.get_all_labeled_files_map(), // Pass map from OrderedFiles by reference
                                    )
                                    .await
                                    {
                                        Ok(categorized_file_paths_result) => {
                                            if !categorized_file_paths_result
                                                .all_discovered
                                                .is_empty()
                                            {
                                                // Add to ordered_files directly
                                                ordered_files
                                                    .add_categorized_files_from_research(
                                                        &categorized_file_paths_result,
                                                    )
                                                    .await;
                                                ordered_files.apply_research_categorization(
                                                    &categorized_file_paths_result,
                                                );

                                                // Send AutoResearchUpdate signal for immediate TUI update
                                                if let Some(tx) = processing_tx_opt.as_ref() {
                                                    if tx
                                                        .send(
                                                            ProcessingSignal::AutoResearchUpdate {
                                                                categorized_files:
                                                                    categorized_file_paths_result
                                                                        .clone(),
                                                            },
                                                        )
                                                        .await
                                                        .is_err()
                                                    {
                                                        warn!("Failed to send AutoResearchUpdate signal for LLM-decided research");
                                                    }
                                                }

                                                let (_, new_files_added_paths_display) =
                                                    process_discovered_paths(
                                                        // _ is newly_read_map
                                                        categorized_file_paths_result
                                                            .all_discovered
                                                            .clone(),
                                                        &current_path_for_research,
                                                    )
                                                    .await;

                                                (format!("Automatic research found and added new files to context: {}.", new_files_added_paths_display.join(", ")), Some(categorized_file_paths_result))
                                            } else {
                                                // Still send update even if no files
                                                if let Some(tx) = processing_tx_opt.as_ref() {
                                                    if tx
                                                        .send(
                                                            ProcessingSignal::AutoResearchUpdate {
                                                                categorized_files:
                                                                    categorized_file_paths_result
                                                                        .clone(),
                                                            },
                                                        )
                                                        .await
                                                        .is_err()
                                                    {
                                                        warn!("Failed to send AutoResearchUpdate signal for LLM-decided research (no files found)");
                                                    }
                                                }
                                                ("Automatic research completed but found no files.".to_string(), Some(categorized_file_paths_result))
                                            }
                                        }
                                        Err(e) => {
                                            warn!("Automatic research flow failed: {}", e);
                                            (format!("Automatic research failed: {}", e), None)
                                        }
                                    }
                                }
                                Err(parse_err) => {
                                    warn!("Failed to parse research block content: {}. Proceeding without auto-research.", parse_err);
                                    if auto_research_mandatory {
                                        return Err(format!("Mandatory research failed: Could not parse research block: {}", parse_err));
                                    }
                                    (
                                        format!("Failed to parse research block: {}", parse_err),
                                        None,
                                    )
                                }
                            }
                        } else {
                            debug!("LLM confirmed current files are sufficient and no further research is needed.");
                            log::debug!("{}", "-".repeat(70));
                            ("Automatic research determined current files are sufficient or no research directive was given.".to_string(), None)
                        }
                    }
                    Err(e) => {
                        let error_message = format!(
                            "Failed to obtain research instructions from LLM after retries: {}",
                            e
                        );
                        warn!("{}", error_message);
                        if auto_research_mandatory {
                            return Err(format!("Mandatory research failed: {}", error_message));
                        }
                        (error_message, None)
                    }
                }
            }
            Err(e) => {
                warn!("LLM call for auto-research decision failed: {}. Proceeding without auto-research.", e);
                if auto_research_mandatory {
                    return Err(format!(
                        "Mandatory research failed: LLM call for decision failed: {}",
                        e
                    ));
                }
                (format!("LLM call for automatic research decision failed: {}. Proceeding with existing files.", e), None)
            }
        };

    if let Some(tx) = processing_tx_opt.as_ref() {
        if tx.send(ProcessingSignal::AutoResearchEnded).await.is_err() {
            warn!("Failed to send AutoResearchEnded signal");
        }
    }
    // research_outcome_summary is already a tuple (String, Option<CategorizedFilePaths>)
    Ok(research_outcome_summary)
}

/// Helper function to process newly discovered paths from research.
/// Reads files, creates LabeledFile objects, and generates display strings.
async fn process_discovered_paths(
    paths: Vec<PathBuf>,
    current_dir_for_display: &PathBuf,
) -> (HashMap<PathBuf, LabeledFile>, Vec<String>) {
    let mut newly_read_files_map = HashMap::new();
    let mut display_strings = Vec::new();

    for path_abs in paths {
        match crate::files::file_handler::read_file_as_labeled(&path_abs).await {
            // Corrected path
            Ok(labeled_file) => {
                newly_read_files_map.insert(path_abs.clone(), labeled_file);
                let display_path = path_abs
                    .strip_prefix(current_dir_for_display)
                    .unwrap_or(&path_abs)
                    .display()
                    .to_string();
                display_strings.push(display_path);
            }
            Err(e) => {
                warn!(
                    "Failed to read discovered file {} during auto-research processing: {}",
                    path_abs.display(),
                    e
                );
            }
        }
    }
    (newly_read_files_map, display_strings)
}