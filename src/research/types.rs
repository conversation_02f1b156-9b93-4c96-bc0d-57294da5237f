// src/research/types.rs
use crate::llm::ChatMessage;
// use crate::task::Task; // For associating results with a task - Unused
use std::path::PathBuf;

#[derive(<PERSON>bug, <PERSON><PERSON>, <PERSON><PERSON>ult)] // Added Default
pub struct CategorizedFilePaths {
    pub all_discovered: Vec<PathBuf>, // All unique files found by research
    pub context_files: Vec<PathBuf>,  // Subset of all_discovered, for context
    pub edit_files: Vec<PathBuf>,     // Subset of all_discovered, for editing
}

#[derive(Debug, <PERSON><PERSON>)]
pub struct ResearchSuccess {
    pub task_id: String,                         // ID of the research task
    pub categorized_files: CategorizedFilePaths, // Changed from discovered_files
    pub final_messages: Vec<ChatMessage>,        // All messages from the research cycle
}

#[derive(Debug)]
pub enum ResearchSignal {
    TurnComplete {
        task_id: String,
        turn_number: usize,
        messages_this_turn: Vec<ChatMessage>, // Messages added in this specific turn
    },
    ResearchComplete(Result<ResearchSuccess, String>), // String is error message
                                                       // Add other signals like ProgressUpdate if needed for finer-grained feedback
}
