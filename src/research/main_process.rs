// src/research/main_process.rs
// Unused imports removed:
// use crate::interactive::app::InteractiveApp;
// use crate::task::Task;
use crate::files::file_handler::LabeledFile; // Corrected path
use crate::llm::{ChatMessage, LLMClient};
use crate::research::research_cycle::run_research_cycle;
use log::error;
use std::collections::HashMap;
use std::path::PathBuf;
use std::sync::Arc;
// directories::UserDirs removed as expand_tilde is removed

// expand_tilde function removed as it's unused.

use crate::config::AppConfig;
use crate::editor::ProcessingSignal; // Re-using ProcessingSignal for now
use crate::research::types::ResearchSuccess; // ResearchSignal removed as unused here
use tokio::sync::mpsc; // For the sender channel

pub async fn start_research_session(
    app_config: AppConfig, // Takes owned AppConfig
    llm_client: Arc<dyn LLMClient>,
    task_id: String, // ID of the research task object created by the caller
    task_description: String,
    // initial_files_message_content: String, // Removed
    historical_context: Vec<ChatMessage>, // Full history before this research task, made mutable
    operation_signal_tx: mpsc::Sender<ProcessingSignal>,
    current_path: PathBuf, // New parameter: current path from InteractiveApp
    files_already_in_context: HashMap<PathBuf, LabeledFile>, // New parameter
) {
    // This function is now the main async task for research.
    // It doesn't interact with `InteractiveApp` directly.
    // It communicates back via `operation_signal_tx`.

    let mut cycle_messages: Vec<ChatMessage> = Vec::new(); // Messages specific to this research cycle

    // Initial message for the research task itself (already added by caller usually)
    // cycle_messages.push(ChatMessage {
    //     role: ChatRole::User,
    //     content: task_description.clone(),
    //     message_type: MessageType::Text,
    // });

    let research_llm_client: Arc<dyn LLMClient>;
    let research_historical_context: Vec<ChatMessage>;

    if let Some(alias) = &app_config.research_model {
        if alias.to_lowercase() != "none" && alias.to_lowercase() != "default" {
            match crate::config::app_config::setup_specific_llm_client(&app_config, alias) {
                Ok(client) => {
                    log::info!("Using specific research model ('{}') for research task '{}'. History will be isolated.", alias, task_id);
                    research_llm_client = client;
                    research_historical_context = Vec::new(); // Isolated history for specific research model
                }
                Err(e) => {
                    log::warn!("Failed to set up research model '{}': {}. Falling back to default LLM and full context for research task '{}'.", alias, e, task_id);
                    research_llm_client = llm_client.clone(); // Fallback to default
                    research_historical_context = historical_context.clone(); // Use provided full history
                }
            }
        } else {
            log::trace!("Using default LLM for research task '{}' (research_model_alias is '{}'). Full history will be used.", task_id, alias);
            research_llm_client = llm_client.clone(); // Default LLM
            research_historical_context = historical_context.clone(); // Use provided full history
        }
    } else {
        log::trace!("Using default LLM for research task '{}' (research_model_alias not set). Full history will be used.", task_id);
        research_llm_client = llm_client.clone(); // Default LLM
        research_historical_context = historical_context.clone(); // Use provided full history
    }

    match run_research_cycle(
        &app_config,
        research_llm_client, // Use the determined LLM client
        &task_description,
        // &initial_files_message_content, // Removed
        &mut cycle_messages,
        &research_historical_context, // Use the determined historical context
        Some(&operation_signal_tx),   // Pass Option<&Sender>
        &task_id,                     // Pass task_id for signals
        &current_path,                // Pass the current_path to run_research_cycle
        &files_already_in_context,    // Pass files already in context
    )
    .await
    {
        Ok(categorized_file_paths_result) => {
            // Renamed from discovered_file_paths
            // categorized_file_paths_result is already the CategorizedFilePaths struct
            let success_payload = ResearchSuccess {
                task_id: task_id.to_string(),
                categorized_files: categorized_file_paths_result, // Directly use the result
                final_messages: cycle_messages,
            };
            // Send a ProcessingSignal variant that can wrap ResearchSuccess
            // This requires modifying ProcessingSignal or how runner handles it.
            // For now, let's assume we add a ResearchComplete variant to ProcessingSignal.
            if operation_signal_tx
                .send(ProcessingSignal::ResearchComplete(Ok(success_payload)))
                .await
                .is_err()
            {
                error!("Failed to send research success signal to TUI.");
            }
        }
        Err(e) => {
            error!("Research cycle failed: {}", e);
            // Send a ProcessingSignal variant for research failure
            if operation_signal_tx
                .send(ProcessingSignal::ResearchComplete(Err(format!(
                    "Task {} failed: {}",
                    task_id, e
                ))))
                .await
                .is_err()
            {
                error!("Failed to send research failure signal to TUI.");
            }
        }
    }
}

// expand_tilde remains unchanged
