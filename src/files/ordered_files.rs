use crate::files::file_handler::{self, LabeledFile}; // Corrected path
use crate::files::types::FileCategory;
use crate::files::utils::calculate_content_hash;
use crate::llm::{ChatMessage, ChatRole, MessageType};
use crate::research::types::CategorizedFilePaths;
use log::warn; // Removed debug
use std::collections::{HashMap, VecDeque}; // Removed HashSet
use std::path::PathBuf;
use tokio::fs;

// Common directory names to ignore during recursive search
const IGNORED_DIRS: &[&str] = &[
    // VCS
    ".git",
    ".svn",
    ".hg",
    // Build outputs & dependencies
    "target",       // Rust
    "node_modules", // Node.js
    "dist",
    "build",
    "out", // Common build outputs
    "bin",
    "obj",         // Common compiled outputs
    "vendor",      // PHP, Go
    "__pycache__", // Python
    // Virtual environments
    ".venv",
    "venv",
    "env",
    // IDE & Editor specific
    ".vscode",
    ".idea",
    // OS specific
    ".DS_Store",
    // Test & Cache
    ".pytest_cache",
    ".mypy_cache",
    ".ruff_cache",
    "coverage",
    // Framework specific
    ".next",       // Next.js
    ".nuxt",       // Nuxt.js
    ".svelte-kit", // SvelteKit
    // Temporary
    "tmp",
    "temp",
    ".aider",
];

#[derive(Debug, Clone)]
pub struct OrderedFiles {
    context_files: VecDeque<PathBuf>,
    uncategorized_files: VecDeque<PathBuf>,
    editing_files: VecDeque<PathBuf>,
    file_cache: HashMap<PathBuf, LabeledFile>,
    content_hashes: HashMap<PathBuf, String>,
    last_sent_content_hashes: HashMap<PathBuf, String>,
}

impl OrderedFiles {
    pub fn new() -> Self {
        Self {
            context_files: VecDeque::new(),
            uncategorized_files: VecDeque::new(),
            editing_files: VecDeque::new(),
            file_cache: HashMap::new(),
            content_hashes: HashMap::new(),
            last_sent_content_hashes: HashMap::new(),
        }
    }

    fn internal_remove_path_from_list(list: &mut VecDeque<PathBuf>, path: &PathBuf) {
        list.retain(|p| p != path);
    }

    fn internal_add_or_update_file_entry(
        &mut self,
        path: PathBuf, // Takes ownership of path
        labeled_file: LabeledFile,
        category: FileCategory,
    ) {
        let content_hash = calculate_content_hash(&labeled_file);

        Self::internal_remove_path_from_list(&mut self.context_files, &path);
        Self::internal_remove_path_from_list(&mut self.uncategorized_files, &path);
        Self::internal_remove_path_from_list(&mut self.editing_files, &path);

        match category {
            FileCategory::Context => self.context_files.push_back(path.clone()),
            FileCategory::Uncategorized => self.uncategorized_files.push_back(path.clone()),
            FileCategory::Editing => self.editing_files.push_back(path.clone()),
        }

        self.file_cache.insert(path.clone(), labeled_file);
        self.content_hashes.insert(path, content_hash);
    }

    pub async fn add_user_file(&mut self, path_raw: &PathBuf) -> Result<bool, String> {
        let canonical_path = dunce::canonicalize(path_raw)
            .map_err(|e| format!("Failed to canonicalize path {}: {}", path_raw.display(), e))?;

        if self.file_cache.contains_key(&canonical_path) {
            self.refresh_single_file_from_filesystem(&canonical_path)
                .await?;
            return Ok(false);
        }

        let labeled_file = file_handler::read_file_as_labeled(&canonical_path)
            .await
            .map_err(|e| format!("Failed to read file {}: {}", canonical_path.display(), e))?;

        self.internal_add_or_update_file_entry(
            canonical_path,
            labeled_file,
            FileCategory::Uncategorized,
        );
        Ok(true)
    }

    pub async fn add_user_files(
        &mut self,
        paths_raw: &[PathBuf],
    ) -> HashMap<PathBuf, Result<(), String>> {
        let mut results = HashMap::new();
        for path_raw in paths_raw {
            results.insert(
                path_raw.clone(),
                self.add_user_file(path_raw).await.map(|_| ()),
            );
        }
        results
    }
    async fn internal_add_directory_recursively(
        &mut self,
        dir_path: &PathBuf,
        depth: usize,
    ) -> Result<usize, String> {
        const MAX_DEPTH: usize = 10;
        if depth >= MAX_DEPTH {
            return Ok(0);
        }

        let mut files_added_count = 0;
        let mut entries = match fs::read_dir(dir_path).await {
            Ok(entries) => entries,
            Err(e) => {
                log::warn!(
                    "Failed to read directory {}: {}. Skipping.",
                    dir_path.display(),
                    e
                );
                return Ok(0);
            }
        };

        while let Ok(Some(entry)) = entries.next_entry().await {
            let path = entry.path();
            if let Some(name) = path.file_name().and_then(|n| n.to_str()) {
                if IGNORED_DIRS.contains(&name) || (name.starts_with('.') && depth > 0) {
                    continue;
                }
            }

            let metadata = match entry.metadata().await {
                Ok(m) => m,
                Err(_) => continue,
            };

            if metadata.is_dir() {
                files_added_count +=
                    Box::pin(self.internal_add_directory_recursively(&path, depth + 1)).await?;
            } else if metadata.is_file() {
                match self.add_user_file(&path).await {
                    Ok(_) => {
                        files_added_count += 1;
                    }
                    Err(e) => {
                        log::warn!("Could not add file {}: {}", path.display(), e);
                    }
                }
            }
        }
        Ok(files_added_count)
    }

    pub async fn add_path_recursively(&mut self, path_raw: &PathBuf) -> Result<usize, String> {
        let metadata = fs::metadata(path_raw)
            .await
            .map_err(|e| format!("Failed to get metadata for {}: {}", path_raw.display(), e))?;

        if metadata.is_dir() {
            self.internal_add_directory_recursively(path_raw, 0).await
        } else {
            self.add_user_file(path_raw).await.map(|_| 1)
        }
    }
    pub async fn add_categorized_files_from_research(
        &mut self,
        categorized_paths: &CategorizedFilePaths,
    ) -> HashMap<PathBuf, Result<(), String>> {
        let mut results = HashMap::new();
        let all_paths_to_process = categorized_paths
            .context_files
            .iter()
            .map(|p| (p, FileCategory::Context))
            .chain(
                categorized_paths
                    .edit_files
                    .iter()
                    .map(|p| (p, FileCategory::Editing)),
            );

        for (path, category) in all_paths_to_process {
            if self.file_cache.contains_key(path) {
                let refresh_res = self.refresh_single_file_from_filesystem(path).await;
                results.insert(
                    path.clone(),
                    refresh_res
                        .map(|_| ()) // Convert Ok(bool) to Ok(())
                        .map_err(|e| format!("Refresh failed: {}", e)),
                );
                continue;
            }

            match file_handler::read_file_as_labeled(path).await {
                Ok(labeled_file) => {
                    self.internal_add_or_update_file_entry(
                        path.clone(),
                        labeled_file,
                        category.clone(),
                    );
                    results.insert(path.clone(), Ok(()));
                }
                Err(e) => {
                    let err_msg =
                        format!("Failed to read discovered file {}: {}", path.display(), e);
                    results.insert(path.clone(), Err(err_msg));
                }
            }
        }
        results
    }

    pub async fn update_file_after_edit(
        &mut self,
        path: &PathBuf,
        new_content_str: &str,
    ) -> Result<(), String> {
        let canonical_path = if self.file_cache.contains_key(path) {
            path.clone()
        } else {
            dunce::canonicalize(path).map_err(|e| {
                format!(
                    "Failed to canonicalize path for edited file {}: {}",
                    path.display(),
                    e
                )
            })?
        };

        let new_labeled_file = LabeledFile::new(canonical_path.clone(), new_content_str);
        self.internal_add_or_update_file_entry(
            canonical_path,
            new_labeled_file,
            FileCategory::Editing,
        );
        Ok(())
    }

    pub fn create_new_file_in_context(
        &mut self,
        path_raw: &PathBuf, // Assumed to be appropriately resolved by caller
        initial_content_str: &str,
    ) -> Result<(), String> {
        // Assuming path_raw is the absolute path or resolved relative to CWD by caller
        if self.file_cache.contains_key(path_raw) {
            return Err(format!(
                "Attempted to create new file {}, but it's already in context.",
                path_raw.display()
            ));
        }

        let new_labeled_file = LabeledFile::new(path_raw.clone(), initial_content_str);
        self.internal_add_or_update_file_entry(
            path_raw.clone(),
            new_labeled_file,
            FileCategory::Editing,
        );
        Ok(())
    }

    pub fn remove_file(&mut self, path: &PathBuf) -> bool {
        let mut found_and_removed = false;
        if let Some(canonical_path) = self.file_cache.keys().find(|k| *k == path).cloned() {
            Self::internal_remove_path_from_list(&mut self.context_files, &canonical_path);
            Self::internal_remove_path_from_list(&mut self.uncategorized_files, &canonical_path);
            Self::internal_remove_path_from_list(&mut self.editing_files, &canonical_path);
            self.file_cache.remove(&canonical_path);
            self.content_hashes.remove(&canonical_path);
            self.last_sent_content_hashes.remove(&canonical_path);
            found_and_removed = true;
        } else if let Ok(canon_path_attempt) = dunce::canonicalize(path) {
            if self.file_cache.contains_key(&canon_path_attempt) {
                Self::internal_remove_path_from_list(&mut self.context_files, &canon_path_attempt);
                Self::internal_remove_path_from_list(
                    &mut self.uncategorized_files,
                    &canon_path_attempt,
                );
                Self::internal_remove_path_from_list(&mut self.editing_files, &canon_path_attempt);
                self.file_cache.remove(&canon_path_attempt);
                self.content_hashes.remove(&canon_path_attempt);
                self.last_sent_content_hashes.remove(&canon_path_attempt);
                found_and_removed = true;
            }
        }
        found_and_removed
    }

    pub fn remove_path_recursively(&mut self, path_raw: &PathBuf) -> usize {
        let canon_path_base = dunce::canonicalize(path_raw).unwrap_or_else(|_| path_raw.clone());

        if canon_path_base.is_file() {
            if self.remove_file(&canon_path_base) {
                1
            } else {
                0
            }
        } else {
            // Treat as a directory if it is a directory, or if it doesn't exist (might have been a dir)
            let paths_to_remove: Vec<PathBuf> = self
                .file_cache
                .keys()
                .filter(|p| p.starts_with(&canon_path_base))
                .cloned()
                .collect();

            let mut removed_count = 0;
            for path in paths_to_remove {
                if self.remove_file(&path) {
                    removed_count += 1;
                }
            }
            removed_count
        }
    }
    pub fn remove_all_files(&mut self) {
        self.context_files.clear();
        self.uncategorized_files.clear();
        self.editing_files.clear();
        self.file_cache.clear();
        self.content_hashes.clear();
        self.last_sent_content_hashes.clear();
    }

    pub async fn refresh_single_file_from_filesystem(
        &mut self,
        path: &PathBuf,
    ) -> Result<bool, String> {
        if !self.file_cache.contains_key(path) {
            return Err(format!("File {} not in cache to refresh.", path.display()));
        }

        match file_handler::read_file_as_labeled(path).await {
            Ok(updated_labeled_file) => {
                let new_hash = calculate_content_hash(&updated_labeled_file);
                let old_hash = self.content_hashes.get(path).cloned();

                self.file_cache.insert(path.clone(), updated_labeled_file);
                self.content_hashes.insert(path.clone(), new_hash.clone());

                Ok(Some(&new_hash) != old_hash.as_ref())
            }
            Err(e) => {
                warn!(
                    "Failed to refresh file {} from disk (may have been deleted): {}",
                    path.display(),
                    e
                );
                self.remove_file(path);
                Err(format!(
                    "Failed to read file {} for refresh: {}",
                    path.display(),
                    e
                ))
            }
        }
    }

    pub async fn refresh_all_files_from_filesystem(&mut self) -> Vec<PathBuf> {
        let mut changed_or_removed_paths = Vec::new();
        let paths_to_refresh: Vec<PathBuf> = self.file_cache.keys().cloned().collect();

        for path in paths_to_refresh {
            match self.refresh_single_file_from_filesystem(&path).await {
                Ok(changed) if changed => changed_or_removed_paths.push(path),
                Err(_) => changed_or_removed_paths.push(path),
                _ => {}
            }
        }
        changed_or_removed_paths
    }

    pub fn get_files_for_prompt_message(
        &mut self,
        current_display_root: &PathBuf, // New parameter
    ) -> (Option<ChatMessage>, HashMap<PathBuf, String>) {
        let mut files_message_parts: Vec<String> = Vec::new();
        let mut current_task_included_content_map: HashMap<PathBuf, String> = HashMap::new();
        let mut new_last_sent_hashes: HashMap<PathBuf, String> = HashMap::new();

        // Preserve order: context_files, then uncategorized_files, then editing_files.
        // Within each category, files are already ordered by insertion due to VecDeque.
        let ordered_paths_for_prompt: Vec<PathBuf> = self
            .context_files
            .iter()
            .chain(self.uncategorized_files.iter())
            .chain(self.editing_files.iter())
            .cloned()
            .collect();

        // Use passed current_display_root instead of std::env::current_dir()
        // let current_dir_for_display =
        //     std::env::current_dir().unwrap_or_else(|_| PathBuf::from("."));

        for path in ordered_paths_for_prompt {
            if let Some(labeled_file) = self.file_cache.get(&path) {
                let current_content_str = labeled_file.get_original_lines().join("\n");
                current_task_included_content_map.insert(path.clone(), current_content_str.clone());

                let current_hash = self.content_hashes.get(&path).cloned().unwrap_or_else(|| {
                    // Should not happen if file is in cache
                    warn!(
                        "Content hash missing for cached file: {}. Recalculating.",
                        path.display()
                    );
                    calculate_content_hash(labeled_file)
                });
                new_last_sent_hashes.insert(path.clone(), current_hash.clone());

                // Always include the file content. The check for changed content is removed.
                let display_path = path
                    .strip_prefix(current_display_root) // Use current_display_root
                    .unwrap_or(&path)
                    .display();

                files_message_parts.push(format!(
                    "\n--- file: {} ---\n{}",
                    display_path,
                    labeled_file.get_numbered_lines_string()
                ));
            }
        }

        self.last_sent_content_hashes = new_last_sent_hashes; // Still update this to reflect what was sent

        if !files_message_parts.is_empty() {
            // Use a single, constant, and simpler intro message.
            let intro_message = "The following files are relevant to the task. Please review their content carefully:";
            files_message_parts.insert(0, intro_message.to_string());
            (
                Some(ChatMessage {
                    role: ChatRole::User,
                    content: files_message_parts.join("\n"),
                    message_type: MessageType::Text,
                }),
                current_task_included_content_map,
            )
        } else {
            (None, current_task_included_content_map)
        }
    }

    pub fn get_paths_for_ui_display(&self) -> Vec<PathBuf> {
        let mut display_paths = Vec::new();
        let mut temp_list: Vec<PathBuf>;

        temp_list = self.context_files.iter().cloned().collect();
        temp_list.sort();
        display_paths.extend(temp_list);

        temp_list = self.uncategorized_files.iter().cloned().collect();
        temp_list.sort();
        display_paths.extend(temp_list);

        temp_list = self.editing_files.iter().cloned().collect();
        temp_list.sort();
        display_paths.extend(temp_list);

        display_paths
    }

    pub fn get_labeled_file(&self, path: &PathBuf) -> Option<&LabeledFile> {
        self.file_cache.get(path)
    }

    pub fn get_all_labeled_files_map(&self) -> &HashMap<PathBuf, LabeledFile> {
        &self.file_cache
    }

    /// Returns paths in the order: context, uncategorized, editing. Preserves internal deque order.
    pub fn get_paths_for_prompt_generation(&self) -> Vec<PathBuf> {
        let mut paths = Vec::new();
        paths.extend(self.context_files.iter().cloned());
        paths.extend(self.uncategorized_files.iter().cloned());
        paths.extend(self.editing_files.iter().cloned());
        paths
    }

    pub fn is_empty(&self) -> bool {
        self.file_cache.is_empty()
    }

    pub fn len(&self) -> usize {
        self.file_cache.len()
    }

    pub fn apply_research_categorization(&mut self, categorized_paths: &CategorizedFilePaths) {
        let mut paths_to_move: Vec<(PathBuf, FileCategory)> = Vec::new();

        for path in &categorized_paths.context_files {
            if self.uncategorized_files.contains(path) || self.editing_files.contains(path) {
                paths_to_move.push((path.clone(), FileCategory::Context));
            }
        }
        for path in &categorized_paths.edit_files {
            if self.uncategorized_files.contains(path) || self.context_files.contains(path) {
                paths_to_move.push((path.clone(), FileCategory::Editing));
            }
        }

        for (path, new_category) in paths_to_move {
            if let Some(labeled_file) = self.file_cache.get(&path).cloned() {
                self.internal_add_or_update_file_entry(path, labeled_file, new_category);
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    // LabeledFile import removed as it's unused in this test module directly
    use crate::research::types::CategorizedFilePaths;
    use std::fs;
    use tempfile::tempdir;

    async fn create_temp_file(dir: &tempfile::TempDir, name: &str, content: &str) -> PathBuf {
        let path = dir.path().join(name);
        fs::write(&path, content).unwrap();
        dunce::canonicalize(path).unwrap()
    }

    #[tokio::test]
    async fn test_add_user_file_new() {
        let mut ordered_files = OrderedFiles::new();
        let temp_dir = tempdir().unwrap();
        let file_path = create_temp_file(&temp_dir, "test.txt", "content").await;

        let result = ordered_files.add_user_file(&file_path).await;
        assert!(result.is_ok());
        assert!(result.unwrap()); // true for new file
        assert_eq!(ordered_files.len(), 1);
        assert!(ordered_files.uncategorized_files.contains(&file_path));
        assert!(ordered_files.get_labeled_file(&file_path).is_some());
    }

    #[tokio::test]
    async fn test_add_user_file_existing_refreshes() {
        let mut ordered_files = OrderedFiles::new();
        let temp_dir = tempdir().unwrap();
        let file_path = create_temp_file(&temp_dir, "test.txt", "initial content").await;

        ordered_files.add_user_file(&file_path).await.unwrap();
        assert_eq!(
            ordered_files
                .get_labeled_file(&file_path)
                .unwrap()
                .get_original_lines(),
            &vec!["initial content"]
        );

        // Modify file content
        fs::write(&file_path, "updated content").unwrap();

        let result = ordered_files.add_user_file(&file_path).await;
        assert!(result.is_ok());
        assert!(!result.unwrap()); // false for existing file
        assert_eq!(ordered_files.len(), 1); // Still 1 file
        assert_eq!(
            ordered_files
                .get_labeled_file(&file_path)
                .unwrap()
                .get_original_lines(),
            &vec!["updated content"]
        );
    }

    #[tokio::test]
    async fn test_update_file_after_edit() {
        let mut ordered_files = OrderedFiles::new();
        let temp_dir = tempdir().unwrap();
        let file_path = create_temp_file(&temp_dir, "test.txt", "original").await;
        ordered_files.add_user_file(&file_path).await.unwrap(); // Adds to uncategorized

        ordered_files
            .update_file_after_edit(&file_path, "edited content")
            .await
            .unwrap();
        assert!(ordered_files.editing_files.contains(&file_path));
        assert!(!ordered_files.uncategorized_files.contains(&file_path));
        assert_eq!(
            ordered_files
                .get_labeled_file(&file_path)
                .unwrap()
                .get_original_lines(),
            &vec!["edited content"]
        );
    }

    #[tokio::test]
    async fn test_create_new_file_in_context() {
        let mut ordered_files = OrderedFiles::new();
        let temp_dir = tempdir().unwrap();
        let new_file_path = temp_dir.path().join("new_file.txt"); // Path doesn't exist yet

        ordered_files
            .create_new_file_in_context(&new_file_path, "new file content")
            .unwrap();
        assert!(ordered_files.editing_files.contains(&new_file_path));
        assert_eq!(
            ordered_files
                .get_labeled_file(&new_file_path)
                .unwrap()
                .get_original_lines(),
            &vec!["new file content"]
        );
    }

    #[tokio::test]
    async fn test_remove_file() {
        let mut ordered_files = OrderedFiles::new();
        let temp_dir = tempdir().unwrap();
        let file_path = create_temp_file(&temp_dir, "test.txt", "content").await;
        ordered_files.add_user_file(&file_path).await.unwrap();
        assert_eq!(ordered_files.len(), 1);

        assert!(ordered_files.remove_file(&file_path));
        assert_eq!(ordered_files.len(), 0);
        assert!(!ordered_files.remove_file(&file_path)); // Already removed
    }

    #[tokio::test]
    async fn test_refresh_single_file_from_filesystem() {
        let mut ordered_files = OrderedFiles::new();
        let temp_dir = tempdir().unwrap();
        let file_path = create_temp_file(&temp_dir, "test.txt", "initial").await;
        ordered_files.add_user_file(&file_path).await.unwrap();

        fs::write(&file_path, "updated").unwrap();
        let changed = ordered_files
            .refresh_single_file_from_filesystem(&file_path)
            .await
            .unwrap();
        assert!(changed);
        assert_eq!(
            ordered_files
                .get_labeled_file(&file_path)
                .unwrap()
                .get_original_lines(),
            &vec!["updated"]
        );

        let not_changed = ordered_files
            .refresh_single_file_from_filesystem(&file_path)
            .await
            .unwrap();
        assert!(!not_changed);
    }

    #[tokio::test]
    async fn test_get_files_for_prompt_message_ordering_and_updates() {
        let mut ordered_files = OrderedFiles::new();
        let temp_dir = tempdir().unwrap();
        let test_current_dir = temp_dir.path().to_path_buf(); // Simulate app's current path

        let context_file_path = create_temp_file(&temp_dir, "context.txt", "context v1").await;
        let uncat_file_path = create_temp_file(&temp_dir, "uncat.txt", "uncat v1").await;
        let edit_file_path = create_temp_file(&temp_dir, "edit.txt", "edit v1").await;

        // Add files (they will go to Uncategorized initially)
        ordered_files
            .add_user_file(&context_file_path)
            .await
            .unwrap();
        ordered_files.add_user_file(&uncat_file_path).await.unwrap();
        ordered_files.add_user_file(&edit_file_path).await.unwrap();

        // Manually move them to their intended categories for this test
        let cf_labeled = ordered_files
            .file_cache
            .get(&context_file_path)
            .unwrap()
            .clone();
        let ef_labeled = ordered_files
            .file_cache
            .get(&edit_file_path)
            .unwrap()
            .clone();
        ordered_files.internal_add_or_update_file_entry(
            context_file_path.clone(),
            cf_labeled,
            FileCategory::Context,
        );
        ordered_files.internal_add_or_update_file_entry(
            edit_file_path.clone(),
            ef_labeled,
            FileCategory::Editing,
        );
        // uncat_file_path remains Uncategorized

        // First call
        let (msg_opt1, map1) = ordered_files.get_files_for_prompt_message(&test_current_dir);
        assert!(msg_opt1.is_some());
        let content1 = msg_opt1.unwrap().content;
        assert!(content1.contains("context.txt")); // No more (context)
        assert!(content1.contains("uncat.txt")); // No more (context)
        assert!(content1.contains("edit.txt")); // No more (context)
        assert!(content1.contains("context v1"));
        assert!(content1.contains("uncat v1"));
        assert!(content1.contains("edit v1"));
        assert_eq!(map1.len(), 3);

        // Second call, no changes to files, but all files should still be sent.
        let (msg_opt2, _map2) = ordered_files.get_files_for_prompt_message(&test_current_dir);
        assert!(msg_opt2.is_some()); // All files should be resent
        let content2 = msg_opt2.unwrap().content;
        assert!(content2.contains("context.txt"));
        assert!(content2.contains("uncat.txt"));
        assert!(content2.contains("edit.txt"));

        // Modify uncat_file_path
        fs::write(&uncat_file_path, "uncat v2").unwrap();
        ordered_files
            .refresh_single_file_from_filesystem(&uncat_file_path)
            .await
            .unwrap();

        // Third call, uncat_file_path changed
        let (msg_opt3, _map3) = ordered_files.get_files_for_prompt_message(&test_current_dir);
        assert!(msg_opt3.is_some());
        let content3 = msg_opt3.unwrap().content;
        assert!(content3.contains("context.txt")); // Still sent
        assert!(content3.contains("uncat.txt")); // Still sent, with new content
        assert!(content3.contains("uncat v2"));
        assert!(content3.contains("edit.txt")); // Still sent

        // Simulate editing 'context_file_path', moving it to 'Editing'
        ordered_files
            .update_file_after_edit(&context_file_path, "context v2 (edited)")
            .await
            .unwrap();
        assert!(ordered_files.editing_files.contains(&context_file_path));
        assert_eq!(ordered_files.editing_files.back(), Some(&context_file_path)); // Moved to end of editing

        // Fourth call, context_file_path changed and moved
        let (msg_opt4, _map4) = ordered_files.get_files_for_prompt_message(&test_current_dir);
        assert!(msg_opt4.is_some());
        let content4 = msg_opt4.unwrap().content;
        assert!(content4.contains("uncat.txt")); // Still sent
        assert!(content4.contains("context.txt")); // Still sent, with new content
        assert!(content4.contains("context v2 (edited)"));
        assert!(content4.contains("edit.txt")); // Still sent

        // Check order in prompt message (Context, Uncategorized, Editing)
        // Current state:
        // Context: (empty)
        // Uncategorized: uncat_file_path
        // Editing: edit_file_path, context_file_path (in this order, new at end)
        // Expected order in message: uncat.txt, then edit.txt, then context.txt
        let uncat_pos = content4.find("uncat.txt").unwrap();
        let edit_pos = content4.find("edit.txt").unwrap();
        let context_pos = content4.find("context.txt").unwrap();
        assert!(uncat_pos < edit_pos);
        assert!(edit_pos < context_pos);
    }

    #[tokio::test]
    async fn test_apply_research_categorization() {
        let mut ordered_files = OrderedFiles::new();
        let temp_dir = tempdir().unwrap();
        let file1 = create_temp_file(&temp_dir, "file1.txt", "content1").await;
        let file2 = create_temp_file(&temp_dir, "file2.txt", "content2").await;
        let file3 = create_temp_file(&temp_dir, "file3.txt", "content3").await;

        ordered_files.add_user_file(&file1).await.unwrap(); // Uncategorized
        ordered_files.add_user_file(&file2).await.unwrap(); // Uncategorized
        ordered_files.add_user_file(&file3).await.unwrap(); // Uncategorized

        let categorized = CategorizedFilePaths {
            all_discovered: vec![file1.clone(), file2.clone()], // Not used by apply_research_categorization directly
            context_files: vec![file1.clone()],
            edit_files: vec![file2.clone()],
        };

        ordered_files.apply_research_categorization(&categorized);

        assert!(ordered_files.context_files.contains(&file1));
        assert!(!ordered_files.uncategorized_files.contains(&file1));
        assert!(ordered_files.editing_files.contains(&file2));
        assert!(!ordered_files.uncategorized_files.contains(&file2));
        assert!(ordered_files.uncategorized_files.contains(&file3)); // file3 remains uncategorized
    }
}