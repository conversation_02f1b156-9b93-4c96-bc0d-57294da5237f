use crate::editor::types::ProposedEdit;
use std::path::PathBuf;
use tokio::fs::File;
use tokio::io::{AsyncReadExt, BufReader}; // Added for apply_edits_to_lines

#[derive(<PERSON>bu<PERSON>, <PERSON>lone)]
pub struct LabeledFile {
    pub path: PathBuf,
    lines: Vec<String>,
}

impl LabeledFile {
    /// Creates a new LabeledFile from a path and raw content string.
    pub(crate) fn new(path: PathBuf, content: &str) -> Self {
        // Made pub(crate)
        LabeledFile {
            path,
            lines: content.lines().map(String::from).collect(),
        }
    }

    /// Returns the original lines of the file.
    pub fn get_original_lines(&self) -> &Vec<String> {
        &self.lines
    }


    /// Returns a single string where each line is not prefixed with a line number.
    pub fn get_original_lines_string(&self) -> String {
        self.get_original_lines().join("\n")
    }

    /// Returns a vector of strings, each prefixed with its line number.
    /// e.g., ["1 | line one", "2 | line two"]
    pub fn get_numbered_lines_vec(&self) -> Vec<String> {
        self.lines
            .iter()
            .enumerate()
            .map(|(i, line)| format!("{} | {}", i + 1, line))
            .collect()
    }

    /// Returns a single string where each line is prefixed with its line number,
    /// suitable for use in prompts.
    /// e.g., "1 | line one\n2 | line two"
    pub fn get_numbered_lines_string(&self) -> String {
        self.get_numbered_lines_vec().join("\n")
    }
}

/// Asynchronously reads a file and returns it as a `LabeledFile`.
pub async fn read_file_as_labeled(file_path: &PathBuf) -> Result<LabeledFile, std::io::Error> {
    // Made pub
    let file = File::open(file_path).await?;
    let mut buf_reader = BufReader::new(file);
    let mut content = String::new();
    buf_reader.read_to_string(&mut content).await?;

    Ok(LabeledFile::new(file_path.clone(), &content))
}



/// Applies a list of proposed edits to a vector of original lines.
/// Edits should be for a single file and are best applied if sorted by start_line descending.
pub fn apply_edits_to_lines(
    // Made pub
    original_lines: &mut Vec<String>,
    edits_for_file: &[ProposedEdit],
) -> Result<usize, String> {
    let mut total_lines_inserted = 0;

    // It's crucial to sort edits by start_line in descending order
    // so that changes don't affect the line numbers of subsequent edits
    // that appear earlier in the file.
    // For 0-0 (new file) edits, their start_line is 0, so they will naturally be
    // processed first if sorting descending. This is fine.
    let mut sorted_edits = edits_for_file.to_vec(); // Clone to sort
    sorted_edits.sort_by(|a, b| b.target.start_line.cmp(&a.target.start_line));

    for edit in sorted_edits {
        let new_code_lines: Vec<String> = edit.new_code.lines().map(String::from).collect();

        if edit.target.start_line == 0 && edit.target.end_line == 0 {
            // This is a new file edit. original_lines should be empty.
            if !original_lines.is_empty() {
                // This case should ideally not happen if logic is correct elsewhere,
                // as a new file would start with empty original_lines.
                return Err(format!(
                    "Attempted to apply new file edit (0-0) to non-empty content for file {}",
                    edit.target.file_path.display()
                ));
            }
            original_lines.clear();
            original_lines.extend(new_code_lines.iter().cloned());
            total_lines_inserted += new_code_lines.len();
        } else {
            // Regular edit for existing lines
            let start_idx = edit.target.start_line.saturating_sub(1); // 1-indexed to 0-indexed
            let end_idx = edit.target.end_line.saturating_sub(1); // 1-indexed to 0-indexed

            // `start_idx` and `end_idx` (from the surrounding scope) are the 0-indexed requested start and end lines.
            // `final_end_idx` will be the (potentially clamped) 0-indexed end line for the splice.
            let mut final_end_idx = end_idx; // `end_idx` here is the 0-indexed requested_end_idx

            // Validation logic for start_idx and clamping/validation for end_idx
            if original_lines.is_empty() {
                // If original_lines is empty, start_idx and requested_end_idx (which is `end_idx` here) must both be 0.
                // This corresponds to a 1-1 edit on an empty file, i.e., insertion.
                if start_idx != 0 || end_idx != 0 {
                    // `end_idx` is the 0-indexed requested_end_idx
                    return Err(format!(
                        "Invalid line range for edit target {} on empty file: start_line {}, end_line {}. Expected 1-1 for insertion.",
                        edit.target.file_path.display(), edit.target.start_line, edit.target.end_line
                    ));
                }
                // final_end_idx is already 0 if end_idx (requested_end_idx) was 0.
            } else {
                // original_lines is not empty.
                // Validate start_idx against file bounds. edit.target.start_line is 1-indexed.
                // Edits with start_line == 0 are for new files and handled by the other branch of the main if/else.
                // So, edit.target.start_line >= 1 here.
                if start_idx >= original_lines.len() {
                    // start_idx is 0-indexed
                    return Err(format!(
                        "Start line ({}) is out of bounds for file {} which has {} lines.",
                        edit.target.start_line,
                        edit.target.file_path.display(),
                        original_lines.len()
                    ));
                }

                // Clamp requested_end_idx (which is `end_idx` here) if it's beyond the end of the file.
                if end_idx >= original_lines.len() {
                    // `end_idx` is the 0-indexed requested_end_idx
                    final_end_idx = original_lines.len() - 1;
                    // Log only if clamping occurred. Clamping to same value is not interesting if file was empty and end_idx was 0.
                    // However, we are in the `else` block where `original_lines` is not empty.
                    if end_idx != final_end_idx {
                        log::debug!(
                            "Clamped end_line for edit target {}: original end_line {} (0-indexed {}) to actual last line {} (0-indexed {}). File has {} lines.",
                            edit.target.file_path.display(),
                            edit.target.end_line,     // 1-indexed original requested
                            end_idx,                  // 0-indexed original requested
                            original_lines.len(),     // 1-indexed actual last line
                            final_end_idx,            // 0-indexed clamped
                            original_lines.len()
                        );
                    }
                }
                // Now, final_end_idx is clamped. Validate start_idx against final_end_idx.
                if start_idx > final_end_idx {
                    return Err(format!(
                        "Invalid line range for edit target {}: start_line {} (0-indexed {}) is greater than final end_line {} (0-indexed {}). Original requested end_line was {}.",
                        edit.target.file_path.display(),
                        edit.target.start_line, start_idx,
                        final_end_idx + 1, final_end_idx, // Convert final_end_idx to 1-based for message
                        edit.target.end_line
                    ));
                }
            }

            total_lines_inserted += new_code_lines.len();

            if original_lines.is_empty() {
                // This implies start_idx=0, final_end_idx=0 (as validated above)
                original_lines.extend(new_code_lines.iter().cloned());
            } else {
                // original_lines is not empty, start_idx and final_end_idx are valid, and start_idx <= final_end_idx.
                original_lines.splice(start_idx..=final_end_idx, new_code_lines);
            }
        }
    }
    Ok(total_lines_inserted)
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::editor::types::{EditTarget, ProposedEdit}; // Updated path
    use std::path::PathBuf;

    #[test]
    fn test_labeled_file_new_empty_content() {
        let lf = LabeledFile::new(PathBuf::from("test.txt"), "");
        assert_eq!(lf.path.to_str().unwrap(), "test.txt");
        assert_eq!(lf.lines.len(), 0); // Expect 0 lines for empty content based on observed behavior
                                       // assert_eq!(lf.lines[0], ""); // This would panic if lines.len() is 0
        assert_eq!(lf.get_numbered_lines_vec(), Vec::<String>::new()); // Expect empty vector
        assert_eq!(lf.get_numbered_lines_string(), ""); // Expect empty string
    }

    #[test]
    fn test_labeled_file_single_line() {
        let lf = LabeledFile::new(PathBuf::from("test.txt"), "Hello");
        assert_eq!(lf.lines, vec!["Hello"]);
        assert_eq!(lf.get_numbered_lines_vec(), vec!["1 | Hello"]);
        assert_eq!(lf.get_numbered_lines_string(), "1 | Hello");
    }

    #[test]
    fn test_labeled_file_multiple_lines() {
        let content = "Hello\nWorld\nRust";
        let lf = LabeledFile::new(PathBuf::from("test.txt"), content);
        assert_eq!(lf.lines, vec!["Hello", "World", "Rust"]);
        let expected_vec = vec!["1 | Hello", "2 | World", "3 | Rust"];
        let expected_str = "1 | Hello\n2 | World\n3 | Rust";
        assert_eq!(lf.get_numbered_lines_vec(), expected_vec);
        assert_eq!(lf.get_numbered_lines_string(), expected_str);
    }

    #[test]
    fn test_labeled_file_with_trailing_newline() {
        let content = "Hello\nWorld\n";
        let lf = LabeledFile::new(PathBuf::from("test.txt"), content);
        // Adjust expectation based on observed behavior (trailing empty string might be omitted)
        assert_eq!(lf.lines, vec!["Hello".to_string(), "World".to_string()]);
        let expected_vec = vec!["1 | Hello".to_string(), "2 | World".to_string()];
        let expected_str = "1 | Hello\n2 | World";
        assert_eq!(lf.get_numbered_lines_vec(), expected_vec);
        assert_eq!(lf.get_numbered_lines_string(), expected_str);
    }

    // Tokio test for async function read_file_as_labeled
    #[tokio::test]
    async fn test_read_file_as_labeled_integration() {
        // Create a temporary file
        let file_path = PathBuf::from("temp_test_file.txt");
        tokio::fs::write(&file_path, "Line 1\nLine 2")
            .await
            .unwrap();

        let labeled_file = read_file_as_labeled(&file_path).await.unwrap();
        assert_eq!(labeled_file.path, file_path);
        assert_eq!(
            labeled_file.get_original_lines(),
            &vec!["Line 1".to_string(), "Line 2".to_string()]
        );
        assert_eq!(
            labeled_file.get_numbered_lines_string(),
            "1 | Line 1\n2 | Line 2"
        );

        // Clean up
        tokio::fs::remove_file(&file_path).await.unwrap();
    }

    #[test]
    fn test_apply_edits_single_edit() {
        let mut lines = vec![
            "line 1".to_string(),
            "line 2".to_string(),
            "line 3".to_string(),
            "line 4".to_string(),
        ];
        let edits = [ProposedEdit {
            target: EditTarget {
                file_path: PathBuf::from("test.txt"),
                start_line: 2,
                end_line: 3,
            },
            new_code: "new line A\nnew line B".to_string(),
            old_code: "".to_string(),
        }];
        let lines_inserted = apply_edits_to_lines(&mut lines, &edits).unwrap();
        assert_eq!(lines_inserted, 2); // 2 new lines inserted
        assert_eq!(
            lines,
            vec![
                "line 1".to_string(),
                "new line A".to_string(),
                "new line B".to_string(),
                "line 4".to_string(),
            ]
        );
    }

    #[test]
    fn test_apply_edits_multiple_non_overlapping_sorted_desc() {
        let mut lines = vec![
            "alpha".to_string(),
            "beta".to_string(),
            "gamma".to_string(),
            "delta".to_string(),
            "epsilon".to_string(),
            "zeta".to_string(),
        ];
        // Edits can be provided in any order; apply_edits_to_lines sorts them internally.
        let edits = vec![
            ProposedEdit {
                // Edit for lines 1-2
                target: EditTarget {
                    file_path: PathBuf::from("test.txt"),
                    start_line: 1,
                    end_line: 2,
                },
                new_code: "new A\nnew B".to_string(),
                old_code: "".to_string(),
            },
            ProposedEdit {
                // Edit for lines 5-6
                target: EditTarget {
                    file_path: PathBuf::from("test.txt"),
                    start_line: 5,
                    end_line: 6,
                },
                new_code: "new E\nnew Z".to_string(),
                old_code: "".to_string(),
            },
        ];

        let lines_inserted = apply_edits_to_lines(&mut lines, &edits).unwrap();
        assert_eq!(lines_inserted, 4);
        assert_eq!(
            lines,
            vec![
                "new A".to_string(),
                "new B".to_string(),
                "gamma".to_string(),
                "delta".to_string(),
                "new E".to_string(),
                "new Z".to_string(),
            ]
        );
    }

    #[test]
    fn test_apply_edits_replace_all() {
        let mut lines = vec!["old 1".to_string(), "old 2".to_string()];
        let edits = [ProposedEdit {
            target: EditTarget {
                file_path: PathBuf::from("test.txt"),
                start_line: 1,
                end_line: 2,
            },
            new_code: "completely new".to_string(),
            old_code: "".to_string(),
        }];
        let lines_inserted = apply_edits_to_lines(&mut lines, &edits).unwrap();
        assert_eq!(lines_inserted, 1);
        assert_eq!(lines, vec!["completely new".to_string(),]);
    }

    #[test]
    fn test_apply_edits_insert_without_deletion() {
        let mut lines = vec![
            "line A".to_string(),
            "line B".to_string(),
            "line C".to_string(),
        ];
        let edits = [ProposedEdit {
            target: EditTarget {
                file_path: PathBuf::from("test.txt"),
                start_line: 2,
                end_line: 2,
            }, // Replace line B
            new_code: "inserted 1\ninserted 2\ninserted 3".to_string(),
            old_code: "".to_string(),
        }];
        let lines_inserted = apply_edits_to_lines(&mut lines, &edits).unwrap();
        assert_eq!(lines_inserted, 3);
        assert_eq!(
            lines,
            vec![
                "line A".to_string(),
                "inserted 1".to_string(),
                "inserted 2".to_string(),
                "inserted 3".to_string(),
                "line C".to_string(),
            ]
        );
    }

    #[test]
    fn test_apply_edits_delete_without_insertion() {
        // Replacing multiple lines with one or zero
        let mut lines = vec![
            "one".to_string(),
            "two".to_string(),
            "three".to_string(),
            "four".to_string(),
        ];
        let edits = [ProposedEdit {
            target: EditTarget {
                file_path: PathBuf::from("test.txt"),
                start_line: 2,
                end_line: 3,
            },
            new_code: "".to_string(), // Delete lines 2 and 3
            old_code: "".to_string(),
        }];
        let lines_inserted = apply_edits_to_lines(&mut lines, &edits).unwrap();
        assert_eq!(lines_inserted, 0); // 0 new lines inserted
        assert_eq!(lines, vec!["one".to_string(), "four".to_string()]);
    }



    #[test]
    fn test_apply_edits_new_file_0_0() {
        let mut lines = Vec::new(); // Start with empty lines for a new file
        let edits = [ProposedEdit {
            target: EditTarget {
                file_path: PathBuf::from("new_file.txt"),
                start_line: 0,
                end_line: 0,
            },
            new_code: "This is line 1.\nThis is line 2.".to_string(),
            old_code: "".to_string(),
        }];
        let lines_inserted = apply_edits_to_lines(&mut lines, &edits).unwrap();
        assert_eq!(lines_inserted, 2);
        assert_eq!(
            lines,
            vec!["This is line 1.".to_string(), "This is line 2.".to_string()]
        );
    }

    #[test]
    fn test_apply_edits_to_empty_existing_file_line_1_1() {
        let mut lines = Vec::new(); // Simulating an empty existing file's content
        let edits = [ProposedEdit {
            target: EditTarget {
                file_path: PathBuf::from("empty_existing.txt"),
                start_line: 1, // Targeting line 1 of an empty file
                end_line: 1,   // (effectively inserting at the beginning)
            },
            new_code: "First line".to_string(),
            old_code: "".to_string(),
        }];
        // The splice logic for start_idx=0, end_idx=0 on empty lines should handle this.
        let lines_inserted = apply_edits_to_lines(&mut lines, &edits).unwrap();
        assert_eq!(lines_inserted, 1);
        assert_eq!(lines, vec!["First line".to_string()]);
    }
}
