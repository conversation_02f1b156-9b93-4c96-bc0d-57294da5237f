use crate::files::file_handler::LabeledFile; // Corrected path
use sha2::{Digest, Sha256};

/// Calculates a SHA256 hash of a LabeledFile's content.
/// The content is derived by joining the original lines with a newline character.
pub(crate) fn calculate_content_hash(labeled_file: &LabeledFile) -> String {
    let mut hasher = Sha256::new();
    hasher.update(labeled_file.get_original_lines().join("\n").as_bytes());
    format!("{:x}", hasher.finalize())
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::files::file_handler::LabeledFile;
    use std::path::PathBuf;

    #[test]
    fn test_calculate_content_hash_empty() {
        let lf = LabeledFile::new(PathBuf::from("test.txt"), "");
        let hash = calculate_content_hash(&lf);
        // SHA256 hash of an empty string
        assert_eq!(
            hash,
            "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"
        );
    }

    #[test]
    fn test_calculate_content_hash_simple() {
        let lf = LabeledFile::new(PathBuf::from("test.txt"), "hello");
        let hash = calculate_content_hash(&lf);
        // SHA256 hash of "hello"
        assert_eq!(
            hash,
            "2cf24dba5fb0a30e26e83b2ac5b9e29e1b161e5c1fa7425e73043362938b9824"
        );
    }

    #[test]
    fn test_calculate_content_hash_multiline() {
        let lf = LabeledFile::new(PathBuf::from("test.txt"), "hello\nworld");
        let hash = calculate_content_hash(&lf);
        // SHA256 hash of "hello\nworld"
        assert_eq!(
            hash,
            "26c60a61d01db5836ca70fefd44a6a016620413c8ef5f259a6c5612d4f79d3b8"
        );
    }

    #[test]
    fn test_calculate_content_hash_consistency() {
        let lf1 = LabeledFile::new(PathBuf::from("test.txt"), "hello\nworld");
        let lf2 = LabeledFile::new(PathBuf::from("test.txt"), "hello\nworld");
        assert_eq!(calculate_content_hash(&lf1), calculate_content_hash(&lf2));
    }

    #[test]
    fn test_calculate_content_hash_difference() {
        let lf1 = LabeledFile::new(PathBuf::from("test.txt"), "hello\nworld");
        let lf2 = LabeledFile::new(PathBuf::from("test.txt"), "hello\nWorld"); // Case difference
        assert_ne!(calculate_content_hash(&lf1), calculate_content_hash(&lf2));
    }
}
