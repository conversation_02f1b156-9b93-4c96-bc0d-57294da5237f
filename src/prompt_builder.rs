use crate::config::AppConfig;
use crate::editor::types::{EditTarget, ProposedEdit};
use crate::files::file_handler::LabeledFile; // Corrected path

use crate::block_parsing::processor::BlockExpectation;
use crate::block_parsing::traits::ParsableBlock; // Added for ParsableBlock trait

/// Constructs the instructional part of the prompt for the LLM to plan edits.
/// This prompt guides the LLM on how to think and what block formats to use.
/// File contents are provided in a separate, preceding message.
///
/// The prompt includes:
/// - The user's high-level instruction.
/// - Specific instructions for each expected block type.
pub fn construct_editor_plan_prompt(
    app_config: &AppConfig,
    user_instruction: &str,
    task_info: Option<&str>, // New parameter
    _available_files_for_validation_only: &[LabeledFile],
    _expectations: &[BlockExpectation], // These are the expectations for parsing the LLM's *response* to this prompt.
    research_option_available: bool,    // New parameter
) -> String {
    let mut effective_user_instruction = user_instruction.to_string();
    crate::llm::apply_prefix_based_on_mode(
        &mut effective_user_instruction,
        app_config,
        crate::config::app_config::NoThinkMode::EditingPlanning,
    );

    let mut prompt = String::new();
    let _research_parser = crate::block_parsing::research_block::ResearchBlockParser;

    prompt.push_str(
        "You are an expert AI developer, renowned for your meticulous planning and precise code editing strategies. \
        File contents have been provided in a preceding message. Please refer to them carefully."
    );

    if let Some(info) = task_info {
        if !info.trim().is_empty() {
            prompt.push_str(&format!(
                "\n\nImportant task context (e.g., previous errors or analysis):\n---\n{}\n---",
                info.trim()
            ));
        }
    }

    prompt.push_str(&format!(
        "\n\nBased on the current user's request: \"{}\"\n",
        effective_user_instruction
    ));

    prompt.push_str(
        "Your task is to devise a comprehensive and exact plan to fulfill this request. \
        You MUST provide a detailed, step-by-step thought process in plain English. \
        Explain your reasoning, the changes you intend to make, and why these changes are necessary. \
        Refer to specific file paths and line numbers from the provided context. \
        Do NOT include any code snippets or code examples within your explanatory text."
    );

    let research_parser = crate::block_parsing::research_block::ResearchBlockParser; // Used for example text

    if research_option_available {
        prompt.push_str(
            "\n\nIf you have sufficient information and all necessary files are in context, you MUST provide all `range` blocks required to implement your plan. \
            Each `range` block defines a segment of code to be replaced or a new file to be created. \
            For each range of line numbers that you provide an explanation for how to edit, you MUST provide a `range` block right after. \
            Ideally ranges inside of `range` blocks are maximum of up to 30 lines at a time. Larger are allowed when replacing a whole function/struct/type/etc, but aim for multiple smaller ranges whenever sensible."
        );
        prompt.push_str("\n\nExample of a `range` block for editing an existing file:\n```range\npath/to/file.ext\nSTART_LINE-END_LINE\n```");
        prompt.push_str("\nExample of a `range` block for creating a new file:\n```range\npath/to/new_file.ext\n0-0\n```");
        prompt.push_str("\n\nAlternatively, if you need context from files not currently provided, or if you are unsure about the contents or existence of a file, you MUST NOT attempt to create or guess its content. Instead, you MUST request further information by providing a single `research` block. If you provide a `research` block, any `range` blocks will be ignored for this turn. The `research` block should contain a 1-3 paragraph explanation for an AI research assistant, detailing what files or information needs to be found.");
        prompt.push_str(&format!(
            "\nExample of a `research` block:\n{}\n",
            research_parser.example_prompt_text()
        ));
        prompt.push_str("\n\nTo summarize: EITHER provide all necessary `range` blocks OR a single `research` block. Ensure you use the exact file paths as provided in the input files when referring to existing files for `range` blocks.");
    } else {
        prompt.push_str(
            "\n\nYou have requested research multiple times or research is otherwise unavailable. The `research` block option is no longer available for this task. \
            You MUST now provide all necessary `range` blocks required to implement your plan based on the currently available files. \
            Each `range` block defines a segment of code to be replaced or a new file to be created. \
            For each range of line numbers that you provide an explanation for how to edit, you MUST provide a `range` block right after. \
            Ideally ranges inside of `range` blocks are maximum of up to 30 lines at a time. Larger are allowed when replacing a whole function/struct/type/etc, but aim for multiple smaller ranges whenever sensible."
        );
        prompt.push_str("\n\nExample of a `range` block for editing an existing file:\n```range\npath/to/file.ext\nSTART_LINE-END_LINE\n```");
        prompt.push_str("\nExample of a `range` block for creating a new file:\n```range\npath/to/new_file.ext\n0-0\n```");
        prompt.push_str("\n\nTo summarize: You MUST provide all necessary `range` blocks. Ensure you use the exact file paths as provided in the input files when referring to existing files for `range` blocks.");
    }
    prompt.push_str("\nIt is crucial that these blocks are accurate and cover all necessary modifications. Neither your thought process nor your range block (or any other code blocks) should contain any lines of code at all.");

    prompt
}

// New prompt for pre-planning research decision
pub fn construct_pre_planning_research_decision_prompt(
    app_config: &AppConfig,
    user_instruction: &str,
    task_info: Option<&str>,                             // New parameter
    available_files_for_validation_only: &[LabeledFile], // Changed parameter name for clarity
) -> String {
    // Convert LabeledFile slice to Vec<String> of path displays
    let current_file_paths_in_context: Vec<String> = available_files_for_validation_only
        .iter()
        .map(|f| f.path.display().to_string())
        .collect();

    // Call the main auto_research_decision_prompt constructor
    // The user_instruction will have its prefix applied by construct_auto_research_decision_prompt
    // using NoThinkMode::ResearchAutoDecision.
    let base_prompt = crate::research::prompts::construct_auto_research_decision_prompt(
        app_config,
        user_instruction, // Pass the user_instruction directly
        task_info,        // Pass task_info
        &current_file_paths_in_context,
        "Directory listing previously considered; focus on the task and provided files.", // Placeholder for ls output
        false, // auto_research_mandatory is false for this decision point
    );

    // Prepend the introductory text
    format!(
        "The initial automatic research phase has completed.\n\n{}",
        base_prompt
    )
}

#[cfg(test)]
mod tests {
    // get_default_expectations function removed as it's unused.
}

/// Constructs the prompt for the LLM to provide specific code for an edit target.
///
/// The prompt includes:
/// - The user's original high-level instruction.
/// - The file path being edited.
/// - The specific line-numbered content of the current range from the relevant file.
/// - An instruction asking the LLM to provide the replacement code for that specific range.
pub fn construct_specific_edit_prompt(
    app_config: &AppConfig, // Added app_config
    overall_user_prompt: &str,
    target_file: &LabeledFile,
    edit_target: &EditTarget, // Contains 1-indexed lines, or 0-0 for new file
    is_new_file: bool,
) -> Result<String, String> {
    let mut effective_overall_user_prompt = overall_user_prompt.to_string();
    crate::llm::apply_prefix_based_on_mode(
        &mut effective_overall_user_prompt,
        app_config,
        crate::config::app_config::NoThinkMode::EditingCoding,
    );

    let mut lines_to_display = Vec::new();
    let original_lines = target_file.get_original_lines(); // 0-indexed

    if !is_new_file {
        // Logic for existing files (start_line >= 1)
        if edit_target.start_line == 0 {
            // Should not happen if is_new_file is false
            return Err("Internal error: start_line is 0 for non-new file.".to_string());
        }
        if edit_target.start_line > edit_target.end_line {
            return Err(format!(
                "Start line ({}) cannot be greater than end line ({}).",
                edit_target.start_line, edit_target.end_line
            ));
        }

        let start_idx = edit_target.start_line - 1; // Convert start_line to 0-indexed

        if original_lines.is_empty() && start_idx != 0 {
            return Err(format!(
                "Start line ({}) is out of bounds for empty file {}. Expected start_line 1 for editing an empty existing file (though this case is unusual).",
                edit_target.start_line,
                target_file.path.display()
            ));
        } else if !original_lines.is_empty() && start_idx >= original_lines.len() {
            return Err(format!(
                "Start line ({}) is out of bounds for file {} which has {} lines.",
                edit_target.start_line,
                target_file.path.display(),
                original_lines.len()
            ));
        }

        if !original_lines.is_empty() {
            let requested_end_idx = edit_target.end_line - 1; // Convert to 0-indexed
            let effective_end_idx = std::cmp::min(requested_end_idx, original_lines.len() - 1);

            if start_idx <= effective_end_idx {
                for i in start_idx..=effective_end_idx {
                    lines_to_display.push(original_lines[i].clone());
                }
            }
        }
    }
    // If is_new_file is true, lines_to_display remains empty, which is correct.
    let mut prompt = String::new();
    prompt.push_str(&format!(
        "User's current overall goal: \"{}\"\n",
        effective_overall_user_prompt // Use potentially modified prompt
    ));

    if is_new_file {
        prompt.push_str(&format!(
            "File: {} (newly created)\n",
            target_file.path.display()
        ));
    } else {
        prompt.push_str(&format!("File: {}\n", target_file.path.display()));
        prompt.push_str(&format!(
            "Original lines to replace (lines {}-{}):\n",
            edit_target.start_line, edit_target.end_line
        ));
        prompt.push_str("```\n");
        prompt.push_str(&lines_to_display.join("\n"));
        prompt.push_str("\n```\n");
    }

    let action_description = if is_new_file {
        "the full content for this new file"
    } else {
        &format!(
            "the new code for lines {}-{}",
            edit_target.start_line, edit_target.end_line
        )
    };

    prompt.push_str(&format!(
        "Please provide {} in a single `replace` block. The new code in this single block should *only* contain the content to be inserted into the file.\n\
        CRITICAL: The code you provide in the 'replace' block MUST be syntactically correct and complete. Pay close attention to matching all opening and closing delimiters (e.g., `()`, `{{}}`, `[]`), quotes, and other syntax. Your response will be used directly to replace code, so any syntax errors, especially unclosed delimiters, will cause the entire operation to fail. Double-check your generated code for correctness before responding.\n\
        Do *not* include line numbers in your response, nor any other code blocks other than a single `replace` block.\n\
        You may respond with 1-3 lines of thinking out loud first, and then after respond *only* with the new code enclosed in a single 'replace' code block like this:\n```replace\n<your new code here without any line numbers>\n```\n\
        Do not include any other text, explanation, or any ```range``` blocks or any other blocks.",
        action_description
    ));

    if app_config
        .advanced_language_features
        .contains(&"rust-delimiter-checking".to_string())
        && target_file
            .path
            .extension()
            .map(|e| e == "rs")
            .unwrap_or(false)
    {
        prompt.push_str("\n Remember, as you write code in the `replace` block, you MUST carefully maintain the net balance of delimiters ('(', '[', '{{') as the original code you are replacing. In other words, the net difference between opening and closing delimiters of each type must match so you do not introduce syntax errors by leaving code around the replace block with hanging delimeters.");
    }

    Ok(prompt)
}

/// Constructs the prompt for the LLM to summarize its planned edits.
/// Always prepends /no_think.
pub fn construct_plan_summary_prompt(
    _app_config: &AppConfig, // app_config is passed for consistency, but no_think is always applied
    parsed_targets: &[EditTarget],
) -> String {
    let mut prompt_lines = vec!["You have planned the following edits:".to_string()];
    for target in parsed_targets {
        let line_info = if target.start_line == 0 && target.end_line == 0 {
            "(New File)".to_string()
        } else {
            format!("Lines: {}-{}", target.start_line, target.end_line)
        };
        prompt_lines.push(format!(
            "- File: {}, {}",
            target.file_path.display(),
            line_info
        ));
    }
    prompt_lines.push("\nPlease provide a very brief (1-3 sentences) summary of these changes. Format your response *only* as a 'summary' code block, like this:".to_string());
    prompt_lines.push("```summary\nYour brief summary here.\n```".to_string());
    prompt_lines.push("Do not include any other text or explanation. Ensure the summary is worded to focus on what will be done in the edits.".to_string());
    let mut prompt = prompt_lines.join("\n");
    crate::llm::apply_prefix_based_on_mode(
        &mut prompt,
        _app_config,
        crate::config::app_config::NoThinkMode::Summaries,
    );
    prompt
}

/// Constructs the prompt for the LLM to summarize the task and the edits performed.
pub fn construct_task_summary_prompt(
    _app_config: &AppConfig,
    proposed_edits: &[ProposedEdit],
    initial_user_prompt: &str,
) -> String {
    let mut summary_prompt_lines = vec![format!(
        "The user's most recent request was: \"{}\"",
        initial_user_prompt
    )];

    if proposed_edits.is_empty() {
        summary_prompt_lines.push("No edits were made in response to this request.".to_string());
    } else {
        summary_prompt_lines
            .push("The following edits were performed to address the request:".to_string());
        let mut edit_details = Vec::new();
        for edit in proposed_edits {
            let action = if edit.target.start_line == 0 && edit.target.end_line == 0 {
                "Created new file"
            } else {
                "Modified"
            };
            edit_details.push(format!(
                "- {action} '{}' (lines {}-{}).",
                edit.target.file_path.display(),
                edit.target.start_line,
                edit.target.end_line
            ));
        }
        summary_prompt_lines.push(edit_details.join("\n"));
    }
    summary_prompt_lines.push("\nBased on the user's request and the edits performed, provide a concise summary. Start with one paragraph summarizing the user's request. Follow this with 1-3 paragraphs outlining the goal and nature of the edits made. Format your entire response *only* as a 'summary' code block.".to_string());
    summary_prompt_lines.push("Example:\n```summary\nUser wanted to refactor X. Edits involved modifying function Y in file Z.rs and creating a new helper module utils.rs to extract common logic. The primary goal was to improve modularity and readability.\n```".to_string());
    summary_prompt_lines.push(
        "Do not include any other text or explanation outside the summary block.".to_string(),
    );
    let mut prompt = summary_prompt_lines.join("\n");
    crate::llm::apply_prefix_based_on_mode(
        &mut prompt,
        _app_config,
        crate::config::app_config::NoThinkMode::Summaries,
    );
    prompt
}

#[cfg(test)]
mod specific_edit_prompt_tests {
    use super::*;
    use crate::config::app_config::{
        AutoExpertMode, AutoExpertSwitch, AutoResearchMode, NoThinkMode,
    };
    use crate::editor::types::EditTarget;
    use crate::files::file_handler::LabeledFile; // Corrected path
    use std::collections::HashSet;
    use std::path::PathBuf;

    // Helper for LabeledFile from previous tests
    fn new_test_labeled_file(path_str: &str, content: &str) -> LabeledFile {
        let path = PathBuf::from(path_str);
        // Use the pub(crate) constructor
        LabeledFile::new(path, content)
    }

    #[test]
    fn test_construct_specific_edit_prompt_valid() {
        let mut app_config = crate::config::AppConfig {
            http_api_port: 8080,
            user_prompt: String::new(), // Not directly used by this function, but part of AppConfig
            file_paths: Vec::new(),
            provider: "openai".to_string(),
            model: "gpt-4o".to_string(),
            effective_provider_api_key: String::new(), // Changed field name
            provider_url: String::new(),
            results_input_file: None,
            research_model: None, // Changed from research_model_alias
            results_input: None,
            task_info: None,
            no_think: HashSet::new(), // Default for this test (empty set)
            results_output_mode: crate::config::app_config::ResultsOutputMode::None,
            results_output_file: None,
            log_level: crate::logger::LogLevel::Off,
            exit_on_success: false, // Replaced interactive
            timestamps: false,
            restore_previous_session_on_startup: false,
            restore_previous_session_models: false,
            auto_test_command: String::new(),
            auto_test_toggle: false,
            max_task_retries: 99999,
            default_model: "test_default_model".to_string(),
            models_list: None,
            auto_research_mode: AutoResearchMode::True,
            auto_expert_switch: AutoExpertSwitch::False,
            expert_model: None, // Changed from expert_model_alias
            auto_expert_mode: AutoExpertMode::Planning,
            notification_command: String::new(),
            forced_research_for_next_cycle: None, // Added field
            decision_model_auto_research_loop_max: 2, // Added default
            expert_model_auto_research_loop_max: 2, // Added default
            retry_model: None,                    // Changed from retry_model_alias
            retry_model_max_attempts: 3,          // Added default
            summary_model: None,                  // Changed from summary_model_alias
            decision_model: None,                 // Added missing field
            advanced_language_features: HashSet::new(), // Added missing field
            sounds_master_volume: 25,
            submit_sound: crate::config::app_config::SubmitSoundConfig::Disabled,
        };

        let user_prompt = "Fix the loop";
        let file_content = "line1\nline2\nline3\nline4\nline5";
        let labeled_file = new_test_labeled_file("src/test.rs", file_content);
        let edit_target = EditTarget {
            file_path: PathBuf::from("src/test.rs"),
            start_line: 2,
            end_line: 4,
        };

        // Test with no_think = empty set (should default to /think for EditingCoding)
        let prompt_empty_set = construct_specific_edit_prompt(
            &app_config,
            user_prompt,
            &labeled_file,
            &edit_target,
            false,
        )
        .unwrap();
        assert!(prompt_empty_set.contains("User's current overall goal: \"/think Fix the loop\""));

        // Test with no_think = {None}
        app_config.no_think.clear();
        app_config.no_think.insert(NoThinkMode::None);
        let prompt_none_mode = construct_specific_edit_prompt(
            &app_config,
            user_prompt,
            &labeled_file,
            &edit_target,
            false,
        )
        .unwrap();
        assert!(prompt_none_mode.contains("User's current overall goal: \"Fix the loop\""));
        assert!(!prompt_none_mode.contains("/no_think ") && !prompt_none_mode.contains("/think "));

        // Test with no_think = {All}
        app_config.no_think.clear();
        app_config.no_think.insert(NoThinkMode::All);
        let prompt_all_mode = construct_specific_edit_prompt(
            &app_config,
            user_prompt,
            &labeled_file,
            &edit_target,
            false,
        )
        .unwrap();
        assert!(prompt_all_mode.contains("User's current overall goal: \"/no_think Fix the loop\""));

        // Test with no_think = {EditingCoding}
        app_config.no_think.clear();
        app_config.no_think.insert(NoThinkMode::EditingCoding);
        let prompt_editing_coding_mode = construct_specific_edit_prompt(
            &app_config,
            user_prompt,
            &labeled_file,
            &edit_target,
            false,
        )
        .unwrap();
        assert!(prompt_editing_coding_mode
            .contains("User's current overall goal: \"/no_think Fix the loop\""));

        // Test with no_think = {EditingPlanning} (EditingCoding should get /think)
        app_config.no_think.clear();
        app_config.no_think.insert(NoThinkMode::EditingPlanning);
        let prompt_editing_planning_mode = construct_specific_edit_prompt(
            &app_config,
            user_prompt,
            &labeled_file,
            &edit_target,
            false,
        )
        .unwrap();
        assert!(prompt_editing_planning_mode
            .contains("User's current overall goal: \"/think Fix the loop\""));

        // Common structural assertions (using prompt_empty_set as an example)
        let prompt = prompt_empty_set;
        assert!(prompt.contains("File: src/test.rs"));
        assert!(prompt.contains("Original lines to replace (lines 2-4):"));
        // Expecting raw lines without numbers
        assert!(prompt.contains("```\nline2\nline3\nline4\n```"));
        assert!(prompt.contains(&format!(
            "Please provide {} in a single `replace` block",
            "the new code for lines 2-4"
        )));
        assert!(prompt.contains("Do *not* include line numbers")); // Made assertion more lenient
        assert!(prompt.contains("<your new code here without any line numbers>"));
    }

    #[test]
    fn test_specific_edit_prompt_end_line_past_eof_is_allowed() {
        let app_config = get_default_app_config_for_prompt_tests(); // Helper for default config
        let user_prompt = "test";
        let labeled_file = new_test_labeled_file("file.txt", "line1\nline2"); // 2 lines
        let edit_target = EditTarget {
            file_path: PathBuf::from("file.txt"),
            start_line: 2, // Valid start
            end_line: 5,   // Past EOF
        };
        let result = construct_specific_edit_prompt(
            &app_config,
            user_prompt,
            &labeled_file,
            &edit_target,
            false,
        );
        assert!(result.is_ok(), "Expected Ok, got Err: {:?}", result.err());
        let prompt = result.unwrap();
        assert!(prompt.contains("Original lines to replace (lines 2-5):"));
        assert!(prompt.contains("```\nline2\n```"));
    }

    #[test]
    fn test_specific_edit_prompt_start_line_past_eof_errors() {
        let app_config = get_default_app_config_for_prompt_tests();
        let user_prompt = "test";
        let labeled_file = new_test_labeled_file("file.txt", "line1\nline2"); // 2 lines
        let edit_target = EditTarget {
            file_path: PathBuf::from("file.txt"),
            start_line: 3, // Invalid start
            end_line: 5,
        };
        let result = construct_specific_edit_prompt(
            &app_config,
            user_prompt,
            &labeled_file,
            &edit_target,
            false,
        );
        assert!(result.is_err());
        assert_eq!(
            result.unwrap_err(),
            "Start line (3) is out of bounds for file file.txt which has 2 lines."
        );
    }

    #[test]
    fn test_specific_edit_prompt_new_file_0_0() {
        let app_config = get_default_app_config_for_prompt_tests();
        let user_prompt = "Create a new configuration file";
        let new_file_path = PathBuf::from("new_config.toml");
        let labeled_file = new_test_labeled_file(new_file_path.to_str().unwrap(), "");
        let edit_target = EditTarget {
            file_path: new_file_path.clone(),
            start_line: 0,
            end_line: 0,
        };

        let prompt = construct_specific_edit_prompt(
            &app_config,
            user_prompt,
            &labeled_file,
            &edit_target,
            true,
        )
        .unwrap();

        assert!(prompt.contains(&format!(
            "User's current overall goal: \"/think {}\"",
            user_prompt
        )));
        assert!(prompt.contains("File: new_config.toml (newly created)"));
        assert!(!prompt.contains("Original lines to replace")); // This phrase should not be there for new files
        assert!(prompt.contains(&format!(
            "Please provide {} in a single `replace` block",
            "the full content for this new file"
        )));
    }

    #[test]
    fn test_specific_edit_prompt_existing_empty_file_edit_at_line_1() {
        let app_config = get_default_app_config_for_prompt_tests();
        let user_prompt = "Add initial content";
        let labeled_file = new_test_labeled_file("empty_existing.txt", ""); // Existing but empty
        let edit_target = EditTarget {
            file_path: PathBuf::from("empty_existing.txt"),
            start_line: 1,
            end_line: 1,
        };
        let result = construct_specific_edit_prompt(
            &app_config,
            user_prompt,
            &labeled_file,
            &edit_target,
            false,
        );
        assert!(result.is_ok(), "Expected Ok, got Err: {:?}", result.err());
        let prompt = result.unwrap();
        assert!(prompt.contains("File: empty_existing.txt"));
        assert!(prompt.contains("Original lines to replace (lines 1-1):"));
        assert!(prompt.contains("```\n\n```"));
        assert!(prompt.contains(&format!(
            "Please provide {} in a single `replace` block",
            "the new code for lines 1-1"
        )));
    }

    #[test]
    fn test_specific_edit_prompt_start_greater_than_end() {
        let app_config = get_default_app_config_for_prompt_tests();
        let user_prompt = "test";
        let labeled_file = new_test_labeled_file("file.txt", "line1\nline2\nline3");
        let edit_target = EditTarget {
            file_path: PathBuf::from("file.txt"),
            start_line: 3,
            end_line: 1,
        };
        let result = construct_specific_edit_prompt(
            &app_config,
            user_prompt,
            &labeled_file,
            &edit_target,
            false,
        );
        assert!(result.is_err());
        assert_eq!(
            result.unwrap_err(),
            "Start line (3) cannot be greater than end line (1)."
        );
    }

    // Helper for default AppConfig in prompt tests
    fn get_default_app_config_for_prompt_tests() -> AppConfig {
        AppConfig {
            http_api_port: 8080,
            user_prompt: String::new(),
            file_paths: Vec::new(),
            provider: "openai".to_string(),
            model: "gpt-4o".to_string(),
            effective_provider_api_key: String::new(), // Changed field name
            provider_url: String::new(),
            results_input_file: None,
            results_input: None,
            task_info: None,
            no_think: HashSet::new(), // Use HashSet for tests
            results_output_mode: crate::config::app_config::ResultsOutputMode::None,
            results_output_file: None,
            log_level: crate::logger::LogLevel::Off,
            exit_on_success: false, // Replaced interactive
            timestamps: false,
            restore_previous_session_on_startup: false,
            restore_previous_session_models: false,
            auto_test_command: String::new(),
            auto_test_toggle: false,
            max_task_retries: 99999,
            default_model: "test_default_model".to_string(),
            models_list: None,
            auto_research_mode: AutoResearchMode::True,
            auto_expert_switch: AutoExpertSwitch::False,
            expert_model: None, // Changed from expert_model_alias
            auto_expert_mode: AutoExpertMode::Planning,
            notification_command: String::new(),
            forced_research_for_next_cycle: None, // Added field
            decision_model_auto_research_loop_max: 2, // Added default
            expert_model_auto_research_loop_max: 2, // Added default
            retry_model: None,                    // Changed from retry_model_alias
            retry_model_max_attempts: 3,          // Added default
            summary_model: None,                  // Changed from summary_model_alias
            research_model: None,                 // Changed from research_model_alias
            decision_model: None,                 // Added missing field
            advanced_language_features: HashSet::new(), // Added missing field
            sounds_master_volume: 25,
            submit_sound: crate::config::app_config::SubmitSoundConfig::Disabled,
        }
    }
}

#[cfg(test)]
mod summary_prompt_tests {
    use super::*;
    use crate::config::app_config::{
        AutoExpertMode, AutoExpertSwitch, AutoResearchMode, NoThinkMode,
    }; // Added more imports
    use crate::editor::types::{EditTarget, ProposedEdit}; // Updated path
    use std::collections::HashSet; // Added HashSet
    use std::path::PathBuf;

    fn get_default_app_config_for_summary_tests() -> AppConfig {
        AppConfig {
            user_prompt: "Initial user prompt for testing.".to_string(),
            file_paths: Vec::new(),
            default_model: "test_default_model".to_string(),
            provider: "test_provider".to_string(),
            model: "test_model".to_string(),
            effective_provider_api_key: String::new(), // Changed field name
            provider_url: String::new(),
            models_list: None,
            results_input_file: None,
            results_input: None,
            task_info: None,
            no_think: HashSet::new(), // This will be ignored by the prompt functions themselves
            results_output_mode: crate::config::app_config::ResultsOutputMode::None,
            results_output_file: None,
            log_level: crate::logger::LogLevel::Off,
            exit_on_success: false, // Replaced interactive
            timestamps: false,
            restore_previous_session_on_startup: false,
            restore_previous_session_models: false,
            auto_test_command: String::new(),
            auto_test_toggle: false,
            max_task_retries: 99999,
            auto_research_mode: AutoResearchMode::True,
            auto_expert_switch: AutoExpertSwitch::False,
            expert_model: None, // Changed from expert_model_alias
            auto_expert_mode: AutoExpertMode::Planning,
            notification_command: String::new(),
            forced_research_for_next_cycle: None, // Added field
            decision_model_auto_research_loop_max: 2, // Added default
            expert_model_auto_research_loop_max: 2, // Added default
            retry_model: None,                    // Changed from retry_model_alias
            retry_model_max_attempts: 3,          // Added default
            summary_model: None,                  // Changed from summary_model_alias
            research_model: None,                 // Changed from research_model_alias
            decision_model: None,                 // Added missing field
            advanced_language_features: HashSet::new(), // Added missing field
            http_api_port: 8080,
            sounds_master_volume: 25,
            submit_sound: crate::config::app_config::SubmitSoundConfig::Disabled,
        }
    }

    #[test]
    fn test_construct_plan_summary_prompt_prefixing() {
        let mut app_config = get_default_app_config_for_summary_tests();
        let targets = vec![EditTarget {
            file_path: PathBuf::from("file1.rs"),
            start_line: 1,
            end_line: 10,
        }];

        // Default (empty set) -> /think
        let prompt_default = construct_plan_summary_prompt(&app_config, &targets);
        assert!(prompt_default.starts_with("/think "));

        // {None} -> no prefix
        app_config.no_think.insert(NoThinkMode::None);
        let prompt_none = construct_plan_summary_prompt(&app_config, &targets);
        assert!(!prompt_none.starts_with("/think ") && !prompt_none.starts_with("/no_think "));
        app_config.no_think.clear();

        // {All} -> /no_think
        app_config.no_think.insert(NoThinkMode::All);
        let prompt_all = construct_plan_summary_prompt(&app_config, &targets);
        assert!(prompt_all.starts_with("/no_think "));
        app_config.no_think.clear();

        // {Summaries} -> /no_think
        app_config.no_think.insert(NoThinkMode::Summaries);
        let prompt_summaries = construct_plan_summary_prompt(&app_config, &targets);
        assert!(prompt_summaries.starts_with("/no_think "));
        app_config.no_think.clear();

        // {EditingCoding} (Summaries not set) -> /think
        app_config.no_think.insert(NoThinkMode::EditingCoding);
        let prompt_editing_coding = construct_plan_summary_prompt(&app_config, &targets);
        assert!(prompt_editing_coding.starts_with("/think "));
    }

    #[test]
    fn test_construct_task_summary_prompt_prefixing() {
        let mut app_config = get_default_app_config_for_summary_tests();
        let edits = vec![ProposedEdit {
            target: EditTarget {
                file_path: PathBuf::from("file1.rs"),
                start_line: 5,
                end_line: 15,
            },
            new_code: "some content".to_string(),
            old_code: "".to_string(),
        }];
        let initial_user_prompt = "Refactor the system.";

        // Default (empty set) -> /think
        let prompt_default =
            construct_task_summary_prompt(&app_config, &edits, initial_user_prompt);
        assert!(prompt_default.starts_with("/think "));

        // {None} -> no prefix
        app_config.no_think.insert(NoThinkMode::None);
        let prompt_none = construct_task_summary_prompt(&app_config, &edits, initial_user_prompt);
        assert!(!prompt_none.starts_with("/think ") && !prompt_none.starts_with("/no_think "));
        app_config.no_think.clear();

        // {All} -> /no_think
        app_config.no_think.insert(NoThinkMode::All);
        let prompt_all = construct_task_summary_prompt(&app_config, &edits, initial_user_prompt);
        assert!(prompt_all.starts_with("/no_think "));
        app_config.no_think.clear();

        // {Summaries} -> /no_think
        app_config.no_think.insert(NoThinkMode::Summaries);
        let prompt_summaries =
            construct_task_summary_prompt(&app_config, &edits, initial_user_prompt);
        assert!(prompt_summaries.starts_with("/no_think "));
        app_config.no_think.clear();

        // {ResearchPlanning} (Summaries not set) -> /think
        app_config.no_think.insert(NoThinkMode::ResearchPlanning);
        let prompt_research =
            construct_task_summary_prompt(&app_config, &edits, initial_user_prompt);
        assert!(prompt_research.starts_with("/think "));
    }
}
