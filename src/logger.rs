use log::LevelFilter; // Level, chrono::Local, colored, std::io::Write, EnvLoggerBuilder removed
use std::env;
use tui_logger;

#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq)]
pub enum LogLevel {
    Off,   // Level 0
    Info,  // Level 1 (maps to log::LevelFilter::Info)
    Debug, // Level 2 (maps to log::LevelFilter::Debug)
    Trace, // Level 3 (maps to log::LevelFilter::Trace)
}

// Convert LogLevel enum to log::LevelFilter
impl From<LogLevel> for LevelFilter {
    fn from(level: LogLevel) -> Self {
        match level {
            LogLevel::Off => LevelFilter::Off,
            LogLevel::Info => LevelFilter::Info,
            LogLevel::Debug => LevelFilter::Debug,
            LogLevel::Trace => LevelFilter::Trace,
        }
    }
}

// Convert u8 to LogLevel (used by AppConfig)
impl From<u8> for LogLevel {
    fn from(value: u8) -> Self {
        match value {
            0 => LogLevel::Off,
            1 => LogLevel::Info,
            2 => LogLevel::Debug,
            3 => LogLevel::Trace,
            _ => {
                log::warn!("Invalid log level value '{}', defaulting to Info.", value);
                LogLevel::Info
            }
        }
    }
}

// Convert LogLevel to u8 (used for LLMSpinner if it expects u8)
impl From<LogLevel> for u8 {
    fn from(level: LogLevel) -> Self {
        match level {
            LogLevel::Off => 0,
            LogLevel::Info => 1,
            LogLevel::Debug => 2,
            LogLevel::Trace => 3,
        }
    }
}

pub fn init_logger(log_level: LogLevel, _timestamps_enabled: bool) {
    // interactive_mode removed, timestamps_enabled prefixed
    let app_level_filter = LevelFilter::from(log_level);
    let crate_name = env!("CARGO_PKG_NAME");

    let dep_general_filter = LevelFilter::Warn;
    let hyper_silence_filter = LevelFilter::Off;

    let rust_log_directive = format!(
        "{crate_name}={app_level},hyper=off,reqwest=off,h2={dep_warn},rustls={dep_warn},mio={dep_warn}",
        app_level = app_level_filter.to_string().to_lowercase(),
        dep_warn = dep_general_filter.to_string().to_lowercase()
    );
    env::set_var("RUST_LOG", &rust_log_directive);

    // Always initialize for TUI mode
    tui_logger::init_logger(app_level_filter)
        .expect("Failed to initialize tui_logger for interactive mode");

    let tui_filter_string = format!(
        "{crate_name}={app_level},hyper=off,reqwest=off,h2={dep_warn},rustls={dep_warn},mio={dep_warn}",
            app_level = app_level_filter.to_string().to_lowercase(),
            dep_warn = dep_general_filter.to_string().to_lowercase()
        );
    tui_logger::set_env_filter_from_string(&tui_filter_string);

    // Whitelist our app (this might be redundant if tui_filter_string already sets it, but ensures it)
    tui_logger::set_level_for_target(crate_name, app_level_filter);

    // Explicitly silence hyper and reqwest (top-level)
    tui_logger::set_level_for_target("hyper", hyper_silence_filter);
    tui_logger::set_level_for_target("reqwest", hyper_silence_filter);

    // Approach 4: Target more specific hyper and reqwest submodules
    tui_logger::set_level_for_target("hyper::client", hyper_silence_filter);
    tui_logger::set_level_for_target("hyper::client::conn", hyper_silence_filter);
    tui_logger::set_level_for_target("hyper::proto", hyper_silence_filter);
    tui_logger::set_level_for_target("hyper::proto::h1", hyper_silence_filter);
    tui_logger::set_level_for_target("hyper::proto::h2", hyper_silence_filter);
    tui_logger::set_level_for_target("reqwest::connect", hyper_silence_filter);
    tui_logger::set_level_for_target("reqwest::blocking::client", hyper_silence_filter);

    // Set other dependencies to a general less noisy level
    tui_logger::set_level_for_target("h2", dep_general_filter);
    tui_logger::set_level_for_target("rustls", dep_general_filter);
    tui_logger::set_level_for_target("mio", dep_general_filter);

    // The order of operations (Approach 5) is: init -> set_env_filter_from_string, then specific overrides.
}
