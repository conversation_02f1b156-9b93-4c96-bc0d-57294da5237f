use crate::api::types::APIMessage;
use tokio::sync::mpsc;

mod routes;
mod types;

pub fn start_server(port: u16, sender: mpsc::Sender<APIMessage>) {
    tokio::spawn(async move {
        let app = routes::app(sender);
        let listener = tokio::net::TcpListener::bind(format!("0.0.0.0:{}", port))
            .await
            .unwrap();
        axum::serve(listener, app).await.unwrap();
    });
}
