use crate::api::types::APIMessage;
use axum::http::StatusCode; // Added for explicit response type
use axum::response::Redirect; // Added for redirection
use axum::{
    extract::State,
    routing::{get, post},
    Json, Router,
};
use chrono::{DateTime, Utc};
use utoipa::OpenApi;
use utoipa_swagger_ui::SwaggerUi;

use std::sync::{Arc, Mutex};
use tokio::sync::mpsc;

use super::types::{HealthCheckResponse, SubmitInputRequest};

// Note: The duplicate 'use utoipa::OpenApi;' was implicitly removed by this change block replacing the section.
// If it were elsewhere, a separate block would be needed.

#[derive(OpenApi)]
#[openapi(
    paths(health_check, submit_input),
    components(schemas(HealthCheckResponse, SubmitInputRequest))
)]
pub struct ApiDoc;

// Define a struct for shared application state for better compatibility with utoipa and axum.
#[derive(Clone)]
struct AppHttpState {
    sender: mpsc::Sender<APIMessage>,
    start_time: Arc<Mutex<DateTime<Utc>>>,
}

pub fn app(sender: mpsc::Sender<APIMessage>) -> Router {
    let start_time = Arc::new(Mutex::new(Utc::now()));

    let app_state = AppHttpState { sender, start_time };

    let api_routes = Router::new()
        .route("/health-check", get(health_check))
        .route("/submit-input", post(submit_input));

    let swagger_router =
        SwaggerUi::new("/swagger-ui").url("/api-docs/openapi.json", ApiDoc::openapi());

    // Combine Swagger UI router and API routes router.
    // Apply the state to the merged router.
    Router::new()
        .merge(swagger_router)
        .merge(api_routes)
        .route("/", get(root_redirect)) // Add redirect for root path
        .with_state(app_state)
}

// Redirects the root path to the Swagger UI.
async fn root_redirect() -> Redirect {
    Redirect::permanent("/swagger-ui")
}

#[utoipa::path(
    get,
    path = "/health-check",
    responses(
        (status = 200, description = "Health check successful", body = HealthCheckResponse),
        (status = 500, description = "Internal server error during health check")
    )
)]
pub async fn health_check(
    State(state): State<AppHttpState>,
) -> axum::response::Result<Json<HealthCheckResponse>> {
    let uptime = Utc::now().signed_duration_since(*state.start_time.lock().unwrap());
    Ok(Json(HealthCheckResponse {
        status: "ok".to_string(),
        uptime: uptime.num_seconds(),
    }))
}

#[utoipa::path(
    post,
    path = "/submit-input",
    request_body = SubmitInputRequest,
    responses(
        (status = 202, description = "Input submitted successfully"),
        (status = 500, description = "Internal server error while submitting input")
    )
)]
pub async fn submit_input(
    State(state): State<AppHttpState>,
    Json(payload): Json<SubmitInputRequest>,
) -> StatusCode {
    let msg = APIMessage::SubmitInput(payload.input);
    match state.sender.send(msg).await {
        Ok(_) => StatusCode::ACCEPTED,
        Err(_) => {
            // It's good practice to log the error here if you have a logger available
            // log::error!("Failed to send APIMessage via channel: {}", e);
            StatusCode::INTERNAL_SERVER_ERROR
        }
    }
}
