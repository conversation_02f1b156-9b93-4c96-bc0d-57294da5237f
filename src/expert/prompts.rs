use crate::block_parsing::processor::BlockExpectation;
use crate::block_parsing::range_replace_block::RangeReplaceBlockParser;
use crate::block_parsing::traits::ParsableBlock; // Added import
use crate::config::AppConfig;
use crate::files::file_handler::LabeledFile;

// Prompt for the default LLM to decide if expert help is needed.
pub fn construct_expert_needed_prompt(
    app_config: &AppConfig, // Changed to app_config from _app_config
    user_instruction: &str,
    available_files: &[LabeledFile], // To provide context about current files
) -> String {
    let mut prompt_body = String::new();

    let files_summary = if available_files.is_empty() {
        "No files are currently in context.".to_string()
    } else {
        let mut s = "Current files in context:\n".to_string();
        for (i, lf) in available_files.iter().enumerate() {
            if i < 5 {
                // List up to 5 files
                s.push_str(&format!("- {}\n", lf.path.display()));
            } else {
                s.push_str(&format!(
                    "...and {} more files.\n",
                    available_files.len() - 5
                ));
                break;
            }
        }
        s
    };

    prompt_body.push_str(&format!(
        "You are an AI assistant evaluating the complexity of a user's request.\n\
        User's request: \"{}\"\n\n\
        {}\n\
        Based on this information, do you believe the task is highly complex and would significantly benefit from an expert AI developer taking over the planning phase? \
        The expert AI is specialized in breaking down complex tasks into precise, actionable code edits.\n\
        Respond *only* with a single code block: ```expert\ntrue\n``` if an expert is recommended, or ```expert\nfalse\n``` otherwise. \
        Do not provide any other explanation or text outside this block.",
        user_instruction,
        files_summary
    ));

    crate::llm::apply_prefix_based_on_mode(
        &mut prompt_body,
        app_config,
        crate::config::app_config::NoThinkMode::ExpertAutoDecision,
    );

    prompt_body
}

// Prompt for the expert LLM to perform the planning.
pub fn construct_expert_planning_prompt(
    app_config: &AppConfig,  // User's overall AppConfig
    user_instruction: &str,  // The original user instruction for the task
    task_info: Option<&str>, // New parameter for task context
    _available_files_for_validation_only: &[LabeledFile], // For consistency with construct_initial_prompt
    expectations: &[BlockExpectation], // Expected blocks (primarily range blocks)
    research_option_available: bool,
) -> String {
    let mut effective_user_instruction = user_instruction.to_string();
    let research_parser = crate::block_parsing::research_block::ResearchBlockParser; // Used for example text

    crate::llm::apply_prefix_based_on_mode(
        &mut effective_user_instruction,
        app_config,
        crate::config::app_config::NoThinkMode::ExpertPlanning,
    );

    let mut prompt = String::new();

    if let Some(info) = task_info {
        if !info.trim().is_empty() {
            prompt.push_str(&format!(
                "Important Task Context (may include previous errors or analysis):\n---\n{}\n---\n\n",
                info.trim()
            ));
        }
    }

    prompt.push_str(
        "You are an expert AI developer, renowned for your meticulous planning and precise code editing strategies. \
        File contents have been provided in a preceding message. Please refer to them carefully."
    );

    prompt.push_str(&format!(
        "\n\nBased on the current user's request: \"{}\"\n",
        effective_user_instruction
    ));

    prompt.push_str(
        "Your task is to devise a comprehensive and exact plan to fulfill this request. \
        You MUST provide a detailed, step-by-step thought process. \
        Explain your reasoning, the changes you intend to make, and why these changes are necessary. \
        Refer to specific file paths and line numbers from the provided context."
    );

    if research_option_available {
        prompt.push_str(
            "\n\nIf you have sufficient information and all necessary files are in context, you MUST provide all `range` blocks required to implement your plan. \
            Each `range` block defines a segment of code to be replaced or a new file to be created. \
            For each range of line numbers that you provide an explanation for how to edit, you MUST provide a `range` block right after."
        );
        prompt.push_str("\n\nExample of a `range` block for editing an existing file:\n```range\npath/to/file.ext\nSTART_LINE-END_LINE\n```");
        prompt.push_str("\nExample of a `range` block for creating a new file:\n```range\npath/to/new_file.ext\n0-0\n```");
        prompt.push_str("\n\nAlternatively, if you need context from files not currently provided, or if you are unsure about the contents or existence of a file, you MUST NOT attempt to create or guess its content. Instead, you MUST request further information by providing a single `research` block. If you provide a `research` block, any `range` blocks will be ignored for this turn. The `research` block should contain a 1-3 paragraph explanation for an AI research assistant, detailing what files or information needs to be found.");
        prompt.push_str(&format!(
            "\nExample of a `research` block:\n{}\n",
            research_parser.example_prompt_text()
        ));
        prompt.push_str("\n\nTo summarize: EITHER provide all necessary `range` blocks OR a single `research` block. Ensure you use the exact file paths as provided in the input files when referring to existing files for `range` blocks.");
    } else {
        prompt.push_str(
            "\n\nYou have requested research multiple times. The `research` block option is no longer available for this task. \
            You MUST now provide all necessary `range` blocks required to implement your plan based on the currently available files. \
            Each `range` block defines a segment of code to be replaced or a new file to be created. \
            For each range of line numbers that you provide an explanation for how to edit, you MUST provide a `range` block right after."
        );
        prompt.push_str("\n\nExample of a `range` block for editing an existing file:\n```range\npath/to/file.ext\nSTART_LINE-END_LINE\n```");
        prompt.push_str("\nExample of a `range` block for creating a new file:\n```range\npath/to/new_file.ext\n0-0\n```");
        prompt.push_str("\n\nTo summarize: You MUST provide all necessary `range` blocks. Ensure you use the exact file paths as provided in the input files when referring to existing files for `range` blocks.");
    }
    prompt.push_str("\nIt is crucial that these blocks are accurate and cover all necessary modifications. Clearly articulate the purpose and impact of each proposed edit.");
    if app_config
        .advanced_language_features
        .contains(&"rust-delimiter-checking".to_string())
    {
        prompt.push_str("\n When writing code, you MUST carefully maintain the net balance of delimiters (parentheses, braces, brackets) as the original code you are replacing. In other words, the net difference between opening and closing delimiters of each type must match so you do not introduce syntax errors by leaving code outside of the range with hanging delimeters.");
    }

    // Include specific format descriptions from expectations.
    // Only include research block format description if research_option_available is true.
    for expectation in expectations {
        if research_option_available || expectation.parser.keyword() != research_parser.keyword() {
            prompt.push_str(&format!(
                "\nDetailed format for '{}' blocks: {}\n",
                expectation.parser.keyword(),
                expectation.parser.block_format_description()
            ));
        }
    }

    if research_option_available {
        prompt.push_str(
            "\nReiterating: provide your detailed thought process. Then, provide EITHER all necessary `range` blocks OR a single `research` block. \
            IMPORTANT: Do not add any other content or commentary inside of the `range` or `research` blocks."
        );
    } else {
        prompt.push_str(
            "\nReiterating: provide your detailed thought process. Then, provide ALL necessary `range` blocks. \
            IMPORTANT: Do not add any other content or commentary inside of the `range` blocks."
        );
    }

    prompt
}

// Prompt for the expert LLM to perform direct editing using range-replace blocks.
pub fn construct_expert_editing_prompt(
    app_config: &AppConfig,
    user_instruction: &str,
    task_info: Option<&str>, // New parameter for task context
    _available_files_for_validation_only: &[LabeledFile], // For consistency and potential use by parsers
    research_option_available: bool,
) -> String {
    let mut effective_user_instruction = user_instruction.to_string();
    let research_parser = crate::block_parsing::research_block::ResearchBlockParser; // Used for example text

    crate::llm::apply_prefix_based_on_mode(
        &mut effective_user_instruction,
        app_config,
        crate::config::app_config::NoThinkMode::ExpertEditing,
    );

    let mut prompt = String::new();
    let range_replace_parser = RangeReplaceBlockParser;

    if let Some(info) = task_info {
        if !info.trim().is_empty() {
            prompt.push_str(&format!(
                "Important Task Context (may include previous errors or analysis):\n---\n{}\n---\n\n",
                info.trim()
            ));
        }
    }

    prompt.push_str(
        "You are an expert AI developer, tasked with directly implementing changes to fulfill the user's request. \
        File contents have been provided in a preceding message. Please refer to them carefully."
    );

    prompt.push_str(&format!(
        "\n\nBased on the current user's request: \"{}\"\n",
        effective_user_instruction
    ));

    prompt.push_str(
        "You must first think out loud, explaining your plan to fulfill this request in detail. \
        This plan should detail the changes you intend to make, which files are affected, and why these changes are necessary."
    );

    if research_option_available {
        prompt.push_str(
            "\n\nIf you have sufficient information and all necessary files are in context, after you have laid out your plan you MUST provide ALL necessary `range-replace` code blocks to implement your plan. Each `range-replace` block defines a segment of code to be replaced or a new file to be created, along with its new content."
        );
        prompt.push_str(&format!(
            "\n\nFormat for `range-replace` blocks:\n",
        ));
        prompt.push_str(&format!(
            "Example of a `range-replace` block for editing an existing file:\n```range-replace\npath/to/file.ext\nSTART_LINE-END_LINE\n<<<<<<< RANGE\noriginal code\n=======\nnew_content_for_the_lines\n>>>>>>> REPLACE\n```\n"
        ));
        prompt.push_str(&format!(
            "Example of a `range-replace` block for creating a new file:\n```range-replace\npath/to/new_file.ext\n0-0\n<<<<<<< RANGE\n=======\ncontent_for_the_new_file\n>>>>>>> REPLACE\n```\n"
        ));
        prompt.push_str("\n\nAlternatively, if you need to edit a file which is not already included (ex. to fix compiler/test errors), or require more files in context to understand structs/enums/functions/types/configs/anything else from the codebase, then you MUST submit a research request by providing a single `research` block and no range-replace block. If you provide a `research` block, any `range-replace` blocks will be ignored for this turn. The `research` block should contain a 1-3 paragraph explanation + keyword/file list for an AI research assistant, detailing what files or information needs to be found with likely file names to search and/or keywords to grep.");
        prompt.push_str(&format!(
            "\nExample of a `research` block:\n{}\n",
            research_parser.example_prompt_text()
        ));
        prompt.push_str("\n\nTo summarize: After your plan, EITHER provide all necessary `range-replace` blocks OR a single `research` block. Ensure you use the exact file paths as provided in the input files when referring to existing files for `range-replace` blocks.");
    } else {
        prompt.push_str(
            "\n\nYou have requested research multiple times. The `research` block option is no longer available for this task. Now you MUST lay out your plan and provide ALL necessary `range-replace` code blocks to implement your plan based on the currently available files. Each `range-replace` block defines a segment of code to be replaced or a new file to be created, along with its new content."
        );
        prompt.push_str(&format!(
            "\n\nFormat for `range-replace` blocks:\n",
        ));
        prompt.push_str(&format!(
            "Example of a `range-replace` block for editing an existing file:\n```range-replace\npath/to/file.ext\nSTART_LINE-END_LINE\n<<<<<<< RANGE\noriginal code\n=======\nnew_content_for_the_lines\n>>>>>>> REPLACE\n```\n"
        ));
        prompt.push_str(&format!(
            "Example of a `range-replace` block for creating a new file:\n```range-replace\npath/to/new_file.ext\n0-0\n<<<<<<< RANGE\n=======\ncontent_for_the_new_file\n>>>>>>> REPLACE\n```\n"
        ));
        prompt.push_str("\n\nTo summarize: After your plan, you MUST provide all necessary `range-replace` blocks. Ensure you use the exact file paths as provided in the input files when referring to existing files for `range-replace` blocks.");
    }

    prompt.push_str(
        "\nIMPORTANT INSTRUCTIONS FOR BLOCKS:\n\
        1. CRITICAL: The code you provide inside each 'range-replace' block MUST be syntactically correct and complete. Pay close attention to maintaining unmatched delimiters (e.g., `()`, `{}`, `[]`) for the specific range that you choose from the original code. Your response will be used directly to replace code, so any syntax errors which do not include required unmatched delimiters (that match outside of the range you choose to edit), will cause the entire operation to fail. Double-check your generated code for correctness before responding.\n\
        2. For any given file and line range, you must provide only ONE `range-replace` block. Do not specify multiple blocks for the same target file & range.\n\
        3. Do not add any other content or commentary inside of the `range-replace` or `research` blocks themselves, only the path, range, markers, and new code for `range-replace`, or the research explanation + keywords for `research`.\
        4. Ideally ranges inside of `range-replace` blocks are maximum of up to 30 lines at a time. Larger are allowed when replacing a whole function/struct/type/etc, but aim for multiple smaller ranges whenever sensible.
        5. You *MUST* explicitly write out *ALL* of the code for `range-replace`, you are not allowed to use elipses or omit any lines in the code you are writing which will replace the original code.\n\
        6. You are not allowed to be lazy, you *MUST* write out code in full."
    );

    prompt
}
