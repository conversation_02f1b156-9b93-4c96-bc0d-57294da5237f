use crate::block_parsing::expert_block::ExpertBlockParser;
use crate::block_parsing::processor::{
    process_llm_response_with_blocks, BlockCount, BlockExpectation,
};
use crate::block_parsing::range_blocks::RangeBlockParser; // For expert planning expectations
use crate::block_parsing::traits::ParsableBlock;
use crate::block_parsing::utils::extract_raw_code_blocks;
use crate::config::app_config::{
    setup_specific_llm_client, AppConfig, AutoExpertMode, AutoExpertSwitch,
};
use crate::editor::types::{ProcessingSignal, ProposedEdit};
use crate::expert::prompts::{
    construct_expert_editing_prompt, construct_expert_needed_prompt,
    construct_expert_planning_prompt,
};
use crate::files::file_handler::LabeledFile;
use crate::llm::client::temporal_chat_inference;
use crate::llm::{ChatMessage, ChatRole, LLMClient, MessageType};
use log::{debug, error, info, trace, warn};
use std::collections::{HashMap, HashSet};
use std::path::PathBuf;
use std::sync::Arc;
use syn;
use tokio::sync::mpsc;

use crate::block_parsing::research_block::ResearchBlockParser;
use crate::research::prompts::construct_auto_research_decision_prompt;

// Define the outcome of the expert flow
#[derive(Debug)]
pub enum ExpertFlowOutcome {
    PlanText {
        plan_text: String,
        #[allow(dead_code)]
        research_content: Option<String>,
    },
    ExpertEdits {
        direct_edits: Vec<ProposedEdit>,
        #[allow(dead_code)]
        research_content: Option<String>,
    },
    OnlyResearch {
        research_content: String,
    },
    NoAction,
}

#[allow(clippy::too_many_arguments)]
pub async fn try_run_expert_flow(
    app_config: &AppConfig,
    default_llm_instance: Arc<dyn LLMClient>,
    expert_llm_instance_override: Option<Arc<dyn LLMClient>>,
    historical_context: &[ChatMessage],
    conversation_log: &mut Vec<ChatMessage>,
    ordered_files: &mut crate::files::ordered_files::OrderedFiles,
    instruction_message_content: &str,
    is_retry: bool,
    processing_tx_opt: Option<&mpsc::Sender<ProcessingSignal>>,
) -> Result<ExpertFlowOutcome, String> {
    let mut effective_switch = app_config.auto_expert_switch;

    if is_retry {
        match effective_switch {
            AutoExpertSwitch::First => {
                effective_switch = AutoExpertSwitch::False;
            }
            AutoExpertSwitch::FirstForcedThenFalse => {
                effective_switch = AutoExpertSwitch::False;
            }
            AutoExpertSwitch::FirstForcedThenTrue => {
                effective_switch = AutoExpertSwitch::True;
            }
            _ => {}
        }
    }

    let labeled_files_vec: Vec<LabeledFile> = ordered_files
        .get_all_labeled_files_map()
        .values()
        .cloned()
        .collect();

    match effective_switch {
        AutoExpertSwitch::False => {
            return Ok(ExpertFlowOutcome::NoAction);
        }
        AutoExpertSwitch::True | AutoExpertSwitch::First => {
            trace!(
                "Auto-expert is '{:?}'. Asking default LLM if expert intervention is needed.",
                effective_switch
            );
            let expert_needed_prompt = construct_expert_needed_prompt(
                app_config,
                instruction_message_content,
                &labeled_files_vec,
            );
            let mut decision_context = historical_context.to_vec();
            decision_context.extend_from_slice(conversation_log);

            match temporal_chat_inference(
                default_llm_instance.as_ref(),
                &decision_context,
                &expert_needed_prompt,
            )
            .await
            {
                Ok(response_text) => {
                    let expert_parser = ExpertBlockParser;
                    let expectations = vec![BlockExpectation {
                        parser: Box::new(ExpertBlockParser),
                        expected_count: BlockCount::Exact(1),
                    }];
                    let mut temp_log = decision_context.clone();
                    temp_log.push(ChatMessage {
                        role: ChatRole::User,
                        content: expert_needed_prompt.clone(),
                        message_type: MessageType::Text,
                    });
                    temp_log.push(ChatMessage {
                        role: ChatRole::Assistant,
                        content: response_text.clone(),
                        message_type: MessageType::Text,
                    });

                    match process_llm_response_with_blocks(
                        &response_text,
                        &expectations,
                        &expert_needed_prompt,
                        &labeled_files_vec,
                        default_llm_instance.clone(),
                        &decision_context,
                        &mut temp_log,
                        app_config,
                    )
                    .await
                    {
                        Ok(processed_output) => {
                            if let Some(expert_block) =
                                processed_output.get_first_block_by_id(&expert_parser.id())
                            {
                                if expert_parser.parse_to_bool(expert_block).unwrap_or(false) {
                                    info!("LLM decided to ask expert for help. Proceeding with expert flow.");
                                    conversation_log.push(ChatMessage {
                                        role: ChatRole::System,
                                        content: "Default LLM determined expert intervention is needed. Engaging expert model.".to_string(),
                                        message_type: MessageType::Text,
                                    });
                                    return run_expert_core_logic(
                                        app_config,
                                        expert_llm_instance_override,
                                        historical_context,
                                        conversation_log,
                                        ordered_files,
                                        instruction_message_content,
                                        default_llm_instance,
                                        processing_tx_opt,
                                    )
                                    .await;
                                } else {
                                    info!("LLM decided expert help not needed.");
                                    return Ok(ExpertFlowOutcome::NoAction);
                                }
                            } else {
                                trace!("No valid 'expert' block. Assuming 'false'.");
                                return Ok(ExpertFlowOutcome::NoAction);
                            }
                        }
                        Err(e) => {
                            trace!(
                                "Error processing LLM decision for expert: {}. Assuming 'false'.",
                                e
                            );
                            return Ok(ExpertFlowOutcome::NoAction);
                        }
                    }
                }
                Err(e) => {
                    warn!(
                        "LLM call for expert decision failed: {}. Skipping expert flow.",
                        e
                    );
                    return Ok(ExpertFlowOutcome::NoAction);
                }
            }
        }
        AutoExpertSwitch::Forced
        | AutoExpertSwitch::FirstForcedThenFalse
        | AutoExpertSwitch::FirstForcedThenTrue => {
            trace!(
                "Auto-expert is '{:?}' (effective after retry check). Proceeding with expert flow.",
                effective_switch
            );
            return run_expert_core_logic(
                app_config,
                expert_llm_instance_override,
                historical_context,
                conversation_log,
                ordered_files,
                instruction_message_content,
                default_llm_instance,
                processing_tx_opt,
            )
            .await;
        }
    }
}

async fn run_expert_core_logic(
    app_config: &AppConfig,
    expert_llm_instance_override: Option<Arc<dyn LLMClient>>,
    historical_context: &[ChatMessage],
    conversation_log: &mut Vec<ChatMessage>,
    ordered_files: &mut crate::files::ordered_files::OrderedFiles,
    instruction_message_content: &str,
    default_llm_instance: Arc<dyn LLMClient>,
    processing_tx_opt: Option<&mpsc::Sender<ProcessingSignal>>,
) -> Result<ExpertFlowOutcome, String> {
    let expert_model_alias_str = match &app_config.expert_model {
        Some(alias) => alias.as_str(),
        None => {
            let err_msg = format!("Auto-expert ({:?}) is enabled, but no expert model alias is configured (`expert_model`).", app_config.auto_expert_mode);
            error!("{}", err_msg);
            return Err(err_msg);
        }
    };

    let expert_llm_instance = match expert_llm_instance_override {
        Some(instance) => instance,
        None => match setup_specific_llm_client(app_config, expert_model_alias_str) {
            Ok(instance) => instance,
            Err(e) => {
                let err_msg = format!(
                    "Failed to set up expert LLM ('{}'): {}",
                    expert_model_alias_str, e
                );
                error!("{}", err_msg);
                return Err(err_msg);
            }
        },
    };

    if ordered_files.is_empty() {
        info!(
            "Expert flow invoked with no files in context. Forcing research task via expert model ('{}').",
            expert_model_alias_str
        );

        let ls_command_output_str = match tokio::process::Command::new("ls")
            .current_dir(std::env::current_dir().unwrap_or_else(|_| PathBuf::from(".")))
            .output()
            .await
        {
            Ok(output) => {
                if output.status.success() {
                    String::from_utf8_lossy(&output.stdout).to_string()
                } else {
                    let stderr = String::from_utf8_lossy(&output.stderr);
                    warn!(
                        "'ls' command failed for expert forced research. Stderr: {}",
                        stderr
                    );
                    format!("Failed to execute 'ls': {}", stderr)
                }
            }
            Err(e) => {
                warn!(
                    "Failed to spawn 'ls' command for expert forced research: {}",
                    e
                );
                format!("Error executing 'ls': {}", e)
            }
        };

        let research_prompt_content = construct_auto_research_decision_prompt(
            app_config,
            instruction_message_content,
            app_config.task_info.as_deref(),
            &[],
            &ls_command_output_str,
            true,
        );

        let mut expert_call_context = historical_context.to_vec();
        expert_call_context.extend_from_slice(conversation_log);

        let expert_user_prompt_message = ChatMessage {
            role: ChatRole::User,
            content: research_prompt_content.clone(),
            message_type: MessageType::Text,
        };
        expert_call_context.push(expert_user_prompt_message.clone());
        conversation_log.push(expert_user_prompt_message);

        match expert_llm_instance.chat(&expert_call_context).await {
            Ok(expert_response_text) => {
                trace!(
                    "Expert model ('{}') response for forced research:\n{}",
                    expert_model_alias_str,
                    expert_response_text
                );
                let assistant_message = ChatMessage {
                    role: ChatRole::Assistant,
                    content: expert_response_text.clone(),
                    message_type: MessageType::Text,
                };
                conversation_log.push(assistant_message);

                let research_parser_instance = ResearchBlockParser;
                let expectations = vec![BlockExpectation {
                    parser: Box::new(ResearchBlockParser),
                    expected_count: BlockCount::Exact(1),
                }];

                match process_llm_response_with_blocks(
                    &expert_response_text,
                    &expectations,
                    &research_prompt_content,
                    &[],
                    default_llm_instance.clone(),
                    historical_context,
                    conversation_log,
                    app_config,
                )
                .await
                {
                    Ok(processed_output) => {
                        if let Some(research_block_raw) =
                            processed_output.get_first_block_by_id(&research_parser_instance.id())
                        {
                            match research_parser_instance.parse_to_string(research_block_raw) {
                                Ok(research_text) => {
                                    info!(
                                        "Expert model ('{}') provided forced research task: {}",
                                        expert_model_alias_str, research_text
                                    );
                                    return Ok(ExpertFlowOutcome::OnlyResearch {
                                        research_content: research_text,
                                    });
                                }
                                Err(e) => {
                                    let err_msg = format!("Expert ('{}') failed to parse mandatory research block: {}. Response: {}", expert_model_alias_str, e, expert_response_text);
                                    error!("{}", err_msg);
                                    return Err(err_msg);
                                }
                            }
                        } else {
                            let err_msg = format!("Expert ('{}') failed to provide mandatory research block. Response: {}", expert_model_alias_str, expert_response_text);
                            error!("{}", err_msg);
                            return Err(err_msg);
                        }
                    }
                    Err(e) => {
                        let err_msg = format!("Error processing expert's ('{}') forced research response: {}. Original response: {}", expert_model_alias_str, e, expert_response_text);
                        error!("{}", err_msg);
                        return Err(err_msg);
                    }
                }
            }
            Err(e) => {
                let err_msg = format!(
                    "Expert model ('{}') call for forced research failed: {}",
                    expert_model_alias_str, e
                );
                error!("{}", err_msg);
                return Err(err_msg);
            }
        }
    }

    let research_option_available_for_expert =
        if app_config.expert_model_auto_research_loop_max == 0 {
            false
        } else {
            match app_config.auto_research_mode {
                crate::config::app_config::AutoResearchMode::NoFiles => false,
                crate::config::app_config::AutoResearchMode::False => false,
                _ => true,
            }
        };

    let labeled_files_vec: Vec<LabeledFile> = ordered_files
        .get_all_labeled_files_map()
        .values()
        .cloned()
        .collect();

    match app_config.auto_expert_mode {
        AutoExpertMode::Planning => {
            info!(
                "Asking expert model ('{}') for editing plan...",
                expert_model_alias_str
            );
            let expert_planning_expectations = vec![
                BlockExpectation {
                    parser: Box::new(RangeBlockParser),
                    expected_count: BlockCount::Any,
                },
                BlockExpectation {
                    parser: Box::new(crate::block_parsing::research_block::ResearchBlockParser),
                    expected_count: BlockCount::Any,
                },
            ];
            let expert_planning_prompt_content = construct_expert_planning_prompt(
                app_config,
                instruction_message_content,
                app_config.task_info.as_deref(),
                &labeled_files_vec,
                &expert_planning_expectations,
                research_option_available_for_expert,
            );
            let mut expert_call_context = historical_context.to_vec();
            expert_call_context.extend_from_slice(conversation_log);
            let expert_user_prompt_message = ChatMessage {
                role: ChatRole::User,
                content: expert_planning_prompt_content.clone(),
                message_type: MessageType::Text,
            };
            expert_call_context.push(expert_user_prompt_message.clone());
            conversation_log.push(expert_user_prompt_message);

            match expert_llm_instance.chat(&expert_call_context).await {
                Ok(expert_response_text) => {
                    info!(
                        "Expert model ('{}') finished constructing editing plan.",
                        expert_model_alias_str
                    );
                    trace!("Expert model plan response:\n{}", expert_response_text);
                    conversation_log.push(ChatMessage {
                        role: ChatRole::Assistant,
                        content: expert_response_text.clone(),
                        message_type: MessageType::Text,
                    });

                    let mut combined_research_content: Option<String> = None;
                    match process_llm_response_with_blocks(
                        &expert_response_text,
                        &expert_planning_expectations,
                        &expert_planning_prompt_content,
                        &labeled_files_vec,
                        default_llm_instance.clone(),
                        &expert_call_context,
                        conversation_log,
                        app_config,
                    )
                    .await
                    {
                        Ok(processed_output) => {
                            if let Some(research_raw_blocks) =
                                processed_output.successfully_parsed_blocks.get(
                                    &crate::block_parsing::research_block::ResearchBlockParser.id(),
                                )
                            {
                                if !research_raw_blocks.is_empty() {
                                    let research_parser =
                                        crate::block_parsing::research_block::ResearchBlockParser;
                                    let mut temp_research_texts = Vec::new();
                                    for raw_block in research_raw_blocks {
                                        if let Ok(text) = research_parser.parse_to_string(raw_block)
                                        {
                                            temp_research_texts.push(text);
                                        }
                                    }
                                    if !temp_research_texts.is_empty() {
                                        combined_research_content =
                                            Some(temp_research_texts.join("\n\n---\n\n"));
                                    }
                                }
                            }

                            if let Some(research_text) = combined_research_content {
                                return Ok(ExpertFlowOutcome::OnlyResearch {
                                    research_content: research_text,
                                });
                            }

                            Ok(ExpertFlowOutcome::PlanText {
                                plan_text: expert_response_text,
                                research_content: None,
                            })
                        }
                        Err(e) => {
                            let err_msg = format!("Failed to process expert's planning response blocks: {}. Original response: {}", e, expert_response_text);
                            error!("{}", err_msg);
                            Err(err_msg)
                        }
                    }
                }
                Err(e) => {
                    let err_msg = format!(
                        "Expert model ('{}') call for planning failed: {}",
                        expert_model_alias_str, e
                    );
                    error!("{}", err_msg);
                    Err(err_msg)
                }
            }
        }
        AutoExpertMode::Editing => {
            info!(
                "Asking expert model ('{}') for direct edits...",
                expert_model_alias_str
            );
            let expert_editing_prompt_content = construct_expert_editing_prompt(
                app_config,
                instruction_message_content,
                app_config.task_info.as_deref(),
                &labeled_files_vec,
                research_option_available_for_expert,
            );
            let mut expert_call_context = historical_context.to_vec();
            expert_call_context.extend_from_slice(conversation_log);
            let expert_user_prompt_message = ChatMessage {
                role: ChatRole::User,
                content: expert_editing_prompt_content.clone(),
                message_type: MessageType::Text,
            };
            expert_call_context.push(expert_user_prompt_message.clone());
            conversation_log.push(expert_user_prompt_message);

            match expert_llm_instance.chat(&expert_call_context).await {
                Ok(expert_response_text) => {
                    info!(
                        "Expert model ('{}') responded with edits.",
                        expert_model_alias_str
                    );
                    trace!(
                        "Expert model raw response for edits:\n{}",
                        expert_response_text
                    );
                    conversation_log.push(ChatMessage {
                        role: ChatRole::Assistant,
                        content: expert_response_text.clone(),
                        message_type: MessageType::Text,
                    });

                    let (mut remaining_text, mut successfully_parsed_targets) =
                        crate::language_integrations::rust::syntax_checker::partition_and_strip_valid_range_replace_blocks(
                            &expert_response_text,
                            &labeled_files_vec,
                        );

                    let mut combined_research_content: Option<String> = None;
                    let research_parser = ResearchBlockParser;
                    let extracted_remaining = extract_raw_code_blocks(&remaining_text);
                    let all_remaining_blocks = extracted_remaining
                        .closed_blocks
                        .into_iter()
                        .chain(extracted_remaining.unclosed_block_at_end.into_iter());

                    let mut research_texts = Vec::new();
                    for block in all_remaining_blocks {
                        if block.keyword == research_parser.keyword() {
                            if let Ok(text) = research_parser.parse_to_string(&block) {
                                research_texts.push(text);
                                if let Some(pos) = remaining_text.find(&block.full_block_text) {
                                    remaining_text
                                        .replace_range(pos..pos + block.full_block_text.len(), "");
                                }
                            }
                        }
                    }

                    if !research_texts.is_empty() {
                        combined_research_content = Some(research_texts.join("\n\n---\n\n"));
                    }

                    if let Some(research_text) = combined_research_content {
                        return Ok(ExpertFlowOutcome::OnlyResearch {
                            research_content: research_text,
                        });
                    }

                    let final_extraction = extract_raw_code_blocks(&remaining_text);
                    let mut malformed_blocks_to_fix = final_extraction.closed_blocks;
                    if let Some(unclosed) = final_extraction.unclosed_block_at_end {
                        malformed_blocks_to_fix.push(unclosed);
                    }

                    if !malformed_blocks_to_fix.is_empty() {
                        let text_to_fix = malformed_blocks_to_fix
                            .iter()
                            .map(|b| b.full_block_text.as_str())
                            .collect::<Vec<_>>()
                            .join("\n\n");

                        info!(
                            "Expert response contains malformed or unclosed blocks. {} blocks successfully parsed. Attempting to fix remaining blocks...",
                            successfully_parsed_targets.len()
                        );
                        let retry_prompt = format!(
                            "Your previous response contained malformed or unclosed `range-replace` blocks. Please correct ONLY the following block(s) so that they are valid `range-replace` blocks for the changes that could not be parsed before. Discard any text that is not a valid block.\n\nBlock(s) to fix:\n```\n{}\n```",
                            text_to_fix
                        );

                        let mut retry_context = expert_call_context.clone();
                        retry_context.push(ChatMessage {
                            role: ChatRole::Assistant,
                            content: expert_response_text.clone(),
                            message_type: MessageType::Text,
                        });

                        match temporal_chat_inference(expert_llm_instance.as_ref(), &retry_context, &retry_prompt).await {
                            Ok(retry_response) => {
                                let (still_remaining, mut new_edits) = crate::language_integrations::rust::syntax_checker::partition_and_strip_valid_range_replace_blocks(
                                        &retry_response,
                                        &labeled_files_vec,
                                    );
 
                                // De-duplicate edits from the retry response.
                                if !new_edits.is_empty() && !successfully_parsed_targets.is_empty() {
                                    let existing_targets: HashSet<_> =
                                        successfully_parsed_targets.iter().map(|p| &p.edit_target).collect();
                                    let original_count = new_edits.len();
                                    new_edits.retain(|new_edit| {
                                        !existing_targets.contains(&new_edit.edit_target)
                                    });
                                    if new_edits.len() < original_count {
                                        info!(
                                            "Removed {} duplicate edit(s) from retry response.",
                                            original_count - new_edits.len()
                                        );
                                    }
                                }
 
                                successfully_parsed_targets.extend(new_edits);

                                let retry_extraction = extract_raw_code_blocks(&still_remaining);
                                let mut unparsable_blocks = retry_extraction.closed_blocks;
                                if let Some(unclosed) = retry_extraction.unclosed_block_at_end {
                                    unparsable_blocks.push(unclosed);
                                }

                                if !unparsable_blocks.is_empty() {
                                    let unparsable_text = unparsable_blocks
                                        .iter()
                                        .map(|b| b.full_block_text.as_str())
                                        .collect::<Vec<_>>()
                                        .join("\n\n");
                                    warn!(
                                        "Expert fix-up response still contained unparsable blocks: '{}'",
                                        unparsable_text
                                    );
                                }
                            }
                            Err(e) => {
                                warn!("LLM call to fix malformed expert response failed: {}. Proceeding with only the initially valid blocks.", e);
                            }
                        }
                    }

                    let mut final_proposed_edits: Vec<ProposedEdit> = Vec::new();
                    let current_dir_for_new_files = std::env::current_dir()
                        .map_err(|e| format!("Failed to get current_dir: {}", e))?;

                    if let Some(tx) = processing_tx_opt.as_ref() {
                        let plan_items: Vec<crate::editor::types::EditTarget> =
                            successfully_parsed_targets
                                .iter()
                                .map(|p_rr| p_rr.edit_target.clone())
                                .collect();
                        if !plan_items.is_empty() {
                            if tx
                                .send(ProcessingSignal::EditPlanReady { plan: plan_items })
                                .await
                                .is_err()
                            {
                                warn!("Failed to send EditPlanReady from expert flow");
                            }
                        }
                    }

                    for parsed_rr_item in &successfully_parsed_targets {
                        let mut resolved_path = parsed_rr_item.edit_target.file_path.clone();
                        if !resolved_path.is_absolute() {
                            resolved_path = current_dir_for_new_files.join(&resolved_path);
                        }
                        let path_for_map_key = if parsed_rr_item.edit_target.start_line == 0
                            && parsed_rr_item.edit_target.end_line == 0
                        {
                            resolved_path.clone()
                        } else {
                            dunce::canonicalize(&resolved_path)
                                .unwrap_or_else(|_| resolved_path.clone())
                        };

                        if parsed_rr_item.edit_target.start_line == 0
                            && parsed_rr_item.edit_target.end_line == 0
                        {
                            if ordered_files.get_labeled_file(&path_for_map_key).is_none() {
                                if let Some(parent_dir) = path_for_map_key.parent() {
                                    if !parent_dir.exists() {
                                        tokio::fs::create_dir_all(parent_dir).await.map_err(
                                            |e| {
                                                format!(
                                                    "Failed to create parent dirs for {}: {}",
                                                    path_for_map_key.display(),
                                                    e
                                                )
                                            },
                                        )?;
                                    }
                                }
                                tokio::fs::File::create(&path_for_map_key)
                                    .await
                                    .map_err(|e| {
                                        format!(
                                            "Failed to create new file {}: {}",
                                            path_for_map_key.display(),
                                            e
                                        )
                                    })?;
                                ordered_files.create_new_file_in_context(
                                    &path_for_map_key,
                                    &parsed_rr_item.replacement_content,
                                )?;
                                info!(
                                    "Expert editing: Created new file {} and added to context.",
                                    path_for_map_key.display()
                                );
                            }
                        }
                        // Extract old code for this edit
                        let old_code = if parsed_rr_item.edit_target.start_line == 0 && parsed_rr_item.edit_target.end_line == 0 {
                            // New file case
                            String::new()
                        } else {
                            // Extract the lines that will be replaced
                            if let Some(labeled_file) = ordered_files.get_labeled_file(&path_for_map_key) {
                                let original_lines = labeled_file.get_original_lines();
                                if parsed_rr_item.edit_target.start_line > 0 && parsed_rr_item.edit_target.start_line <= original_lines.len() {
                                    let start_idx = parsed_rr_item.edit_target.start_line - 1; // Convert to 0-based
                                    let end_idx = std::cmp::min(parsed_rr_item.edit_target.end_line, original_lines.len());
                                    original_lines[start_idx..end_idx].join("\n")
                                } else {
                                    String::new()
                                }
                            } else {
                                String::new()
                            }
                        };

                        final_proposed_edits.push(ProposedEdit {
                            target: crate::editor::types::EditTarget {
                                file_path: path_for_map_key,
                                start_line: parsed_rr_item.edit_target.start_line,
                                end_line: parsed_rr_item.edit_target.end_line,
                            },
                            new_code: parsed_rr_item.replacement_content.clone(),
                            old_code,
                        });
                    }

                    // RESTORED: After collecting all proposed edits, perform delimiter checking
                    let mut delimiter_fixed_edits: Vec<ProposedEdit> = Vec::new();
                    let mut broken_files_cache: HashMap<PathBuf, bool> = HashMap::new();

                    for (idx, edit_to_check) in final_proposed_edits.into_iter().enumerate() {
                        let mut delimiter_check_passed = true;

                        let file_path = &edit_to_check.target.file_path;
                        let is_rust_file = file_path.extension().map_or(false, |ext| ext == "rs");
                        let delimiter_check_enabled = app_config
                            .advanced_language_features
                            .contains("rust-delimiter-checking");

                        let should_run_delimiter_check = if is_rust_file && delimiter_check_enabled {
                            let is_broken =
                                broken_files_cache.entry(file_path.clone()).or_insert_with(|| {
                                    if let Some(lf) = ordered_files.get_labeled_file(file_path) {
                                        let original_content = lf.get_original_lines().join("\n");
                                        if original_content.is_empty() {
                                            return false;
                                        }
                                        if let Err(e) = syn::parse_file(&original_content) {
                                            if e.to_string()
                                                .contains("cannot parse string into token stream")
                                            {
                                                warn!(
                                                    "Skipping delimiter check for {}: file appears to have pre-existing syntax errors (likely broken delimiters).",
                                                    file_path.display()
                                                );
                                                return true; // is broken
                                            }
                                        }
                                    }
                                    false // not broken
                                });
                            !*is_broken
                        } else {
                            false
                        };

                        let final_edit_to_apply = if should_run_delimiter_check {
                            let original_code_slice = if edit_to_check.target.start_line == 0
                                && edit_to_check.target.end_line == 0
                            {
                                String::new()
                            } else {
                                match ordered_files
                                    .get_labeled_file(&edit_to_check.target.file_path)
                                {
                                    Some(lf) => {
                                        let original_lines_vec = lf.get_original_lines();
                                        let start_idx = (edit_to_check.target.start_line - 1)
                                            .min(original_lines_vec.len());
                                        let end_idx_exclusive = (edit_to_check.target.end_line)
                                            .min(original_lines_vec.len());
                                        if start_idx < end_idx_exclusive {
                                            original_lines_vec[start_idx..end_idx_exclusive]
                                                .join("\n")
                                        } else {
                                            String::new()
                                        }
                                    }
                                    None => {
                                        warn!("Could not find LabeledFile for {} during expert delimiter check. Assuming empty original slice.", edit_to_check.target.file_path.display());
                                        String::new()
                                    }
                                }
                            };

                            match crate::language_integrations::rust::delimiter_fixer::check_and_fix_delimiters_for_edit(
                                &original_code_slice,
                                edit_to_check.clone(),
                                app_config,
                                expert_llm_instance.clone(),
                                &expert_call_context,
                                &edit_to_check.target.file_path,
                            ).await {
                                crate::language_integrations::rust::delimiter_fixer::DelimiterCheckOutcome::Success(fixed_edit) => {
                                    debug!("Delimiter check/fix successful for expert edit in {}", fixed_edit.target.file_path.display());
                                    delimiter_check_passed = true;
                                    Some(fixed_edit)
                                }
                                crate::language_integrations::rust::delimiter_fixer::DelimiterCheckOutcome::Failure(original_edit, err_msg) => {
                                    warn!("Delimiter check/fix failed for expert edit in {}: {}. DISCARDING this edit.", original_edit.target.file_path.display(), err_msg);
                                    delimiter_check_passed = false;
                                    None
                                }
                            }
                        } else {
                            Some(edit_to_check)
                        };

                        if let Some(final_edit) = final_edit_to_apply {
                            delimiter_fixed_edits.push(final_edit);
                        }

                        if let Some(tx) = processing_tx_opt.as_ref() {
                            if tx
                                .send(ProcessingSignal::TargetProcessed {
                                    target_index: idx,
                                    success: delimiter_check_passed,
                                })
                                .await
                                .is_err()
                            {
                                warn!(
                                    "Failed to send TargetProcessed from expert flow for index {}",
                                    idx
                                );
                            }
                        }
                    }
                    final_proposed_edits = delimiter_fixed_edits;
                    if final_proposed_edits.is_empty() {
                        info!(
                            "Expert (Editing Mode) provided no usable edits after processing."
                        );
                    }

                    Ok(ExpertFlowOutcome::ExpertEdits {
                        direct_edits: final_proposed_edits,
                        research_content: None,
                    })
                }
                Err(e) => {
                    let err_msg = format!(
                        "Expert model ('{}') call for editing failed: {}",
                        expert_model_alias_str, e
                    );
                    error!("{}", err_msg);
                    Err(err_msg)
                }
            }
        }
    }
}