use crate::config::AppConfig;
use crate::task::Task;
use log::info;

const PRUNING_CHAR_THRESHOLD: usize = 250000;

fn calculate_total_message_chars(tasks: &[Task]) -> usize {
    tasks
        .iter()
        .flat_map(|task| &task.messages)
        .map(|message| message.content.chars().count())
        .sum()
}

/// Checks if the history needs pruning based on character count.
/// If so, it collects summaries from all tasks, prepends them to `app_config.task_info`,
/// and clears the tasks list.
///
/// # Arguments
/// * `tasks` - The current list of tasks. Consumed and potentially cleared.
/// * `app_config` - The current application configuration. Consumed and potentially modified.
use crate::llm::{ChatMessage, ChatRole, MessageType};

/// * `force_prune` - If true, pruning will occur regardless of character count.
///
/// # Returns
/// A tuple containing:
/// - The (potentially cleared) list of tasks.
/// - The `AppConfig` (passed through).
/// - A `Vec<ChatMessage>` containing a system message for each pruned task, if pruning occurred.
pub fn prune_history_if_needed(
    mut tasks: Vec<Task>,
    app_config: AppConfig,
) -> (Vec<Task>, AppConfig, Vec<ChatMessage>) {
    if tasks.is_empty() {
        return (tasks, app_config, Vec::new()); // No tasks to prune
    }

    let num_pruned_tasks = tasks.len();
    let mut pruned_task_messages: Vec<ChatMessage> = Vec::new();

    info!("Pruning {} tasks from history.", num_pruned_tasks);

    for task in tasks.iter() {
        let summary_content = task
            .summary
            .as_deref()
            .unwrap_or("No summary was generated for this task.");

        let mut formatted_edits = String::new();
        if !task.applied_edits.is_empty() {
            formatted_edits.push_str("\n\nEdits Applied:\n");
            for edit in &task.applied_edits {
                // In the future, we might want to make this relative to the project root.
                // For now, the full path is clear.
                let display_path = edit.file_path.display();
                if edit.start_line == 0 && edit.end_line == 0 {
                    // This indicates a new file was created
                    formatted_edits.push_str(&format!("- {} (New File)\n", display_path));
                } else {
                    formatted_edits.push_str(&format!(
                        "- {}: lines {}-{}\n",
                        display_path, edit.start_line, edit.end_line
                    ));
                }
            }
        }

        let final_content =
            format!("Task Summary: {}{}", summary_content.trim(), formatted_edits);

        pruned_task_messages.push(ChatMessage {
            role: ChatRole::System,
            content: format!(
                "Summary of a previously completed and pruned task:\n---\n{}\n---",
                final_content
            ),
            message_type: MessageType::Text,
        });
    }

    tasks.clear(); // Clear the original tasks vector

    (tasks, app_config, pruned_task_messages)
}