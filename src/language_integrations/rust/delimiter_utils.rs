// src/language_integrations/rust/delimiter_utils.rs

use rustc_lexer::{Token, TokenKind};
use std::collections::HashMap;

/// A helper to check if an opening and closing delimiter form a matching pair.
fn is_matching_pair(open: char, close: char) -> bool {
    matches!((open, close), ('(', ')') | ('{', '}') | ('[', ']'))
}

/// Calculates the net unmatched delimiters in a Rust code snippet using a stack-based approach.
///
/// This function is aware of token order and is the primary method for checking correctness.
/// It uses `rustc_lexer` to correctly ignore delimiters within comments, strings, and other literals.
///
/// # Returns
/// A `Vec<char>` containing the sequence of net unmatched delimiters.
pub fn get_net_unmatched_delimiters(code: &str) -> Vec<char> {
    let mut stack: Vec<char> = Vec::new();

    let code_to_tokenize = if let Some(stripped) = code.strip_prefix("#!") {
        if let Some(newline_idx) = stripped.find('\n') {
            &stripped[newline_idx + 1..]
        } else {
            ""
        }
    } else {
        code
    };

    for Token { kind, .. } in rustc_lexer::tokenize(code_to_tokenize) {
        match kind {
            TokenKind::OpenParen => stack.push('('),
            TokenKind::OpenBrace => stack.push('{'),
            TokenKind::OpenBracket => stack.push('['),
            TokenKind::CloseParen => {
                if let Some(&last) = stack.last() {
                    if is_matching_pair(last, ')') {
                        stack.pop();
                        continue;
                    }
                }
                stack.push(')');
            }
            TokenKind::CloseBrace => {
                if let Some(&last) = stack.last() {
                    if is_matching_pair(last, '}') {
                        stack.pop();
                        continue;
                    }
                }
                stack.push('}');
            }
            TokenKind::CloseBracket => {
                if let Some(&last) = stack.last() {
                    if is_matching_pair(last, ']') {
                        stack.pop();
                        continue;
                    }
                }
                stack.push(']');
            }
            _ => {}
        }
    }
    stack
}

/// Counts the occurrences of each delimiter character in a given Rust code snippet.
///
/// This function is primarily used to generate detailed, human-readable error
/// messages for an LLM prompt when the more robust stack-based check fails.
///
/// # Returns
/// A HashMap where keys are '(', ')', '{', '}', '[', ']' and values are their counts.
pub fn count_delimiters(code: &str) -> HashMap<char, u32> {
    let mut counts = HashMap::new();
    let code_to_tokenize = if let Some(stripped) = code.strip_prefix("#!") {
        if let Some(newline_idx) = stripped.find('\n') {
            &stripped[newline_idx + 1..]
        } else {
            ""
        }
    } else {
        code
    };

    for Token { kind, .. } in rustc_lexer::tokenize(code_to_tokenize) {
        match kind {
            TokenKind::OpenParen => *counts.entry('(').or_insert(0) += 1,
            TokenKind::CloseParen => *counts.entry(')').or_insert(0) += 1,
            TokenKind::OpenBrace => *counts.entry('{').or_insert(0) += 1,
            TokenKind::CloseBrace => *counts.entry('}').or_insert(0) += 1,
            TokenKind::OpenBracket => *counts.entry('[').or_insert(0) += 1,
            TokenKind::CloseBracket => *counts.entry(']').or_insert(0) += 1,
            _ => {}
        }
    }
    counts
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_stack_perfectly_balanced() {
        assert_eq!(
            get_net_unmatched_delimiters("fn main() { let x = (1 + 2); }"),
            Vec::<char>::new()
        );
    }

    #[test]
    fn test_stack_mismatched_order() {
        assert_eq!(get_net_unmatched_delimiters("}{"), vec!['}', '{']);
    }

    #[test]
    fn test_count_simple() {
        let counts = count_delimiters("fn main() { let x = (1 + 2); }");
        assert_eq!(counts.get(&'('), Some(&2));
        assert_eq!(counts.get(&'{'), Some(&1));
    }
}
