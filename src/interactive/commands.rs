use std::collections::HashSet;
use std::str::FromStr;
use tokio::process::Command as TokioCommand;
use tokio::sync::mpsc;

use crate::editor::ProcessingSignal;
use crate::interactive::app::{InteractiveApp, LastSignificantCommandType};
use crate::llm::{ChatMessage, ChatRole, MessageType};
use crate::notifications::execute_notification_command;
use crate::task::Task;

use super::commands_utils::{copy_to_clipboard, expand_tilde, generate_context_string};

/// Struct to define command properties for help and autocomplete
pub struct CommandDefinition {
    pub name: &'static str,
    pub display_text: &'static str,
    pub description: &'static str,
    pub requires_argument: bool,
}

pub const COMMAND_DEFINITIONS: &[CommandDefinition] = &[
    CommandDefinition { name: "/add", display_text: "/add <path>...", description: "Adds file(s) or files in folder(s) to context.", requires_argument: true },
    CommandDefinition { name: "/ask", display_text: "/ask <your question>", description: "Ask a question using task and Q&A history.", requires_argument: true },
    CommandDefinition { name: "/ask-add-task-info", display_text: "/ask-add-task-info <your question>", description: "Ask a question using history and add answer to task info.", requires_argument: true },
    CommandDefinition { name: "/ask-no-context", display_text: "/ask-no-context <your question>", description: "Ask a question with no context (just the question to LLM).", requires_argument: true },
    CommandDefinition { name: "/ask-no-context-add-task-info", display_text: "/ask-no-context-add-task-info <your question>", description: "Ask a question with no context and add answer to task info.", requires_argument: true },
    CommandDefinition { name: "/auto-expert", display_text: "/auto-expert <switch>", description: "Sets auto-expert: false, true, forced, first, first-forced-then-false, first-forced-then-true.", requires_argument: true },
    CommandDefinition { name: "/auto-expert-mode", display_text: "/auto-expert-mode <planning|editing>", description: "Sets the mode for auto-expert.", requires_argument: true },
    CommandDefinition { name: "/auto-research", display_text: "/auto-research <mode>", description: "Sets auto-research: false, true, forced, first, first-forced-then-false, first-forced-then-true.", requires_argument: true },
    CommandDefinition { name: "/auto-test-command", display_text: "/auto-test-command <command>", description: "Sets the command for auto-testing after edits.", requires_argument: true },
    CommandDefinition { name: "/auto-test-toggle", display_text: "/auto-test-toggle", description: "Toggles the auto-test feature on/off.", requires_argument: false },
    CommandDefinition { name: "/cd", display_text: "/cd <directory>", description: "Changes the current working directory.", requires_argument: true },
    CommandDefinition { name: "/clear-all", display_text: "/clear-all", description: "Clears logs, task info, and task history. Does not remove files from context.", requires_argument: false },
    CommandDefinition { name: "/clear-logs", display_text: "/clear-logs", description: "Clears the displayed logs in the TUI.", requires_argument: false },
    CommandDefinition { name: "/clear-task-history", display_text: "/clear-task-history", description: "Clears all task history.", requires_argument: false },
    CommandDefinition { name: "/continue", display_text: "/continue <prompt>", description: "Continues from the last completed task.", requires_argument: true },
    CommandDefinition { name: "/copy-context", display_text: "/copy-context", description: "Copies the current context to the clipboard.", requires_argument: false },
    CommandDefinition { name: "/copy-context-diffs", display_text: "/copy-context-diffs", description: "Copies context and git diffs to the clipboard.", requires_argument: false },
    CommandDefinition { name: "/copy-context-diffs-test", display_text: "/copy-context-diffs-test", description: "Copies context, git diffs, and test output to the clipboard.", requires_argument: false },
    CommandDefinition { name: "/decision-model", display_text: "/decision-model", description: "Shows the current decision model alias.", requires_argument: false },
    CommandDefinition { name: "/decision-model-set", display_text: "/decision-model-set <alias>", description: "Sets the model alias for decision tasks.", requires_argument: true },
    CommandDefinition { name: "/default-model", display_text: "/default-model", description: "Shows the current default model configuration.", requires_argument: false },
    CommandDefinition { name: "/default-model-set", display_text: "/default-model-set <alias>", description: "Sets the default model alias to use from config.", requires_argument: true },
    CommandDefinition { name: "/drop", display_text: "/drop <path>...", description: "Drops file(s) or folder(s) from the context.", requires_argument: true },
    CommandDefinition { name: "/drop-all", display_text: "/drop-all", description: "Drops all files from the context.", requires_argument: false },
    CommandDefinition { name: "/drop-and-clear-all", display_text: "/drop-and-clear-all", description: "Drops all files from context and clears logs, task info, and history.", requires_argument: false },
    CommandDefinition { name: "/exit", display_text: "/exit or /quit", description: "Exits the application.", requires_argument: false },
    CommandDefinition { name: "/expert-model", display_text: "/expert-model", description: "Shows the current expert model alias.", requires_argument: false },
    CommandDefinition { name: "/expert-model-set", display_text: "/expert-model-set <alias>", description: "Sets the model alias for expert tasks.", requires_argument: true },
    CommandDefinition { name: "/help", display_text: "/help", description: "Shows this help message.", requires_argument: false },
    CommandDefinition { name: "/max-task-retries", display_text: "/max-task-retries", description: "Shows the current max task retries amount.", requires_argument: false },
    CommandDefinition { name: "/max-task-retries-set", display_text: "/max-task-retries-set <number>", description: "Sets the max number of times a task can be retried.", requires_argument: true },
    CommandDefinition { name: "/no-think", display_text: "/no-think <modes...>", description: "Sets no_think modes (space-separated: none, all, ...).", requires_argument: true },
    CommandDefinition { name: "/notification-command", display_text: "/notification-command <command>", description: "Sets the command for system notifications.", requires_argument: true },
    CommandDefinition { name: "/paste-llm-edits", display_text: "/paste-llm-edits", description: "Pastes and applies LLM-generated edits from the clipboard.", requires_argument: false },
    CommandDefinition { name: "/paste-llm-edits-and-test", display_text: "/paste-llm-edits-and-test", description: "Pastes edits, runs auto-test, and copies context on failure.", requires_argument: false },
    CommandDefinition { name: "/pwd", display_text: "/pwd", description: "Shows the current working directory.", requires_argument: false },
    CommandDefinition { name: "/quit", display_text: "/quit", description: "Alias for /exit.", requires_argument: false },
    CommandDefinition { name: "/r", display_text: "/r <task description>", description: "Alias for /research.", requires_argument: true },
    CommandDefinition { name: "/research", display_text: "/research <task description>", description: "Starts a research flow.", requires_argument: true },
    CommandDefinition { name: "/research-model", display_text: "/research-model", description: "Shows the current research model alias.", requires_argument: false },
    CommandDefinition { name: "/research-model-set", display_text: "/research-model-set <alias>", description: "Sets the model alias for research tasks.", requires_argument: true },
    CommandDefinition { name: "/retry-model", display_text: "/retry-model", description: "Shows the current retry model alias.", requires_argument: false },
    CommandDefinition { name: "/retry-model-set", display_text: "/retry-model-set <alias>", description: "Sets the model alias for retry tasks.", requires_argument: true },
    CommandDefinition { name: "/retry-model-max-attempts", display_text: "/retry-model-max-attempts", description: "Shows the current retry model max attempts.", requires_argument: false },
    CommandDefinition { name: "/retry-model-max-attempts-set", display_text: "/retry-model-max-attempts-set <number>", description: "Sets the maximum number of retry attempts for block parsing failures.", requires_argument: true },
    CommandDefinition { name: "/run", display_text: "/run <command>", description: "Executes a shell command.", requires_argument: true },
    CommandDefinition { name: "/summary-model", display_text: "/summary-model", description: "Shows the current summary model alias.", requires_argument: false },
    CommandDefinition { name: "/summary-model-set", display_text: "/summary-model-set <alias>", description: "Sets the model alias for summary tasks.", requires_argument: true },
    CommandDefinition { name: "/task-info", display_text: "/task-info", description: "Displays the current task information.", requires_argument: false },
    CommandDefinition { name: "/task-info-add", display_text: "/task-info-add <text>", description: "Adds text to the task information.", requires_argument: true },
    CommandDefinition { name: "/task-info-clear", display_text: "/task_info-clear", description: "Clears the task information.", requires_argument: false },
    CommandDefinition { name: "/task-info-set", display_text: "/task-info-set <text>", description: "Sets the task information.", requires_argument: true },
];

#[derive(Debug, PartialEq)]
pub enum ParsedCommand {
    Add(String),
    Pwd,
    Cd(String),
    ClearAll,
    ClearLogs,
    ClearTaskHistory,
    Continue(String),
    CopyContext,
    CopyContextDiffs,
    CopyContextDiffsTest,
    Drop(String),
    DropAll,
    DropAndClearAll,
    Help,
    MaxTaskRetriesShow,
    MaxTaskRetriesSet(String),
    DefaultModelShow,
    DefaultModelSet(String),
    Run(String),
    NoThink(String),
    TaskInfo,
    TaskInfoSet(String),
    TaskInfoAdd(String),
    TaskInfoClear,
    AutoTestCommand(String),
    AutoTestToggle,
    AutoResearchToggle(String),
    AutoExpertSwitch(String),
    ExpertModelSet(String),
    ExpertModelShow,
    AutoExpertMode(String),
    NotificationCommand(String),
    Research(String),
    ResearchModelSet(String),
    ResearchModelShow,
    RetryModelSet(String),
    RetryModelShow,
    RetryModelMaxAttemptsShow,
    RetryModelMaxAttemptsSet(String),
    SummaryModelSet(String),
    SummaryModelShow,
    DecisionModelSet(String),
    DecisionModelShow,
    Ask(String),
    AskAddTaskInfo(String),
    AskNoContext(String),
    AskNoContextAddTaskInfo(String),
    PasteLlmEdits,
    PasteLlmEditsAndTest,
    Exit,
    Unknown(String),
    NotACommand,
}
pub fn parse_command_from_input(input_text: &str) -> ParsedCommand {
    let trimmed_input = input_text.trim_start();
    if !trimmed_input.starts_with('/') {
        return ParsedCommand::NotACommand;
    }

    let mut parts = trimmed_input.splitn(2, char::is_whitespace);
    let command_with_slash = parts.next().unwrap_or("");
    let argument = parts.next().map(str::trim).unwrap_or("").to_string();

    let cmd_def_opt = COMMAND_DEFINITIONS
        .iter()
        .find(|def| def.name == command_with_slash);

    match cmd_def_opt {
        Some(cmd_def) => {
            if cmd_def.requires_argument && argument.is_empty() {
                return ParsedCommand::Unknown(format!("{}: missing argument", cmd_def.name));
            }
            match cmd_def.name {
                "/exit" | "/quit" => ParsedCommand::Exit,
                "/add" => ParsedCommand::Add(argument),
                "/pwd" => ParsedCommand::Pwd,
                "/cd" => ParsedCommand::Cd(argument),
                "/clear-all" => ParsedCommand::ClearAll,
                "/clear-logs" => ParsedCommand::ClearLogs,
                "/clear-task-history" => ParsedCommand::ClearTaskHistory,
                "/continue" => ParsedCommand::Continue(argument),
                "/copy-context" => ParsedCommand::CopyContext,
                "/copy-context-diffs" => ParsedCommand::CopyContextDiffs,
                "/copy-context-diffs-test" => ParsedCommand::CopyContextDiffsTest,
                "/drop" => ParsedCommand::Drop(argument),
                "/drop-all" => ParsedCommand::DropAll,
                "/drop-and-clear-all" => ParsedCommand::DropAndClearAll,
                "/help" => ParsedCommand::Help,
                "/max-task-retries" => ParsedCommand::MaxTaskRetriesShow,
                "/max-task-retries-set" => ParsedCommand::MaxTaskRetriesSet(argument),
                "/default-model" => ParsedCommand::DefaultModelShow,
                "/default-model-set" => ParsedCommand::DefaultModelSet(argument),
                "/run" => ParsedCommand::Run(argument),
                "/no-think" => ParsedCommand::NoThink(argument),
                "/task-info" => ParsedCommand::TaskInfo,
                "/task-info-set" => ParsedCommand::TaskInfoSet(argument),
                "/task-info-add" => ParsedCommand::TaskInfoAdd(argument),
                "/task-info-clear" => ParsedCommand::TaskInfoClear,
                "/auto-test-command" => ParsedCommand::AutoTestCommand(argument),
                "/auto-test-toggle" => ParsedCommand::AutoTestToggle,
                "/auto-research" => ParsedCommand::AutoResearchToggle(argument),
                "/auto-expert" => ParsedCommand::AutoExpertSwitch(argument),
                "/expert-model-set" => ParsedCommand::ExpertModelSet(argument),
                "/expert-model" => ParsedCommand::ExpertModelShow,
                "/auto-expert-mode" => ParsedCommand::AutoExpertMode(argument),
                "/notification-command" => ParsedCommand::NotificationCommand(argument),
                "/paste-llm-edits" => ParsedCommand::PasteLlmEdits,
                "/paste-llm-edits-and-test" => ParsedCommand::PasteLlmEditsAndTest,
                "/research" | "/r" => ParsedCommand::Research(argument),
                "/research-model-set" => ParsedCommand::ResearchModelSet(argument),
                "/research-model" => ParsedCommand::ResearchModelShow,
                "/retry-model-set" => ParsedCommand::RetryModelSet(argument),
                "/retry-model" => ParsedCommand::RetryModelShow,
                "/retry-model-max-attempts" => ParsedCommand::RetryModelMaxAttemptsShow,
                "/retry-model-max-attempts-set" => ParsedCommand::RetryModelMaxAttemptsSet(argument),
                "/summary-model-set" => ParsedCommand::SummaryModelSet(argument),
                "/summary-model" => ParsedCommand::SummaryModelShow,
                "/decision-model-set" => ParsedCommand::DecisionModelSet(argument),
                "/decision-model" => ParsedCommand::DecisionModelShow,
                "/ask" => ParsedCommand::Ask(argument),
                "/ask-add-task-info" => ParsedCommand::AskAddTaskInfo(argument),
                "/ask-no-context" => ParsedCommand::AskNoContext(argument),
                "/ask-no-context-add-task-info" => ParsedCommand::AskNoContextAddTaskInfo(argument),
                _ => ParsedCommand::Unknown(format!("Unmapped known command: {}", cmd_def.name)),
            }
        }
        None => {
            if command_with_slash.starts_with('/') && !command_with_slash[1..].is_empty() {
                ParsedCommand::Unknown(command_with_slash.to_string())
            } else {
                ParsedCommand::NotACommand
            }
        }
    }
}

pub async fn handle_command_if_present(
    app: &mut InteractiveApp,
    processing_tx: &mpsc::Sender<ProcessingSignal>,
) -> bool {
    let command_handled = match parse_command_from_input(&app.input) {
        ParsedCommand::Exit => {
            app.should_quit = true;
            app.last_significant_command = None;
            true
        }
        ParsedCommand::Add(paths_str) => {
            app.last_significant_command = None;
            let paths_to_process: Vec<String> = paths_str
                .split_whitespace()
                .filter(|s| !s.is_empty())
                .map(String::from)
                .collect();
            if paths_to_process.is_empty() {
                log::error!("Command Error: /add requires at least one file or directory path.");
                return true;
            }

            for path_item_str in paths_to_process {
                let path_expanded = expand_tilde(&path_item_str);
                let resolved_path_item = if path_expanded.is_absolute() {
                    path_expanded
                } else {
                    app.current_path.join(path_expanded)
                };
                match app
                    .ordered_files
                    .add_path_recursively(&resolved_path_item)
                    .await
                {
                    Ok(count) => {
                        if count > 1 {
                            log::debug!(
                                "Added {} files from '{}' to context.",
                                count,
                                resolved_path_item.display()
                            );
                        } else if count == 1 {
                            log::debug!("Added '{}' to context.", resolved_path_item.display());
                        } else {
                            log::debug!(
                                "No new files added from '{}'.",
                                resolved_path_item.display()
                            );
                        }
                    }
                    Err(e) => {
                        log::error!("Failed to add path {}: {}", resolved_path_item.display(), e)
                    }
                }
            }
            true
        }
        ParsedCommand::Continue(task_description) => {
            if app.is_processing() {
                log::warn!("Cannot start new task: an operation is already in progress.");
                return true;
            }
            let last_task = match app.last_completed_task.clone() {
                Some(task) => task,
                None => {
                    log::error!("No previous completed task to continue from.");
                    return true;
                }
            };

            let mut edits_context_str = String::new();
            edits_context_str.push_str("# Previous Task Detailed Info\n");
            edits_context_str.push_str("## User Specified Task Message\n");
            edits_context_str.push_str("```\n");
            edits_context_str.push_str(last_task.initial_user_prompt.trim());
            edits_context_str.push_str("\n```\n\n");

            if !last_task.applied_edits.is_empty() {
                edits_context_str.push_str("## Files Edited\n\n");
                for edit in &last_task.applied_edits {
                    let display_path = edit
                        .file_path
                        .strip_prefix(&app.current_path)
                        .unwrap_or(&edit.file_path);
                    edits_context_str.push_str(&format!("### {}\n\n", display_path.display()));
                    if edit.start_line == 0 && edit.end_line == 0 {
                        edits_context_str.push_str("#### New File\n");
                        edits_context_str.push_str("```\n");
                        edits_context_str.push_str(&edit.new_code);
                        if !edit.new_code.ends_with('\n') {
                            edits_context_str.push('\n');
                        }
                        edits_context_str.push_str("```\n\n");
                    } else {
                        edits_context_str.push_str(&format!(
                            "#### Edits Applied To Lines {}-{}\n",
                            edit.start_line, edit.end_line
                        ));

                        // Show old code if available
                        if !edit.old_code.is_empty() {
                            edits_context_str.push_str("**Previous Code:**\n");
                            edits_context_str.push_str("```\n");
                            edits_context_str.push_str(&edit.old_code);
                            if !edit.old_code.ends_with('\n') {
                                edits_context_str.push('\n');
                            }
                            edits_context_str.push_str("```\n\n");
                        }

                        // Show new code
                        edits_context_str.push_str("**New Code:**\n");
                        edits_context_str.push_str("```\n");
                        edits_context_str.push_str(&edit.new_code);
                        if !edit.new_code.ends_with('\n') {
                            edits_context_str.push('\n');
                        }
                        edits_context_str.push_str("```\n\n");
                    }
                }
            }

            let new_task_info = match app.app_config.task_info.take() {
                Some(existing_info) if !existing_info.trim().is_empty() => {
                    format!("{} \n{}", existing_info, edits_context_str)
                }
                _ => edits_context_str,
            };
            app.app_config.task_info = Some(new_task_info);

            app.input = task_description;
            app.last_significant_command = Some(LastSignificantCommandType::Edit);
            app.submit_current_input();

            let mut new_task = Task::new(
                app.app_config.user_prompt.clone(),
                app.app_config.task_info.clone(),
            );

            let (files_msg_opt, _) = app
                .ordered_files
                .get_files_for_prompt_message(&app.current_path);
            if let Some(files_msg) = files_msg_opt {
                new_task.add_message(files_msg);
            }

            let mut instruction_content_user = new_task.initial_user_prompt.clone();
            if let Some(ti_content) = &new_task.initial_task_info {
                if !ti_content.trim().is_empty() {
                    instruction_content_user = format!(
                        "Important task context:\n---\n{}\n---\n\n{}",
                        ti_content.trim(),
                        instruction_content_user
                    );
                }
            }
            new_task.add_message(crate::llm::ChatMessage {
                role: crate::llm::ChatRole::User,
                content: instruction_content_user,
                message_type: crate::llm::MessageType::Text,
            });

            app.start_processing_task(new_task, processing_tx).await;
            true
        }
        ParsedCommand::CopyContext => {
            app.last_significant_command = None;
            let context_string = generate_context_string(app, false, false).await;
            copy_to_clipboard(context_string);
            let notification_title = "Lledit Context Copied".to_string();
            let notification_message = "Copied files in context.".to_string();
            execute_notification_command(
                &app.app_config.notification_command,
                &app.current_path,
                &notification_title,
                &notification_message,
            );
            true
        }
        ParsedCommand::CopyContextDiffs => {
            app.last_significant_command = None;
            let context_string = generate_context_string(app, true, false).await;
            copy_to_clipboard(context_string);
            let notification_title = "Lledit Context Copied".to_string();
            let notification_message = "Copied files in context and git diffs.".to_string();
            execute_notification_command(
                &app.app_config.notification_command,
                &app.current_path,
                &notification_title,
                &notification_message,
            );
            true
        }
        ParsedCommand::CopyContextDiffsTest => {
            app.last_significant_command = None;
            let context_string = generate_context_string(app, true, true).await;
            copy_to_clipboard(context_string);
            let notification_title = "Lledit Context Copied".to_string();
            let notification_message =
                "Copied files in context, git diffs, and test output.".to_string();
            execute_notification_command(
                &app.app_config.notification_command,
                &app.current_path,
                &notification_title,
                &notification_message,
            );
            true
        }
        ParsedCommand::Pwd => {
            log::debug!("Current working directory: {}", app.current_path.display());
            app.last_significant_command = None;
            true
        }
        ParsedCommand::Cd(path_str) => {
            app.last_significant_command = None;
            let target_dir_expanded = expand_tilde(&path_str);
            let target_dir_for_log = target_dir_expanded.clone();
            let cd_result = tokio::task::spawn_blocking(move || {
                std::env::set_current_dir(&target_dir_expanded)?;
                std::env::current_dir()
            })
            .await;
            match cd_result {
                Ok(Ok(new_canonical_cwd)) => {
                    app.current_path = new_canonical_cwd;
                    log::debug!(
                        "Current working directory changed to: {}",
                        app.current_path.display()
                    );
                }
                Ok(Err(io_err)) => {
                    log::error!(
                        "Failed to change directory to {}: {}",
                        target_dir_for_log.display(),
                        io_err
                    );
                }
                Err(join_err) => {
                    log::error!(
                        "Failed to change directory (task execution error for {}): {}",
                        target_dir_for_log.display(),
                        join_err
                    );
                }
            }
            true
        }
        ParsedCommand::ClearAll => {
            app.clear_logs();
            app.clear_task_info();
            app.clear_task_history();
            log::info!("All clearable state (logs, task info, task history) has been reset.");
            app.last_significant_command = None;
            true
        }
        ParsedCommand::ClearLogs => {
            app.clear_logs();
            log::debug!("Displayed logs cleared.");
            app.last_significant_command = None;
            true
        }
        ParsedCommand::ClearTaskHistory => {
            app.clear_task_history();
            log::debug!("Task history cleared.");
            app.last_significant_command = None;
            true
        }
        ParsedCommand::Run(command_str) => {
            let app_current_path = app.current_path.clone();
            let command_to_run = command_str.clone();
            app.last_run_command_executed = Some(command_to_run.clone());
            log::debug!(
                "Running command: `{}` in `{}`",
                command_to_run,
                app_current_path.display()
            );
            let command_output_result = tokio::task::spawn_blocking(move || {
                std::process::Command::new("sh")
                    .arg("-c")
                    .arg(&command_to_run)
                    .current_dir(&app_current_path)
                    .output()
            })
            .await;
            match command_output_result {
                Ok(Ok(output)) => {
                    let stdout_str = String::from_utf8_lossy(&output.stdout);
                    let stderr_str = String::from_utf8_lossy(&output.stderr);
                    let combined_output = if !stdout_str.is_empty() && !stderr_str.is_empty() {
                        format!(
                            "--- STDOUT ---\n{}\n--- STDERR ---\n{}",
                            stdout_str.trim_end(),
                            stderr_str.trim_end()
                        )
                    } else if !stdout_str.is_empty() {
                        stdout_str.trim_end().to_string()
                    } else if !stderr_str.is_empty() {
                        format!("--- STDERR ---\n{}", stderr_str.trim_end())
                    } else {
                        "<no output>".to_string()
                    };
                    let log_message = format!(
                        "{}\nCommand output:\n{}\n{}",
                        "-".repeat(70),
                        combined_output,
                        "-".repeat(70)
                    );
                    log::debug!("{}", log_message);
                    app.last_run_command_output = Some(combined_output);
                }
                Ok(Err(e)) => {
                    log::error!("Failed to execute command `{}`: {}", command_str, e);
                    app.last_run_command_output = Some(format!("Failed to execute command: {}", e));
                }
                Err(e) => {
                    log::error!(
                        "Failed to spawn command execution task for `{}`: {}",
                        command_str,
                        e
                    );
                    app.last_run_command_output =
                        Some(format!("Failed to run command (task error): {}", e));
                }
            }
            log::debug!("Do you want to add this output to task info? (Y)es/(N)o [Yes]");
            app.input_mode = crate::interactive::app::InputMode::AwaitingRunConfirmation;
            app.last_significant_command = None;
            true
        }
        ParsedCommand::TaskInfo => {
            match &app.app_config.task_info {
                Some(info) if !info.is_empty() => {
                    log::debug!("Current task info:\n---\n{}\n---", info)
                }
                _ => log::debug!("Task info is currently not set."),
            }
            app.last_significant_command = None;
            true
        }
        ParsedCommand::TaskInfoSet(info) => {
            app.app_config.task_info = Some(info.clone());
            log::debug!("Task info set to: \"{}\"", info);
            app.last_significant_command = None;
            true
        }
        ParsedCommand::TaskInfoAdd(info_to_add) => {
            app.last_significant_command = None;
            match app.app_config.task_info.as_mut() {
                Some(existing_info) if !existing_info.is_empty() => {
                    existing_info.push_str("\n\n");
                    existing_info.push_str(&info_to_add);
                    log::debug!(
                        "Added to task info. New task info:\n---\n{}\n---",
                        existing_info
                    );
                }
                _ => {
                    app.app_config.task_info = Some(info_to_add.clone());
                    log::debug!("Task info set to: \"{}\"", info_to_add);
                }
            }
            true
        }
        ParsedCommand::TaskInfoClear => {
            app.clear_task_info();
            log::debug!("Task info cleared.");
            app.last_significant_command = None;
            true
        }
        ParsedCommand::AutoTestCommand(command_str) => {
            app.app_config.auto_test_command = command_str.clone();
            log::debug!("Auto-test command set to: `{}`", command_str);
            app.last_significant_command = None;
            true
        }
        ParsedCommand::AutoTestToggle => {
            app.app_config.auto_test_toggle = !app.app_config.auto_test_toggle;
            if app.app_config.auto_test_toggle {
                log::debug!(
                    "Auto-test toggled ON. Command: `{}`",
                    if app.app_config.auto_test_command.is_empty() {
                        "<not set>"
                    } else {
                        &app.app_config.auto_test_command
                    }
                );
            } else {
                log::debug!("Auto-test toggled OFF.");
            }
            app.last_significant_command = None;
            true
        }
        ParsedCommand::AutoResearchToggle(mode_str) => {
            app.last_significant_command = None;
            match crate::config::app_config::AutoResearchMode::from_str(&mode_str) {
                Ok(mode) => {
                    app.app_config.auto_research_mode = mode;
                    log::debug!("Auto-research mode set to: {}.", mode);
                }
                Err(e) => {
                    log::error!("Command Error: {}", e);
                }
            }
            true
        }
        ParsedCommand::AutoExpertSwitch(switch_str) => {
            app.last_significant_command = None;
            match crate::config::app_config::AutoExpertSwitch::from_str(&switch_str) {
                Ok(switch) => {
                    app.app_config.auto_expert_switch = switch;
                    log::debug!("Auto-expert: {}.", switch);
                }
                Err(e) => {
                    log::error!("Command Error: {}", e);
                }
            }
            true
        }
        ParsedCommand::ExpertModelSet(alias_str) => {
            app.last_significant_command = None;
            if app
                .app_config
                .get_model_config_by_alias(&alias_str)
                .is_some()
            {
                app.app_config.expert_model = Some(alias_str.clone());
                log::debug!("Expert model set to: '{}'.", alias_str);
            } else {
                log::error!(
                    "Command Error: Model alias '{}' not found in configuration.",
                    alias_str
                );
            }
            true
        }
        ParsedCommand::ExpertModelShow => {
            app.last_significant_command = None;
            match &app.app_config.expert_model {
                Some(alias) => log::debug!("Current expert model alias: '{}'.", alias),
                None => log::debug!("Expert model alias is not set."),
            }
            true
        }
        ParsedCommand::AutoExpertMode(mode_str) => {
            app.last_significant_command = None;
            match crate::config::app_config::AutoExpertMode::from_str(&mode_str) {
                Ok(mode) => {
                    app.app_config.auto_expert_mode = mode;
                    log::debug!("Auto-expert mode set to: {}.", mode);
                }
                Err(e) => {
                    log::error!("Command Error: {}", e);
                }
            }
            true
        }
        ParsedCommand::NotificationCommand(command_str) => {
            app.last_significant_command = None;
            app.app_config.notification_command = command_str.clone();
            if command_str.is_empty() {
                log::debug!("Notification command cleared.");
            } else {
                log::debug!("Notification command set to: `{}`", command_str);
            }
            true
        }
        ParsedCommand::PasteLlmEdits => {
            app.last_significant_command = None;
            if let Some(clipboard_content) = super::commands_utils::paste_from_clipboard() {
                log::info!("Pasting content from clipboard to apply edits...");
                super::commands_utils::parse_and_apply_llm_edits(app, &clipboard_content).await;
            } else {
                log::error!("Failed to paste content from clipboard.");
            }
            true
        }
        ParsedCommand::PasteLlmEditsAndTest => {
            app.last_significant_command = None;
            if let Some(clipboard_content) = super::commands_utils::paste_from_clipboard() {
                log::info!("Pasting content from clipboard to apply edits and run tests...");
                super::commands_utils::parse_and_apply_llm_edits(app, &clipboard_content).await;

                if app.app_config.auto_test_command.is_empty() {
                    log::warn!(
                        "/paste-llm-edits-and-test used, but no auto-test command is set. Skipping test."
                    );
                    return true;
                }

                let command_str = app.app_config.auto_test_command.clone();
                log::info!("Running auto-test command: `{}`", &command_str);

                match TokioCommand::new("sh")
                    .arg("-c")
                    .arg(&command_str)
                    .current_dir(&app.current_path)
                    .output()
                    .await
                {
                    Ok(output) => {
                        let stdout = String::from_utf8_lossy(&output.stdout);
                        let stderr = String::from_utf8_lossy(&output.stderr);
                        if !stdout.is_empty() {
                            log::debug!("Auto-test stdout:\n{}", stdout);
                        }
                        if !stderr.is_empty() {
                            log::debug!("Auto-test stderr:\n{}", stderr);
                        }

                        if !output.status.success() {
                            log::warn!(
                                "Auto-test command failed with status {}. Copying context with diffs and test output.",
                                output.status
                            );
                            let context_string = generate_context_string(app, true, true).await;
                            copy_to_clipboard(context_string);

                            let notification_title = "Lledit Context Copied".to_string();
                            let notification_message = "Auto-test failed. Copied context, diffs, and test output.".to_string();
                            execute_notification_command(
                                &app.app_config.notification_command,
                                &app.current_path,
                                &notification_title,
                                &notification_message,
                            );
                        } else {
                            log::info!("Auto-test command succeeded.");
                        }
                    }
                    Err(e) => {
                        log::error!("Failed to execute auto-test command `{}`: {}", command_str, e);
                    }
                }
            } else {
                log::error!("Failed to paste content from clipboard.");
            }
            true
        }
        ParsedCommand::Drop(paths_str) => {
            app.last_significant_command = None;
            let paths_to_process: Vec<String> = paths_str
                .split_whitespace()
                .filter(|s| !s.is_empty())
                .map(String::from)
                .collect();

            if paths_to_process.is_empty() {
                log::error!("Command Error: /drop requires at least one file path.");
                return true;
            }

            for path_item_str in paths_to_process {
                let path_to_drop_expanded = expand_tilde(&path_item_str);
                let resolved_path_to_drop = if path_to_drop_expanded.is_absolute() {
                    path_to_drop_expanded
                } else {
                    app.current_path.join(path_to_drop_expanded)
                };

                let removed_count = app
                    .ordered_files
                    .remove_path_recursively(&resolved_path_to_drop);
                if removed_count > 0 {
                    log::debug!(
                        "Dropped {} file(s) from context for path: {}",
                        removed_count,
                        resolved_path_to_drop.display()
                    );
                } else {
                    log::warn!(
                        "No files in context for path: {}",
                        resolved_path_to_drop.display()
                    );
                }
            }
            true
        }
        ParsedCommand::DropAll => {
            app.clear_all_files();
            log::debug!("All files dropped from context.");
            app.last_significant_command = None;
            true
        }
        ParsedCommand::DropAndClearAll => {
            app.clear_all_files();
            app.clear_logs();
            app.clear_task_info();
            app.clear_task_history();
            log::info!("Dropped all files and cleared all state (logs, task info, history).");
            app.last_significant_command = None;
            true
        }
        ParsedCommand::Help => {
            log::debug!("Available commands:");
            for cmd_def in COMMAND_DEFINITIONS {
                log::debug!("  {:<45} - {}", cmd_def.display_text, cmd_def.description);
            }
            app.last_significant_command = None;
            true
        }
        ParsedCommand::MaxTaskRetriesShow => {
            app.last_significant_command = None;
            log::debug!(
                "Current max task retries: {}",
                app.app_config.max_task_retries
            );
            true
        }
        ParsedCommand::MaxTaskRetriesSet(retries_str) => {
            app.last_significant_command = None;
            match retries_str.parse::<usize>() {
                Ok(retries) => {
                    app.app_config.max_task_retries = retries;
                    log::debug!("Max task retries set to: {}", retries);
                }
                Err(_) => {
                    log::error!(
                        "Command Error: Invalid number for /max-task-retries-set: '{}'",
                        retries_str
                    );
                }
            }
            true
        }
        ParsedCommand::DefaultModelShow => {
            app.last_significant_command = None;
            log::debug!("Current Default Model Configuration:");
            log::debug!("  Alias: {}", app.app_config.default_model);
            log::debug!("  Provider: {}", app.app_config.provider);
            log::debug!("  Model: {}", app.app_config.model);
            log::debug!(
                "  Provider URL: {}",
                if app.app_config.provider_url.is_empty() {
                    "Default".to_string()
                } else {
                    app.app_config.provider_url.clone()
                }
            );
            true
        }
        ParsedCommand::DefaultModelSet(alias_name) => {
            app.last_significant_command = None;
            if let Some(models_list) = &app.app_config.models_list {
                if let Some(entry) = models_list.iter().find(|e| e.alias == alias_name) {
                    app.app_config.default_model = alias_name.clone();
                    app.app_config.provider = entry.provider.clone();
                    app.app_config.model = entry.model.clone();
                    app.app_config.provider_url =
                        crate::config::app_config::AppConfig::determine_effective_provider_url(
                            &entry.provider,
                            entry.provider_url.clone(),
                        );
                    log::info!("Default model set to '{}'. Provider: {}, Model: {}. New operations will use this model.",
                                    alias_name, app.app_config.provider, app.app_config.model);
                    if !app.is_processing() {
                        app.current_operation_model_alias =
                            Some(app.app_config.default_model.clone());
                    }
                } else {
                    log::error!("Model alias '{}' not found in configuration.", alias_name);
                }
            } else {
                log::error!("No models_list configured. Cannot set default model alias.");
            }
            true
        }
        ParsedCommand::NoThink(modes_str) => {
            app.last_significant_command = None;
            let mut new_modes = HashSet::new();
            if modes_str.trim().is_empty() || modes_str.trim() == "none" {
                app.app_config.no_think.clear();
                log::debug!("NoThink modes cleared (set to none).");
            } else if modes_str.trim() == "all" {
                let mut all_modes = HashSet::new();
                all_modes.insert(crate::config::app_config::NoThinkMode::EditingPlanning);
                all_modes.insert(crate::config::app_config::NoThinkMode::EditingCoding);
                all_modes.insert(crate::config::app_config::NoThinkMode::ResearchAutoDecision);
                all_modes.insert(crate::config::app_config::NoThinkMode::ResearchPlanning);
                all_modes.insert(crate::config::app_config::NoThinkMode::ResearchSearching);
                all_modes.insert(crate::config::app_config::NoThinkMode::Ask);
                all_modes.insert(crate::config::app_config::NoThinkMode::Summaries);
                all_modes.insert(crate::config::app_config::NoThinkMode::ExpertAutoDecision);
                all_modes.insert(crate::config::app_config::NoThinkMode::ExpertPlanning);
                all_modes.insert(crate::config::app_config::NoThinkMode::ExpertEditing);
                app.app_config.no_think = all_modes;
                log::debug!("NoThink modes set to all.");
            } else {
                for mode_s in modes_str.split_whitespace() {
                    match crate::config::app_config::NoThinkMode::from_str(mode_s) {
                        Ok(mode) => {
                            new_modes.insert(mode);
                        }
                        Err(e) => {
                            log::error!("Command Error: {}", e);
                        }
                    }
                }
                app.app_config.no_think = new_modes;
                let mode_names: Vec<String> = app
                    .app_config
                    .no_think
                    .iter()
                    .map(|m| m.to_string())
                    .collect();
                log::debug!("NoThink modes set to: [{}].", mode_names.join(", "));
            }
            true
        }
        ParsedCommand::Research(task_description) => {
            app.original_user_request_for_current_processing_sequence =
                Some(task_description.clone());
            if app.is_processing || app.is_researching || app.is_asking_question {
                log::warn!("Cannot start research: another operation is already in progress.");
                return true;
            }
            app.last_significant_command =
                Some(crate::interactive::app::LastSignificantCommandType::Research);
            app.is_researching = true;
            app.is_asking_question = false;
            app.is_processing = false;
            app.research_progress = 0.0;
            app.research_bash_commands_issued = 0;
            let mut research_task_object = Task::new(
                format!("Research: {}", task_description),
                app.app_config.task_info.clone(),
            );
            research_task_object.add_message(ChatMessage {
                role: ChatRole::User,
                content: task_description.clone(),
                message_type: MessageType::Text,
            });
            app.current_research_task_id = Some(research_task_object.id.clone());
            app.tasks.push(research_task_object.clone());

            log::info!(
                "Starting research: \"{}\"",
                task_description.chars().take(50).collect::<String>()
            );
            log::debug!("{}", "-".repeat(70));

            let research_app_config = app.app_config.clone();
            let research_historical_context = app.get_full_conversation_history_for_llm();
            let app_current_path_for_research = app.current_path.clone();

            app.current_operation_model_alias = Some(
                app.app_config
                    .research_model
                    .as_ref()
                    .filter(|&a| a.to_lowercase() != "none" && a.to_lowercase() != "default")
                    .cloned()
                    .unwrap_or_else(|| app.app_config.default_model.clone()),
            );

            let research_abort_handle =
                crate::interactive::async_ops::spawn_new_research_processing(
                    research_app_config,
                    research_task_object.id.clone(),
                    task_description,
                    research_historical_context,
                    processing_tx.clone(),
                    app_current_path_for_research,
                    app.ordered_files.get_all_labeled_files_map().clone(),
                );
            app.current_research_abort_handle = Some(research_abort_handle);

            true
        }
        ParsedCommand::ResearchModelSet(alias_str) => {
            app.last_significant_command = None;
            if alias_str.to_lowercase() == "none" || alias_str.to_lowercase() == "default" {
                app.app_config.research_model = None;
                log::debug!("Research model reset to use default model.");
            } else if app
                .app_config
                .get_model_config_by_alias(&alias_str)
                .is_some()
            {
                app.app_config.research_model = Some(alias_str.clone());
                log::debug!("Research model set to: '{}'.", alias_str);
            } else {
                log::error!(
                    "Command Error: Model alias '{}' not found in configuration.",
                    alias_str
                );
            }
            true
        }
        ParsedCommand::ResearchModelShow => {
            app.last_significant_command = None;
            match &app.app_config.research_model {
                Some(alias) => log::debug!("Current research model alias: '{}'.", alias),
                None => log::debug!("Research model is not set (uses default model)."),
            }
            true
        }
        ParsedCommand::RetryModelSet(alias_str) => {
            app.last_significant_command = None;
            if alias_str.to_lowercase() == "none" || alias_str.to_lowercase() == "default" {
                app.app_config.retry_model = None;
                log::debug!("Retry model reset to use original task model.");
            } else if app
                .app_config
                .get_model_config_by_alias(&alias_str)
                .is_some()
            {
                app.app_config.retry_model = Some(alias_str.clone());
                log::debug!("Retry model set to: '{}'.", alias_str);
            } else {
                log::error!(
                    "Command Error: Model alias '{}' not found in configuration.",
                    alias_str
                );
            }
            true
        }
        ParsedCommand::RetryModelShow => {
            app.last_significant_command = None;
            match &app.app_config.retry_model {
                Some(alias) => log::debug!("Current retry model alias: '{}'.", alias),
                None => log::debug!("Retry model is not set (uses original task model)."),
            }
            true
        }
        ParsedCommand::RetryModelMaxAttemptsShow => {
            app.last_significant_command = None;
            log::debug!(
                "Current retry model max attempts: {}",
                app.app_config.retry_model_max_attempts
            );
            true
        }
        ParsedCommand::RetryModelMaxAttemptsSet(attempts_str) => {
            app.last_significant_command = None;
            match attempts_str.parse::<usize>() {
                Ok(attempts) => {
                    app.app_config.retry_model_max_attempts = attempts;
                    log::debug!("Retry model max attempts set to: {}", attempts);
                }
                Err(_) => {
                    log::error!(
                        "Command Error: Invalid number for /retry-model-max-attempts-set: '{}'",
                        attempts_str
                    );
                }
            }
            true
        }
        ParsedCommand::SummaryModelSet(alias_str) => {
            app.last_significant_command = None;
            if alias_str.to_lowercase() == "none" || alias_str.to_lowercase() == "default" {
                app.app_config.summary_model = None;
                log::debug!("Summary model reset to use default model.");
            } else if app
                .app_config
                .get_model_config_by_alias(&alias_str)
                .is_some()
            {
                app.app_config.summary_model = Some(alias_str.clone());
                log::debug!("Summary model set to: '{}'.", alias_str);
            } else {
                log::error!(
                    "Command Error: Model alias '{}' not found in configuration.",
                    alias_str
                );
            }
            true
        }
        ParsedCommand::SummaryModelShow => {
            app.last_significant_command = None;
            match &app.app_config.summary_model {
                Some(alias) => log::debug!("Current summary model alias: '{}'.", alias),
                None => log::debug!("Summary model is not set (uses default model)."),
            }
            true
        }
        ParsedCommand::DecisionModelSet(alias_str) => {
            app.last_significant_command = None;
            if alias_str.to_lowercase() == "none" || alias_str.to_lowercase() == "default" {
                app.app_config.decision_model = None;
                log::debug!("Decision model reset to use default model.");
            } else if app
                .app_config
                .get_model_config_by_alias(&alias_str)
                .is_some()
            {
                app.app_config.decision_model = Some(alias_str.clone());
                log::debug!("Decision model set to: '{}'.", alias_str);
            } else {
                log::error!(
                    "Command Error: Model alias '{}' not found in configuration.",
                    alias_str
                );
            }
            true
        }
        ParsedCommand::DecisionModelShow => {
            app.last_significant_command = None;
            match &app.app_config.decision_model {
                Some(alias) => log::debug!("Current decision model alias: '{}'.", alias),
                None => log::debug!("Decision model is not set (uses default model)."),
            }
            true
        }
        ParsedCommand::Ask(question_text) => {
            app.original_user_request_for_current_processing_sequence = Some(question_text.clone());
            crate::interactive::question_handler::handle_question_command(
                app,
                processing_tx,
                question_text,
                false,
                true,
            )
            .await;
            app.current_operation_model_alias = Some(app.app_config.default_model.clone());
            app.last_significant_command =
                Some(crate::interactive::app::LastSignificantCommandType::Question);
            true
        }
        ParsedCommand::AskAddTaskInfo(question_text) => {
            app.original_user_request_for_current_processing_sequence = Some(question_text.clone());
            crate::interactive::question_handler::handle_question_command(
                app,
                processing_tx,
                question_text,
                true,
                true,
            )
            .await;
            app.current_operation_model_alias = Some(app.app_config.default_model.clone());
            app.last_significant_command =
                Some(crate::interactive::app::LastSignificantCommandType::Question);
            true
        }
        ParsedCommand::AskNoContext(question_text) => {
            app.original_user_request_for_current_processing_sequence = Some(question_text.clone());
            crate::interactive::question_handler::handle_no_context_question_command(
                app,
                processing_tx,
                question_text,
                false,
            )
            .await;
            app.current_operation_model_alias = Some(app.app_config.default_model.clone());
            app.last_significant_command =
                Some(crate::interactive::app::LastSignificantCommandType::Question);
            true
        }
        ParsedCommand::AskNoContextAddTaskInfo(question_text) => {
            app.original_user_request_for_current_processing_sequence = Some(question_text.clone());
            crate::interactive::question_handler::handle_no_context_question_command(
                app,
                processing_tx,
                question_text,
                true,
            )
            .await;
            app.current_operation_model_alias = Some(app.app_config.default_model.clone());
            app.last_significant_command =
                Some(crate::interactive::app::LastSignificantCommandType::Question);
            true
        }
        ParsedCommand::Unknown(cmd_text) => {
            app.last_significant_command = None;
            log::error!(
                "Unknown command or missing argument: {}. Type /help for available commands.",
                cmd_text
            );
            true
        }
        ParsedCommand::NotACommand => false,
    };

    if command_handled {
        if !app.input.is_empty() && app.input_history.last() != Some(&app.input) {
            app.input_history.push(app.input.clone());
        }
        app.current_history_index = None;
        app.current_input_draft.clear();

        app.input.clear();
        app.input_cursor_char_idx = 0;
    }
    command_handled
}