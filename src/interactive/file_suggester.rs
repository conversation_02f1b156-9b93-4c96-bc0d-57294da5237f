use log::trace;
use std::path::{Path, PathBuf};
use fuzzy_matcher::{skim::SkimMatcherV2, FuzzyMatcher};
use tokio::sync::mpsc;
const MAX_VISIBLE_SUGGESTIONS: usize = 4;
const MAX_TOTAL_SUGGESTIONS: usize = 100; // Limit stored suggestions

// Common directory names to ignore during recursive search
const IGNORED_DIRS: &[&str] = &[
    // VCS
    ".git",
    ".svn",
    ".hg",
    // Build outputs & dependencies
    "target",       // Rust
    "node_modules", // Node.js
    "dist",
    "build",
    "out", // Common build outputs
    "bin",
    "obj",         // Common compiled outputs
    "vendor",      // PHP, Go
    "__pycache__", // Python
    // Virtual environments
    ".venv",
    "venv",
    "env",
    // IDE & Editor specific
    ".vscode",
    ".idea",
    // OS specific
    ".DS_Store",
    // Test & Cache
    ".pytest_cache",
    ".mypy_cache",
    ".ruff_cache",
    "coverage",
    // Framework specific
    ".next",       // Next.js
    ".nuxt",       // Nuxt.js
    ".svelte-kit", // SvelteKit
    // Temporary
    "tmp",
    "temp",
    ".aider",
];

#[derive(Debug, Clone)]
pub struct FileSuggestion {
    #[allow(dead_code)] // Field is currently unused but might be in the future
    pub path: PathBuf,
    pub display_name: String,
}

#[derive(Debug)]
pub struct FileSuggesterState {
    pub active: bool,
    search_base_path: PathBuf,
    pub search_term: String,              // Made public
    input_prefix_for_completion: String, // The part of the user's input before the search term. e.g., "src/" if user typed "src/main"
    pub suggestions: Vec<FileSuggestion>, // Made public
    pub selected_index: Option<usize>,
    pub scroll_offset: usize,
    search_task_abort_handle: Option<tokio::task::AbortHandle>,
}

impl FileSuggesterState {
    pub fn new() -> Self {
        Self {
            active: false,
            search_base_path: PathBuf::new(),
            search_term: String::new(),
            input_prefix_for_completion: String::new(),
            suggestions: Vec::new(),
            selected_index: None,
            scroll_offset: 0,
            search_task_abort_handle: None,
        }
    }

    fn deactivate_search_task(&mut self) {
        if let Some(handle) = self.search_task_abort_handle.take() {
            handle.abort();
        }
    }

    pub fn deactivate(&mut self) {
        self.deactivate_search_task();
        self.active = false;
        self.suggestions.clear();
        self.selected_index = None;
        self.scroll_offset = 0;
        self.search_term.clear();
        self.search_base_path = PathBuf::new();
        self.input_prefix_for_completion.clear();
    }

    pub fn set_found_paths(&mut self, paths: Vec<PathBuf>, app_current_path: &PathBuf) {
        // Do not clear suggestions here; append new ones.
        // Clearing is handled in activate_or_update for a new search.
        for path in paths {
            // Limit total suggestions to avoid performance issues and excessive memory use.
            if self.suggestions.len() >= MAX_TOTAL_SUGGESTIONS {
                break;
            }
            let display_name =
                path_to_display_name(&path, &self.search_base_path, app_current_path);
            self.suggestions.push(FileSuggestion { path, display_name });
        }

        // Deduplicate suggestions by path.
        self.suggestions.sort_by(|a, b| a.path.cmp(&b.path));
        self.suggestions.dedup_by(|a, b| a.path == b.path);

        // Standard sort by display name. Directories (ending with /) will typically sort before files with similar names.
        self.suggestions
            .sort_by(|a, b| a.display_name.cmp(&b.display_name));

        if !self.suggestions.is_empty() {
            self.selected_index = Some(0);
        } else {
            self.selected_index = None;
        }
        self.scroll_offset = 0;
        self.active = true; // Keep active if we received paths, even if empty (means search completed)
    }

    pub fn select_next(&mut self) {
        if self.suggestions.is_empty() {
            return;
        }
        self.selected_index = match self.selected_index {
            Some(idx) => Some((idx + 1) % self.suggestions.len()),
            None => Some(0),
        };
        self.adjust_scroll();
    }

    pub fn select_previous(&mut self) {
        if self.suggestions.is_empty() {
            return;
        }
        self.selected_index = match self.selected_index {
            Some(idx) => Some(if idx == 0 {
                self.suggestions.len() - 1
            } else {
                idx - 1
            }),
            None => Some(self.suggestions.len() - 1),
        };
        self.adjust_scroll();
    }

    fn adjust_scroll(&mut self) {
        if let Some(selected_idx) = self.selected_index {
            if selected_idx < self.scroll_offset {
                self.scroll_offset = selected_idx;
            } else if selected_idx >= self.scroll_offset + MAX_VISIBLE_SUGGESTIONS {
                self.scroll_offset = selected_idx - MAX_VISIBLE_SUGGESTIONS + 1;
            }
            let max_possible_offset = self
                .suggestions
                .len()
                .saturating_sub(MAX_VISIBLE_SUGGESTIONS);
            if self.scroll_offset > max_possible_offset {
                self.scroll_offset = max_possible_offset;
            }
        } else {
            self.scroll_offset = 0;
        }
    }

    pub fn get_current_selected_path_for_input(&self) -> Option<String> {
        self.selected_index
            .and_then(|idx| self.suggestions.get(idx))
            .map(|suggestion| {
                // suggestion.path is absolute. We want to complete the user's input.
                // self.input_prefix_for_completion is the part of the user's input *before* the search term.
                // suggestion.path.file_name() is the name of the matched file/dir.
                // If the suggestion is a directory, its path might be what we want.
                // If the suggestion is a file, its name is what we want.
                // The `display_name` is already crafted to be the part that completes the term.
                format!(
                    "{}{}",
                    self.input_prefix_for_completion, suggestion.display_name
                )
            })
    }

    pub fn get_visible_suggestions(&self) -> Vec<&FileSuggestion> {
        if !self.active || self.suggestions.is_empty() {
            return Vec::new();
        }
        self.suggestions
            .iter()
            .skip(self.scroll_offset)
            .take(MAX_VISIBLE_SUGGESTIONS)
            .collect()
    }

    pub fn get_popup_height(&self) -> usize {
        if !self.active || self.suggestions.is_empty() {
            0
        } else {
            self.get_visible_suggestions()
                .len()
                .min(MAX_VISIBLE_SUGGESTIONS)
                + 2
        }
    }
    pub fn activate_from_context_files(
        &mut self,
        existing_paths: &[PathBuf],
        app_current_path: &Path,
        current_segment_being_typed: &str,
        prefix_for_current_segment: String,
    ) {
        self.deactivate(); // Full reset, including aborting any file-system search task

        self.active = true;
        self.input_prefix_for_completion = prefix_for_current_segment;
        self.search_term = current_segment_being_typed.to_lowercase();

        if existing_paths.is_empty() {
            self.suggestions.clear();
            self.selected_index = None;
            self.scroll_offset = 0;
            return;
        }

        let matcher = SkimMatcherV2::default();

        let mut scored_suggestions: Vec<(i64, FileSuggestion)> = existing_paths
            .iter()
            .map(|path| {
                let display_name = path
                    .strip_prefix(app_current_path)
                    .unwrap_or(path)
                    .to_string_lossy()
                    .to_string();
                (path, display_name)
            })
            .filter_map(|(path, display_name)| {
                matcher
                    .fuzzy_match(&display_name, &self.search_term)
                    .map(|score| {
                        (
                            score,
                            FileSuggestion {
                                path: path.clone(),
                                display_name,
                            },
                        )
                    })
            })
            .collect();

        scored_suggestions.sort_by(|a, b| b.0.cmp(&a.0));

        self.suggestions = scored_suggestions
            .into_iter()
            .map(|(_, sugg)| sugg)
            .collect();

        if !self.suggestions.is_empty() {
            self.selected_index = Some(0);
        } else {
            self.selected_index = None;
        }
        self.scroll_offset = 0;
        self.adjust_scroll();
    }
    // This is the single, correct activate_or_update method, now part of the main impl block.
    pub fn activate_or_update(
        &mut self,
        app_current_path: PathBuf,
        current_segment_being_typed: &str,
        prefix_for_current_segment: String, // e.g., "/add existing/path1 "
        results_tx: mpsc::Sender<Vec<PathBuf>>,
    ) {
        let (base_dir_to_search_relative, term_to_search) = {
            let path_input = PathBuf::from(current_segment_being_typed);
            if current_segment_being_typed.is_empty()
                || current_segment_being_typed.ends_with('/')
                || current_segment_being_typed.ends_with(std::path::MAIN_SEPARATOR)
            {
                (path_input, "".to_string()) // Search for empty term in path_input dir
            } else if let Some(parent) = path_input.parent() {
                let term = path_input
                    .file_name()
                    .unwrap_or_default()
                    .to_string_lossy()
                    .to_string();
                if parent.as_os_str().is_empty() {
                    // e.g. user typed "file.txt"
                    (PathBuf::from("."), term)
                } else {
                    (parent.to_path_buf(), term)
                }
            } else {
                // No parent, means current_segment_being_typed is like "file" or "dir"
                (PathBuf::from("."), current_segment_being_typed.to_string())
            }
        };

        let absolute_base_path_to_search = if base_dir_to_search_relative.is_absolute() {
            base_dir_to_search_relative.clone()
        } else {
            app_current_path.join(&base_dir_to_search_relative)
        };

        let normalized_new_base = normalize_path(&absolute_base_path_to_search);
        let normalized_old_base = normalize_path(&self.search_base_path);

        // Check if search parameters (base path and term) have actually changed.
        // Also consider if the input prefix for completion changed, as that affects the final suggested string.
        if self.active
            && normalized_new_base == normalized_old_base
            && self.search_term == term_to_search
            && self.input_prefix_for_completion == prefix_for_current_segment
        {
            return;
        }

        self.deactivate_search_task();

        self.active = true;
        self.search_base_path = absolute_base_path_to_search.clone();
        self.search_term = term_to_search.clone();

        // Construct the correct input_prefix_for_completion.
        // prefix_for_current_segment is from key_handler (e.g., "/add " or "/add path1 ").
        // base_dir_to_search_relative is the directory part of what the user is currently typing (e.g., "src", ".", "../mydir").
        let new_input_prefix_for_completion = {
            let mut base_prefix = prefix_for_current_segment.clone(); // Start with the prefix before the current path argument.
            let base_dir_str = base_dir_to_search_relative.to_string_lossy();

            if !base_dir_str.is_empty() && base_dir_str != "." {
                base_prefix.push_str(&base_dir_str);
                // Ensure it ends with a separator if it's a directory path.
                if !base_dir_str.ends_with(std::path::MAIN_SEPARATOR)
                    && !base_dir_str.ends_with('/')
                {
                    base_prefix.push(std::path::MAIN_SEPARATOR);
                }
            }
            // If base_dir_str is empty or ".", base_prefix (e.g., "/add ") is used as is.
            base_prefix
        };
        self.input_prefix_for_completion = new_input_prefix_for_completion;

        self.suggestions.clear(); // Clear suggestions for a new search or parameter change
        self.selected_index = None;
        self.scroll_offset = 0;

        let search_task_base_path = self.search_base_path.clone();
        let search_task_term = self.search_term.clone();

        let search_task = tokio::spawn(async move {
            let mut found_paths_count = 0;
            recursive_search(
                search_task_base_path,
                &search_task_term.to_lowercase(),
                &results_tx,
                &mut found_paths_count,
                0,
            )
            .await;
            // Signal completion of search explicitly, even if no new paths were found in the last batch
            // This helps the receiver know the search isn't just stalled.
            // Sending an empty Vec can signify this.
            if results_tx.send(Vec::new()).await.is_err() {
                // Log trace or debug if needed, but don't panic or error out the task.
            }
        });
        self.search_task_abort_handle = Some(search_task.abort_handle());
    }
} // End of the first and only impl FileSuggesterState block

fn path_to_display_name(
    abs_path: &Path,
    search_base_path: &Path,
    app_current_path: &Path,
) -> String {
    let mut display_str = String::new();
    let is_dir = abs_path.is_dir(); // Check if it's a directory

    // Try to make it relative to search_base_path first
    if let Ok(rel_path) = abs_path.strip_prefix(search_base_path) {
        if rel_path.components().count() > 0 {
            display_str = rel_path.to_string_lossy().into_owned();
        }
    }

    if display_str.is_empty() {
        // Try to make it relative to app_current_path
        if let Ok(rel_path) = abs_path.strip_prefix(app_current_path) {
            display_str = rel_path.to_string_lossy().into_owned();
        }
    }

    if display_str.is_empty() {
        // Fallback to filename or full path
        display_str = abs_path.file_name().map_or_else(
            || abs_path.to_string_lossy().into_owned(),
            |name| name.to_string_lossy().into_owned(),
        );
    }

    if is_dir && !display_str.ends_with('/') {
        display_str.push('/');
    }
    display_str
}

// Helper to normalize paths for comparison (e.g. resolve "..")
fn normalize_path(path: &Path) -> PathBuf {
    path.canonicalize().unwrap_or_else(|_| path.to_path_buf())
}

async fn recursive_search(
    dir: PathBuf,
    term_lower: &str,
    results_tx: &mpsc::Sender<Vec<PathBuf>>,
    found_paths_count: &mut usize, // Use a mutable ref to count across recursive calls
    depth: usize,
) {
    const MAX_DEPTH: usize = 5; // Limit recursion depth
    if depth > MAX_DEPTH {
        return;
    }

    let mut entries = match tokio::fs::read_dir(&dir).await {
        Ok(entries) => entries,
        Err(_e) => {
            // warn!("Failed to read directory {}: {}", dir.display(), e);
            return;
        }
    };

    let mut batch = Vec::new();

    while let Ok(Some(entry)) = entries.next_entry().await {
        if *found_paths_count >= MAX_TOTAL_SUGGESTIONS {
            return; // Stop searching everywhere
        }

        let path = entry.path();
        if let Some(name) = path.file_name().and_then(|n| n.to_str()) {
            if term_lower.is_empty() || name.to_lowercase().contains(term_lower) {
                batch.push(path.clone());
                *found_paths_count += 1;

                if batch.len() >= 10 {
                    // Send in batches
                    if results_tx.send(batch.clone()).await.is_err() {
                        return; // Stop if channel closed
                    }
                    batch.clear();
                }
            }
        }

        if path.is_dir() {
            let mut should_recurse = true;
            if let Some(dir_name_osstr) = path.file_name() {
                if let Some(dir_name_str) = dir_name_osstr.to_str() {
                    // Check against ignored directory names
                    if IGNORED_DIRS.contains(&dir_name_str) {
                        should_recurse = false;
                    }
                    // Check for dot-directories (but allow if it's the root of the search, i.e., depth == 0)
                    else if dir_name_str.starts_with('.') && depth > 0 {
                        should_recurse = false;
                    }
                }
            }

            if should_recurse {
                Box::pin(recursive_search(
                    path,
                    term_lower,
                    results_tx,
                    found_paths_count,
                    depth + 1,
                ))
                .await;
                if *found_paths_count >= MAX_TOTAL_SUGGESTIONS {
                    // Check after recursive call
                    return;
                }
            }
        }
    }

    if !batch.is_empty() {
        if results_tx.send(batch).await.is_err() {
            trace!("File suggester results channel closed during final batch send in recursive_search.");
        }
    }
}