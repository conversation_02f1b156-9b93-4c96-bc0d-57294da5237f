use crate::config;
use crate::config::AppConfig;
use crate::editor::{run_edit_cycle, ProcessingSignal, ProcessingSuccess};
use crate::files::file_handler::LabeledFile;
use crate::llm::ChatMessage;
use crate::task::Task;
use regex::Regex;
use std::collections::HashMap;
use std::path::PathBuf;
use tokio::sync::mpsc;

// AsyncTaskStateClone struct and its impl block removed as it's unused.

pub fn spawn_new_edit_processing(
    app_config: AppConfig,
    _current_task_for_run: Task, // This Task object has an empty `messages` field initially, prefixed as unused
    pruned_summaries: Vec<ChatMessage>,
    all_previous_tasks: Vec<Task>,
    initial_messages_for_new_task: Vec<ChatMessage>, // Prepared initial messages (pruned summary, user instruction)
    mut ordered_files: crate::files::ordered_files::OrderedFiles, // Now takes ownership and is mutable
    current_display_root: PathBuf,                                // New parameter
    processing_tx: mpsc::Sender<ProcessingSignal>,
    retry_count: usize,
    // categorization_for_files_message: Option<&crate::research::types::CategorizedFilePaths>, // Removed
    forced_research_content_on_entry: Option<String>,
) -> tokio::task::AbortHandle {
    let processing_tx_for_outer_spawn = processing_tx.clone();
    let current_display_root_for_async = current_display_root.clone(); // Clone for async block
                                                                       // let categorization_clone_for_async = categorization_for_files_message.cloned(); // Removed
    let forced_research_content_clone_for_async = forced_research_content_on_entry.clone();

    let task_llm_instance = match config::setup_llm_client(&app_config) {
        Ok(instance) => instance,
        Err(e_str) => {
            log::error!("Failed to setup LLM client: {}", e_str);
            // Send error signal back through processing_tx
            let tx_clone = processing_tx.clone();
            tokio::spawn(async move {
                if tx_clone
                    .send(ProcessingSignal::ProcessingComplete(Err(format!(
                        "LLM setup failed: {}",
                        e_str
                    ))))
                    .await
                    .is_err()
                {
                    log::error!("Failed to send LLM setup error signal");
                }
            });
            // Return a dummy abort handle as the task won't really run
            return tokio::spawn(async {}).abort_handle();
        }
    };

    // messages_for_current_task is now initial_messages_for_new_task
    // task_ordered_files_snapshot is now the owned `ordered_files`

    let join_handle = tokio::spawn(async move {
        run_edit_cycle(
            &app_config,
            task_llm_instance,
            &pruned_summaries,
            &all_previous_tasks,             // Pass as slice
            initial_messages_for_new_task,   // Pass Vec<ChatMessage> by value
            &mut ordered_files,              // Pass mutable OrderedFiles
            &current_display_root_for_async, // Pass current_display_root
            Some(processing_tx.clone()),
            retry_count,
            // categorization_clone_for_async.as_ref(), // Removed
            forced_research_content_clone_for_async,
        )
        .await
    });

    let abort_handle = join_handle.abort_handle();

    tokio::spawn(async move {
        match join_handle.await {
            Ok(inner_result) => {
                match inner_result {
                    Ok((
                        updated_messages,
                        proposed_edits_from_cycle,
                        overall_summary,
                        task_summary_opt,
                        // map_after_cycle, // Removed from destructuring
                        final_included_content_map_from_cycle,
                        new_categorization_from_cycle,
                    )) => {
                        let success_payload = ProcessingSuccess {
                            updated_messages_for_current_task: updated_messages,
                            proposed_edits: proposed_edits_from_cycle,
                            // updated_labeled_files_map: map_after_cycle, // Removed
                            final_included_files_content_map: final_included_content_map_from_cycle,
                            summary_message: overall_summary,
                            task_summary: task_summary_opt,
                            new_categorization: new_categorization_from_cycle,
                            // pending_research_content removed
                        };
                        if processing_tx_for_outer_spawn
                            .send(ProcessingSignal::ProcessingComplete(Ok(success_payload)))
                            .await
                            .is_err()
                        {
                            log::error!("Failed to send processing complete signal");
                        }
                    }
                    Err(e) => {
                        log::error!("Async task failed internally: {}", e);
                        if processing_tx_for_outer_spawn
                            .send(ProcessingSignal::ProcessingComplete(Err(e)))
                            .await
                            .is_err()
                        {
                            log::error!("Failed to send processing error signal (internal error)");
                        }
                    }
                }
            }
            Err(join_error) => {
                let err_msg = if join_error.is_cancelled() {
                    "Task cancelled by user.".to_string()
                } else {
                    format!("Task failed: {}", join_error)
                };
                log::warn!("{}", err_msg);
                if processing_tx_for_outer_spawn
                    .send(ProcessingSignal::ProcessingComplete(Err(err_msg)))
                    .await
                    .is_err()
                {
                    log::error!("Failed to send processing error signal (task aborted/panicked)");
                }
            }
        }
    });

    abort_handle
}

pub fn spawn_new_question_processing(
    app_config_for_question: AppConfig,
    messages_for_llm: Vec<ChatMessage>,
    processing_tx: mpsc::Sender<ProcessingSignal>,
    is_add_to_task_info: bool,
) -> tokio::task::AbortHandle {
    let processing_tx_for_outer_spawn = processing_tx.clone();

    let join_handle = tokio::spawn(async move {
        let llm_client_for_question = match config::setup_llm_client(&app_config_for_question) {
            Ok(instance) => instance,
            Err(e_str) => {
                return Err(format!("LLM setup failed for question: {}", e_str));
            }
        };

        // Extract the original question from the last user message in messages_for_llm
        let original_question = messages_for_llm
            .iter()
            .last()
            .filter(|msg| msg.role == crate::llm::ChatRole::User)
            .map(|msg| msg.content.clone())
            .unwrap_or_else(|| "Unknown question (error extracting from history)".to_string());

        match llm_client_for_question.chat(&messages_for_llm).await {
            Ok(content_string) => Ok((original_question, content_string)), // Return (question, answer)
            Err(e) => Err(format!("LLM call for question failed: {}", e)),
        }
    });

    let abort_handle = join_handle.abort_handle();

    tokio::spawn(async move {
        match join_handle.await {
            Ok(llm_result) => {
                // llm_result is Result<(String, String), String>
                let signal_content = match llm_result {
                    Ok((question, answer)) => Ok((question, answer)),
                    Err(setup_err_msg) => Err(setup_err_msg), // Propagate setup error
                };

                let signal = if is_add_to_task_info {
                    ProcessingSignal::QuestionAnswerAndAddToTaskInfoReceived(signal_content)
                } else {
                    ProcessingSignal::QuestionAnswerReceived(signal_content)
                };
                if processing_tx_for_outer_spawn.send(signal).await.is_err() {
                    log::error!("Failed to send question processing complete signal");
                }
            }
            Err(join_error) => {
                // Task was aborted or panicked (outer spawn)
                let err_msg = if join_error.is_cancelled() {
                    "Question task cancelled by user.".to_string()
                } else {
                    format!("Question task failed/panicked: {}", join_error)
                };
                log::warn!("{}", err_msg);
                let signal = if is_add_to_task_info {
                    ProcessingSignal::QuestionAnswerAndAddToTaskInfoReceived(Err(err_msg))
                } else {
                    ProcessingSignal::QuestionAnswerReceived(Err(err_msg))
                };
                if processing_tx_for_outer_spawn.send(signal).await.is_err() {
                    log::error!("Failed to send question task abort/panic signal");
                }
            }
        }
    });
    abort_handle
}

// New function to spawn research processing
pub fn spawn_new_research_processing(
    app_config_for_research: AppConfig,
    task_id: String,
    task_description: String,
    // initial_files_content: String, // Removed
    historical_context: Vec<ChatMessage>,
    operation_signal_tx: mpsc::Sender<ProcessingSignal>,
    current_path: PathBuf, // New parameter: current path from InteractiveApp
    files_already_in_context: HashMap<PathBuf, LabeledFile>, // New parameter
) -> tokio::task::AbortHandle {
    // Clone values needed for the outer monitoring task *before* they are moved into the first spawn.
    let task_id_for_outer = task_id.clone();
    let task_description_for_outer = task_description.clone();
    let outer_tx_clone = operation_signal_tx.clone();
    let _task_id_for_err_log = task_id.clone(); // Clone for error logging if LLM setup fails

    let join_handle = tokio::spawn(async move {
        let llm_client_for_research = match config::setup_llm_client(&app_config_for_research) {
            Ok(instance) => instance,
            Err(e_str) => {
                // This error occurs before start_research_session is called.
                // We need to send a ResearchComplete signal with this error.
                return Err(format!("LLM setup failed for research: {}", e_str));
            }
        };

        // task_id, task_description, operation_signal_tx are moved into this async block
        crate::research::main_process::start_research_session(
            app_config_for_research,
            llm_client_for_research, // Pass Arc directly
            task_id,                 // Moved
            task_description,        // Moved
            // initial_files_content, // Removed
            historical_context,
            operation_signal_tx, // Moved (this is the original from the function params)
            current_path,        // Pass current_path to start_research_session
            files_already_in_context, // Pass the new parameter
        )
        .await;
        Ok(()) // Ensure the spawn block returns a Result compatible with the Err path
    });

    let abort_handle = join_handle.abort_handle();

    tokio::spawn(async move {
        // This async block uses the _for_outer clones and outer_tx_clone
        match join_handle.await {
            Ok(inner_result) => {
                // inner_result is Result from the spawned task (which itself calls start_research_session)
                // If inner_result is Err, it means LLM setup failed before start_research_session was called.
                if let Err(setup_err_msg) = inner_result {
                    log::warn!("{}", setup_err_msg);
                    if outer_tx_clone
                        .send(ProcessingSignal::ResearchComplete(Err(setup_err_msg)))
                        .await
                        .is_err()
                    {
                        log::error!(
                            "Failed to send research LLM setup error signal for task ID '{}'",
                            task_id_for_outer
                        );
                    }
                }
                // If inner_result is Ok, start_research_session was called.
                // start_research_session itself sends ResearchComplete on its own success/failure.
                // So, nothing more to do here for the Ok(Ok(_)) or Ok(Err(_)) from start_research_session.
            }
            Err(join_error) => {
                // Task was aborted or panicked (outer spawn)
                let err_msg = if join_error.is_cancelled() {
                    format!("Research Task was cancelled by user.")
                } else {
                    format!(
                        "[Task Failed/Panicked] Research task ID '{}' ('{}...') failed: {}",
                        task_id_for_outer,
                        task_description_for_outer
                            .chars()
                            .take(30)
                            .collect::<String>(),
                        join_error
                    )
                };
                log::warn!("{}", err_msg);
                // Send a ResearchComplete signal indicating failure due to abort/panic
                if outer_tx_clone
                    .send(ProcessingSignal::ResearchComplete(Err(err_msg)))
                    .await
                    .is_err()
                {
                    log::error!(
                        "Failed to send research task abort/panic signal for task ID '{}'",
                        task_id_for_outer
                    );
                }
            }
        }
    });
    abort_handle
}

/// Cleans a string by removing ANSI escape codes and other non-essential control characters.
/// Preserves printable characters, tabs, newlines, and carriage returns.
pub fn clean_string_for_terminal(input: &str) -> String {
    // Regex for ANSI escape codes
    let ansi_regex = Regex::new(r"\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])").unwrap();
    // Regex for other unwanted control characters (keeps Tab, LF, CR)
    let control_char_regex = Regex::new(r"[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]").unwrap();

    let without_ansi = ansi_regex.replace_all(input, "");
    control_char_regex
        .replace_all(&without_ansi, "")
        .into_owned()
}