use crate::editor::ProcessingSignal;
use crate::interactive::app::InteractiveApp;
use crate::interactive::async_ops;
use crate::llm::{ChatMessage, ChatRole, MessageType}; // LLMClient removed
use log;
use tokio::sync::mpsc;

pub async fn handle_question_command(
    app: &mut InteractiveApp,
    processing_tx: &mpsc::Sender<ProcessingSignal>,
    question_text: String,
    add_to_task_info: bool,
    include_history: bool, // New parameter
) {
    if app.is_processing || app.is_researching || app.is_asking_question {
        log::warn!("Cannot ask question: another operation is already in progress.");
        return;
    }

    app.is_asking_question = true;
    app.is_processing = false;
    app.is_researching = false;
    app.current_task_start_time = Some(std::time::Instant::now());

    log::debug!("{}", "-".repeat(70));
    if add_to_task_info {
        log::info!(
            "Asking question (and adding to task info): \"{}...\"",
            question_text.chars().take(500).collect::<String>()
        );
    } else {
        log::info!(
            "Asking question: \"{}...\"",
            question_text.chars().take(500).collect::<String>()
        );
    }
    log::debug!("{}", "-".repeat(70));

    let mut messages_for_llm: Vec<ChatMessage> = Vec::new();

    if include_history {
        // 1. Add historical task messages
        messages_for_llm.extend(app.get_historical_context_for_llm());

        // 2. Add historical Q&A messages
        for qna_pair in &app.question_answer_history {
            messages_for_llm.push(ChatMessage {
                role: ChatRole::User,
                content: qna_pair.question.clone(),
                message_type: MessageType::Text,
            });
            messages_for_llm.push(ChatMessage {
                role: ChatRole::Assistant,
                content: qna_pair.answer.clone(),
                message_type: MessageType::Text,
            });
        }
    }

    // 3. Add files message (always, regardless of include_history for this specific function's design)
    let (files_msg_opt, _included_content) = app
        .ordered_files
        .get_files_for_prompt_message(&app.current_path); // Use OrderedFiles method

    if let Some(files_msg) = files_msg_opt {
        messages_for_llm.push(files_msg);
    }

    // 4. Add the current question
    let final_question_prompt_content = if let Some(ti_content) = &app.app_config.task_info {
        if !ti_content.trim().is_empty() {
            format!("Important task context:\n---\n{}\n---\n\nUser's question: {}\n\nGiven all the provided context, please answer the user's question.", ti_content.trim(), question_text)
        } else {
            format!("User's question: {}\n\nGiven all the provided context, please answer the user's question.", question_text)
        }
    } else {
        format!("User's question: {}\n\nGiven all the provided context, please answer the user's question.", question_text)
    };

    let mut effective_question_prompt_content = final_question_prompt_content;
    crate::llm::apply_prefix_based_on_mode(
        &mut effective_question_prompt_content,
        &app.app_config,
        crate::config::app_config::NoThinkMode::Ask,
    );

    messages_for_llm.push(ChatMessage {
        role: ChatRole::User,
        content: effective_question_prompt_content,
        message_type: MessageType::Text,
    });

    let task_app_config = app.app_config.clone();

    let abort_handle = async_ops::spawn_new_question_processing(
        task_app_config, // Pass AppConfig directly
        messages_for_llm,
        processing_tx.clone(),
        add_to_task_info,
    );
    app.current_task_abort_handle = Some(abort_handle);
    // app.input is cleared by the main command handler logic after this returns true
}

pub async fn handle_no_context_question_command(
    app: &mut InteractiveApp,
    processing_tx: &mpsc::Sender<ProcessingSignal>,
    question_text: String,
    add_to_task_info: bool,
) {
    if app.is_processing || app.is_researching || app.is_asking_question {
        log::warn!("Cannot ask question: another operation is already in progress.");
        return;
    }

    app.is_asking_question = true;
    app.is_processing = false;
    app.is_researching = false;
    app.current_task_start_time = Some(std::time::Instant::now());

    log::debug!("{}", "-".repeat(70));
    if add_to_task_info {
        log::info!(
            "Asking question with no context (and adding to task info): \"{}...\"",
            question_text.chars().take(500).collect::<String>()
        );
    } else {
        log::info!(
            "Asking question with no context: \"{}...\"",
            question_text.chars().take(500).collect::<String>()
        );
    }
    log::debug!("{}", "-".repeat(70));

    // For no-context questions, we only send the user's question directly to the LLM
    // No history, no files, no task info - just the raw question
    let mut messages_for_llm: Vec<ChatMessage> = Vec::new();

    messages_for_llm.push(ChatMessage {
        role: ChatRole::User,
        content: question_text,
        message_type: MessageType::Text,
    });

    let task_app_config = app.app_config.clone();

    let abort_handle = async_ops::spawn_new_question_processing(
        task_app_config,
        messages_for_llm,
        processing_tx.clone(),
        add_to_task_info,
    );
    app.current_task_abort_handle = Some(abort_handle);
}
