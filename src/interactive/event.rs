use crossterm::event::{self, Event as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>};
use std::thread; // Still need std::thread for the polling thread
use std::time::Duration;
use tokio::sync::mpsc; // Changed to tokio::sync::mpsc

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)]
pub enum TerminalEvent {
    KeyPress(KeyEvent),
    MouseEvent(MouseEvent), // Added MouseEvent
    Tick,                   // For periodic updates if needed
}

pub struct EventHandler {
    rx: mpsc::Receiver<TerminalEvent>,
    // We might need a way to signal the polling thread to stop if EventHandler is dropped.
    // For now, the thread will run until the sender or receiver is dropped, causing errors on send.
    // let _polling_thread_handle: thread::Jo<PERSON><PERSON><PERSON><PERSON><()>; // To keep the thread alive
}

impl EventHandler {
    pub fn new(tick_rate: Duration) -> Self {
        // Buffer size of 100 for the channel, adjust if needed
        let (tx, rx) = mpsc::channel(100);

        /* let _polling_thread_handle = */
        thread::spawn(move || {
            loop {
                let mut event_sent = false;
                // Poll for keyboard events with a timeout matching part of the tick_rate
                // to allow for timely tick emission.
                if event::poll(tick_rate / 2).unwrap_or(false) {
                    match event::read() {
                        Ok(CrosstermEvent::Key(key_event)) => {
                            if tx
                                .blocking_send(TerminalEvent::KeyPress(key_event))
                                .is_err()
                            {
                                break;
                            }
                            event_sent = true;
                        }
                        Ok(CrosstermEvent::Mouse(mouse_event)) => {
                            if tx
                                .blocking_send(TerminalEvent::MouseEvent(mouse_event))
                                .is_err()
                            {
                                break;
                            }
                            event_sent = true;
                        }
                        _ => { /* Other events like Resize, FocusGained/Lost are ignored for now */
                        }
                    }
                }

                // Send tick event. If the channel is full or closed, log and break.
                // This ensures ticks are sent even if no key events occur.
                if tx.blocking_send(TerminalEvent::Tick).is_err() {
                    // log::debug!("Event handler's receiver dropped (Tick). Stopping poll thread.");
                    break;
                }

                // If an event was sent, we might have used up the poll duration.
                // If not, sleep for the remainder of the tick_rate to maintain the desired frequency.
                if !event_sent {
                    // This sleep is a simplification. A more robust tick timer might be needed
                    // if precise tick intervals are critical alongside event polling.
                    // For now, this aims to send a Tick at roughly tick_rate intervals.
                    // std::thread::sleep(tick_rate / 2); // Already waited tick_rate/2 in poll
                }
            }
        });

        EventHandler {
            rx, /*, _polling_thread_handle*/
        }
    }

    // next() is now an async function, returning Option<TerminalEvent>
    // This is more idiomatic for mpsc::Receiver::recv()
    pub async fn next(&mut self) -> Option<TerminalEvent> {
        self.rx.recv().await
    }
}
