use crate::interactive::commands::COMMAND_DEFINITIONS;
use fuzzy_matcher::skim::SkimMatcherV2;
use fuzzy_matcher::FuzzyMatcher;

const MAX_VISIBLE_SUGGESTIONS: usize = 4;

#[derive(Debug, <PERSON>lone)]
pub struct CommandSuggestion {
    pub name: String,         // e.g., "/add"
    pub display_text: String, // e.g., "/add <path>..."
    pub description: String,  // e.g., "Adds file(s) or folder(s) to context."
}

#[derive(Debug, <PERSON>lone)]
pub struct AutoCompleteState {
    all_commands: Vec<CommandSuggestion>,
    pub filtered_suggestions: Vec<CommandSuggestion>,
    pub selected_index: Option<usize>, // Index within filtered_suggestions
    pub active: bool,
    pub scroll_offset: usize, // For scrolling through more than MAX_VISIBLE_SUGGESTIONS
}

impl AutoCompleteState {
    pub fn new() -> Self {
        let mut commands = Vec::new();
        for cmd_def in COMMAND_DEFINITIONS {
            commands.push(CommandSuggestion {
                name: cmd_def.name.to_string(),
                display_text: cmd_def.display_text.to_string(),
                description: cmd_def.description.to_string(),
            });
        }

        Self {
            all_commands: commands,
            filtered_suggestions: Vec::new(),
            selected_index: None,
            active: false,
            scroll_offset: 0,
        }
    }

    pub fn activate(&mut self, current_input: &str) {
        self.active = true;
        self.filter_suggestions(current_input);
        if !self.filtered_suggestions.is_empty() {
            self.selected_index = Some(0);
        } else {
            self.selected_index = None;
        }
        self.scroll_offset = 0;
    }

    pub fn deactivate(&mut self) {
        self.active = false;
        self.selected_index = None;
        self.filtered_suggestions.clear();
        self.scroll_offset = 0;
    }

    pub fn filter_suggestions(&mut self, input: &str) {
        if !input.starts_with('/') || input.contains(char::is_whitespace) {
            // If not starting with / or contains space (meaning args are being typed),
            // clear suggestions and deactivate.
            self.deactivate();
            return;
        }

        let command_part = input; // e.g., "/ad"
        let matcher = SkimMatcherV2::default();

        let mut scored_suggestions: Vec<(i64, CommandSuggestion)> = self
            .all_commands
            .iter()
            .filter_map(|cmd| {
                matcher
                    .fuzzy_match(&cmd.name, command_part)
                    .map(|score| (score, cmd.clone()))
            })
            .collect();

        // Sort by score in descending order (higher score is better)
        scored_suggestions.sort_by(|a, b| b.0.cmp(&a.0));

        self.filtered_suggestions = scored_suggestions.into_iter().map(|(_, cmd)| cmd).collect();

        if self.filtered_suggestions.is_empty() {
            self.selected_index = None;
            self.active = false; // Deactivate if no suggestions match
        } else {
            self.active = true; // Keep active if there are suggestions
                                // Try to maintain selection or select the first one
                                // If the previous selection is no longer valid (e.g. list changed significantly), reset to 0
            if let Some(current_selected_idx) = self.selected_index {
                if current_selected_idx >= self.filtered_suggestions.len() {
                    self.selected_index = Some(0);
                }
                // If the previously selected item is still in the list, try to keep it.
                // This is complex with fuzzy search as the order can change drastically.
                // For simplicity, we'll just reset to 0 if the list isn't empty.
                // A more sophisticated approach might try to find the old item in the new list.
                // For now, always selecting the top match after filtering is reasonable.
                self.selected_index = Some(0);
            } else {
                self.selected_index = Some(0); // Select first if nothing was selected
            }
        }
        self.adjust_scroll();
    }

    pub fn select_next(&mut self) {
        if self.filtered_suggestions.is_empty() {
            return;
        }
        self.selected_index = match self.selected_index {
            Some(idx) => {
                if idx + 1 < self.filtered_suggestions.len() {
                    Some(idx + 1)
                } else {
                    Some(0) // Wrap around
                }
            }
            None => Some(0),
        };
        self.adjust_scroll();
    }

    pub fn select_previous(&mut self) {
        if self.filtered_suggestions.is_empty() {
            return;
        }
        self.selected_index = match self.selected_index {
            Some(idx) => {
                if idx > 0 {
                    Some(idx - 1)
                } else {
                    Some(self.filtered_suggestions.len() - 1) // Wrap around
                }
            }
            None => Some(self.filtered_suggestions.len() - 1),
        };
        self.adjust_scroll();
    }

    fn adjust_scroll(&mut self) {
        if let Some(selected_idx) = self.selected_index {
            if selected_idx < self.scroll_offset {
                self.scroll_offset = selected_idx;
            } else if selected_idx >= self.scroll_offset + MAX_VISIBLE_SUGGESTIONS {
                self.scroll_offset = selected_idx - MAX_VISIBLE_SUGGESTIONS + 1;
            }
            // Ensure scroll_offset doesn't go too far if list is short
            let max_possible_offset = self
                .filtered_suggestions
                .len()
                .saturating_sub(MAX_VISIBLE_SUGGESTIONS);
            if self.scroll_offset > max_possible_offset {
                self.scroll_offset = max_possible_offset;
            }
        } else {
            self.scroll_offset = 0;
        }
    }

    pub fn get_current_selection_text(&self) -> Option<String> {
        self.selected_index
            .and_then(|idx| self.filtered_suggestions.get(idx))
            .map(|cmd| cmd.name.clone())
    }

    pub fn get_visible_suggestions(&self) -> Vec<CommandSuggestion> {
        if !self.active || self.filtered_suggestions.is_empty() {
            return Vec::new();
        }
        self.filtered_suggestions
            .iter()
            .skip(self.scroll_offset)
            .take(MAX_VISIBLE_SUGGESTIONS)
            .cloned()
            .collect()
    }

    pub fn get_popup_height(&self) -> usize {
        if !self.active || self.filtered_suggestions.is_empty() {
            0
        } else {
            // Height for suggestions + 2 for top/bottom borders of the popup block
            self.get_visible_suggestions()
                .len()
                .min(MAX_VISIBLE_SUGGESTIONS)
                + 2
        }
    }
}
