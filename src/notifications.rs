use directories::UserDirs;
use log::trace;
use std::path::PathBuf;
use std::process::Command as StdCommand; // Renamed to avoid conflict if tokio::process::Command is used later // Added for tilde expansion

/// Truncates a string to a maximum length, adding an ellipsis if truncation occurs.
pub fn truncate_with_ellipsis(s: &str, max_len: usize) -> String {
    if s.chars().count() > max_len {
        let mut truncated = s
            .chars()
            .take(max_len.saturating_sub(3))
            .collect::<String>();
        truncated.push_str("...");
        truncated
    } else {
        s.to_string()
    }
}

/// Executes a user-defined notification command with title and contents set as environment variables.
/// This function spawns a blocking task to avoid blocking the main async executor.
pub fn execute_notification_command(command_str: &str, cwd: &PathBuf, title: &str, contents: &str) {
    if command_str.is_empty() {
        return;
    }

    let expanded_command_str = {
        let mut parts = command_str.splitn(2, char::is_whitespace);
        let program_part = parts.next().unwrap_or("");
        let args_part = parts.next().unwrap_or("");

        let expanded_program = if program_part == "~" {
            UserDirs::new()
                .map(|ud| ud.home_dir().to_string_lossy().into_owned())
                .unwrap_or_else(|| program_part.to_string())
        } else if program_part.starts_with("~/") {
            UserDirs::new()
                .map(|ud| {
                    ud.home_dir()
                        .join(&program_part[2..])
                        .to_string_lossy()
                        .into_owned()
                })
                .unwrap_or_else(|| program_part.to_string())
        } else {
            program_part.to_string()
        };

        if args_part.is_empty() {
            expanded_program
        } else {
            format!("{} {}", expanded_program, args_part)
        }
    };

    let title_owned = title.to_string();
    let contents_owned = contents.to_string();
    let cwd_owned = cwd.clone();
    // Use the expanded command string for execution and logging
    let command_str_owned = expanded_command_str;

    tokio::task::spawn_blocking(move || {
        match StdCommand::new("sh") // Use "sh" as the program to execute
            .arg("-c") // Tell "sh" to execute the following string as a command
            .arg(&command_str_owned) // Pass the user's full (potentially tilde-expanded) command string
            .current_dir(&cwd_owned)
            .env("LLEDIT_NOTIFICATION_TITLE", &title_owned)
            .env("LLEDIT_NOTIFICATION_CONTENTS", &contents_owned)
            .status()
        {
            Ok(status) => {
                if !status.success() {
                    trace!(
                        "Notification command '{}' failed with status: {}",
                        command_str_owned,
                        status
                    );
                }
            }
            Err(e) => {
                trace!(
                    "Failed to execute notification command '{}': {}",
                    command_str_owned,
                    e
                );
            }
        }
    });
}
