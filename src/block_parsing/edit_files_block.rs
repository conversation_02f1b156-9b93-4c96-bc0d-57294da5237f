use crate::block_parsing::traits::{MalformedBlockInfo, ParsableBlock};
use crate::block_parsing::utils::RawCodeBlock;
use crate::files::file_handler::LabeledFile;
use std::error::Error;
use std::fmt;
use std::path::PathBuf;

#[derive(Debug, <PERSON>lone, PartialEq, Eq)]
pub enum EditFilesParsingError {
    EmptyContent, // If the block is empty after the keyword
}

impl fmt::Display for EditFilesParsingError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            EditFilesParsingError::EmptyContent => write!(f, "The 'edit-files' block cannot be empty. It should list file paths likely to be edited."),
        }
    }
}

impl Error for EditFilesParsingError {}

pub struct EditFilesBlockParser;

impl EditFilesBlockParser {
    pub fn parse_to_file_list(
        &self,
        raw_block: &RawCodeBlock,
    ) -> Result<Vec<PathBuf>, EditFilesParsingError> {
        let content = raw_block.content_after_keyword.trim();
        if content.is_empty() {
            // Allow empty if the keyword is present, means LLM found no files for this category.
            return Ok(Vec::new());
        }
        let files: Vec<PathBuf> = content
            .lines()
            .map(|s| s.trim())
            .filter(|s| !s.is_empty())
            .map(PathBuf::from)
            .collect();
        Ok(files)
    }
}

impl ParsableBlock for EditFilesBlockParser {
    fn id(&self) -> String {
        "edit_files_block".to_string()
    }

    fn keyword(&self) -> &'static str {
        "edit-files"
    }

    fn block_format_description(&self) -> String {
        "An 'edit-files' block lists file paths that are likely to be edited. Each path on a new line. Format: ```edit-files\n<path1>\n<path2>\n...\n```".to_string()
    }

    fn validate_raw_block(
        &self,
        raw_block: &RawCodeBlock,
        _available_files: &[LabeledFile],
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        match self.parse_to_file_list(raw_block) {
            Ok(_) => Ok(()),
            Err(e) => Err(Box::new(e)),
        }
    }

    fn identify_malformed(
        &self,
        raw_block: &RawCodeBlock,
        _available_files: &[LabeledFile],
    ) -> Option<MalformedBlockInfo> {
        let content = raw_block.content_after_keyword.trim();
        let parser_id = self.id();

        if raw_block.keyword != self.keyword()
            && (raw_block.keyword.is_empty()
                || raw_block.keyword.to_lowercase().starts_with("edit"))
        {
            if !content.is_empty()
                && content
                    .lines()
                    .any(|line| line.contains('/') || line.contains('.') || line.contains('\\'))
            {
                return Some(MalformedBlockInfo {
                    raw_text: raw_block.full_block_text.clone(),
                    fix_suggestion: format!("This block appears to be a list of files to edit but has a missing or misspelled keyword (expected 'edit-files'). Found '{}'.", raw_block.keyword),
                    parser_id,
                });
            }
        }
        None
    }

    fn construct_fixer_prompt_segment(
        &self,
        malformed_blocks: &[MalformedBlockInfo],
        _available_files: &[LabeledFile],
    ) -> Option<String> {
        if malformed_blocks.is_empty() {
            return None;
        }
        let mut prompt_segment = String::new();
        prompt_segment.push_str("For 'edit-files' blocks (listing files likely to be edited):\n");
        for (index, malformed) in malformed_blocks.iter().enumerate() {
            prompt_segment.push_str(&format!("  Problematic edit-files block #{}:\n", index + 1));
            prompt_segment.push_str("  ```text\n");
            prompt_segment.push_str(&malformed.raw_text.replace("```", "'''"));
            prompt_segment.push_str("\n  ```\n");
            prompt_segment.push_str(&format!("  Issue: {}\n\n", malformed.fix_suggestion));
        }
        prompt_segment.push_str(&format!(
            "  Correct format: {}. Each file path should be on a new line.\n",
            self.block_format_description()
        ));
        Some(prompt_segment)
    }

    fn example_prompt_text(&self) -> String {
        format!("```edit-files\nsrc/main.rs\nsrc/module/api.rs\n```")
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    fn new_raw_code_block(
        keyword: &str,
        content_after_keyword: &str,
        full_block_text: &str,
    ) -> RawCodeBlock {
        RawCodeBlock {
            keyword: keyword.to_string(),
            content_after_keyword: content_after_keyword.to_string(),
            full_block_text: full_block_text.to_string(),
        }
    }

    #[test]
    fn test_parse_to_file_list_valid() {
        let parser = EditFilesBlockParser;
        let raw_block = new_raw_code_block(
            "edit-files",
            "path/to/file1.rs\n  another/path/file2.txt",
            "```edit-files\npath/to/file1.rs\n  another/path/file2.txt\n```",
        );
        let result = parser.parse_to_file_list(&raw_block);
        assert!(result.is_ok());
        let files = result.unwrap();
        assert_eq!(files.len(), 2);
        assert_eq!(files[0], PathBuf::from("path/to/file1.rs"));
        assert_eq!(files[1], PathBuf::from("another/path/file2.txt"));
    }

    #[test]
    fn test_parse_to_file_list_empty_content_yields_empty_vec() {
        let parser = EditFilesBlockParser;
        let raw_block = new_raw_code_block("edit-files", "   ", "```edit-files\n   \n```");
        let result = parser.parse_to_file_list(&raw_block);
        assert!(result.is_ok());
        assert!(result.unwrap().is_empty());
    }

    #[test]
    fn test_validate_raw_block_ok() {
        let parser = EditFilesBlockParser;
        let files: Vec<LabeledFile> = vec![];
        let raw_block_with_files =
            new_raw_code_block("edit-files", "file1.txt", "```edit-files\nfile1.txt\n```");
        assert!(parser
            .validate_raw_block(&raw_block_with_files, &files)
            .is_ok());
        let raw_block_empty_content =
            new_raw_code_block("edit-files", "  ", "```edit-files\n  \n```");
        assert!(parser
            .validate_raw_block(&raw_block_empty_content, &files)
            .is_ok());
    }
}
