use crate::block_parsing::traits::{MalformedBlockInfo, ParsableBlock};
use crate::block_parsing::utils::RawCodeBlock;
use crate::files::file_handler::LabeledFile; // Required by ParsableBlock trait
use std::error::Error;
use std::fmt;

#[derive(Debug, Clone, PartialEq, Eq)]
pub enum ResearchParsingError {
    EmptyContent, // If the block is empty after the keyword but content is expected
}

impl fmt::Display for ResearchParsingError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ResearchParsingError::EmptyContent => write!(f, "The 'research' block cannot be empty. It should contain an explanation for the research assistant."),
        }
    }
}

impl Error for ResearchParsingError {}

pub struct ResearchBlockParser;

impl ResearchBlockParser {
    // Helper to parse content, not part of ParsableBlock trait but useful
    pub fn parse_to_string(
        &self,
        raw_block: &RawCodeBlock,
    ) -> Result<String, ResearchParsingError> {
        let content = raw_block.content_after_keyword.trim();
        if content.is_empty() {
            return Err(ResearchParsingError::EmptyContent);
        }
        Ok(content.to_string())
    }
}

impl ParsableBlock for ResearchBlockParser {
    fn id(&self) -> String {
        "research_block".to_string()
    }

    fn keyword(&self) -> &'static str {
        "research"
    }

    fn block_format_description(&self) -> String {
        "A 'research' block provides a detailed explanation for an AI research assistant to find relevant files. Format: ```research\\n<1-3 paragraph explanation>\\n```".to_string()
    }

    fn validate_raw_block(
        &self,
        raw_block: &RawCodeBlock,
        _available_files: &[LabeledFile],
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        match self.parse_to_string(raw_block) {
            Ok(_) => Ok(()),
            Err(e) => Err(Box::new(e)),
        }
    }

    fn identify_malformed(
        &self,
        raw_block: &RawCodeBlock,
        _available_files: &[LabeledFile],
    ) -> Option<MalformedBlockInfo> {
        let content = raw_block.content_after_keyword.trim();
        let parser_id = self.id();

        if raw_block.keyword != self.keyword()
            && (raw_block.keyword.is_empty() || raw_block.keyword.to_lowercase().starts_with("res"))
        {
            // Keyword is missing or misspelled.
            // Check if content looks like a research explanation (e.g., multiple words, sentences).
            if !content.is_empty() && content.split_whitespace().count() > 5 {
                // Heuristic: more than 5 words
                return Some(MalformedBlockInfo {
                    raw_text: raw_block.full_block_text.clone(),
                    fix_suggestion: format!("This block appears to be a research directive but has a missing or misspelled keyword (expected 'research'). Found '{}'.", raw_block.keyword),
                    parser_id,
                });
            }
        } else if raw_block.keyword == self.keyword() {
            // Keyword is correct, but content might be empty.
            if content.is_empty() {
                return Some(MalformedBlockInfo {
                    raw_text: raw_block.full_block_text.clone(),
                    fix_suggestion: "The 'research' block is empty. Please provide a detailed explanation for the research assistant.".to_string(),
                    parser_id,
                });
            }
        }
        None
    }

    fn construct_fixer_prompt_segment(
        &self,
        malformed_blocks: &[MalformedBlockInfo],
        _available_files: &[LabeledFile],
    ) -> Option<String> {
        if malformed_blocks.is_empty() {
            return None;
        }
        let mut prompt_segment = String::new();
        prompt_segment.push_str(
            "For 'research' blocks (providing detailed explanation for file searching):\n",
        );
        for (index, malformed) in malformed_blocks.iter().enumerate() {
            prompt_segment.push_str(&format!("  Problematic research block #{}:\n", index + 1));
            prompt_segment.push_str("  ```text\n");
            prompt_segment.push_str(&malformed.raw_text.replace("```", "'''"));
            prompt_segment.push_str("\n  ```\n");
            prompt_segment.push_str(&format!("  Issue: {}\n\n", malformed.fix_suggestion));
        }
        prompt_segment.push_str(&format!(
            "  Correct format: {}. The content should be a 1-3 paragraph explanation.\n",
            self.block_format_description()
        ));
        Some(prompt_segment)
    }

    fn example_prompt_text(&self) -> String {
        format!(
            "{}\nExample:\n```research\nThe user wants to update the authentication flow. I need to find the main user model definition, \
any services related to authentication or sessions, and potentially controller files that handle login or registration endpoints. \
Look for files named 'user.rs', 'auth_service.rs', or similar, and check routes in 'main.rs' or 'routes/auth.rs'.\n```",
            self.block_format_description()
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    fn new_raw_code_block(
        keyword: &str,
        content_after_keyword: &str,
        full_block_text: &str,
    ) -> RawCodeBlock {
        RawCodeBlock {
            keyword: keyword.to_string(),
            content_after_keyword: content_after_keyword.to_string(),
            full_block_text: full_block_text.to_string(),
        }
    }

    #[test]
    fn test_parse_to_string_valid() {
        let parser = ResearchBlockParser;
        let raw_block = new_raw_code_block(
            "research",
            "Find all user models and auth services.",
            "```research\nFind all user models and auth services.\n```",
        );
        let result = parser.parse_to_string(&raw_block);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "Find all user models and auth services.");
    }

    #[test]
    fn test_parse_to_string_empty_content_error() {
        let parser = ResearchBlockParser;
        let raw_block = new_raw_code_block("research", "   ", "```research\n   \n```");
        let result = parser.parse_to_string(&raw_block);
        assert_eq!(result, Err(ResearchParsingError::EmptyContent));
    }

    #[test]
    fn test_validate_raw_block_valid() {
        let parser = ResearchBlockParser;
        let files: Vec<LabeledFile> = vec![];
        let raw_block = new_raw_code_block(
            "research",
            "Valid research content.",
            "```research\nValid research content.\n```",
        );
        assert!(parser.validate_raw_block(&raw_block, &files).is_ok());
    }

    #[test]
    fn test_validate_raw_block_empty_content_error() {
        let parser = ResearchBlockParser;
        let files: Vec<LabeledFile> = vec![];
        let raw_block = new_raw_code_block("research", "", "```research\n```");
        let result = parser.validate_raw_block(&raw_block, &files);
        assert!(result.is_err());
        match result.err().unwrap().downcast_ref::<ResearchParsingError>() {
            Some(ResearchParsingError::EmptyContent) => {} // Expected
            _ => panic!("Incorrect error type"),
        }
    }

    #[test]
    fn test_identify_malformed_missing_keyword_looks_like_research() {
        let parser = ResearchBlockParser;
        let files: Vec<LabeledFile> = vec![];
        let raw_block = new_raw_code_block(
            "",
            "This looks like a fairly detailed research explanation with several words.",
            "```\nThis looks like a fairly detailed research explanation with several words.\n```",
        );
        let malformed_info = parser.identify_malformed(&raw_block, &files);
        assert!(malformed_info.is_some());
        let info = malformed_info.unwrap();
        assert_eq!(info.parser_id, "research_block");
        assert!(info
            .fix_suggestion
            .contains("missing or misspelled keyword (expected 'research')"));
    }

    #[test]
    fn test_identify_malformed_correct_keyword_empty_content() {
        let parser = ResearchBlockParser;
        let files: Vec<LabeledFile> = vec![];
        let raw_block = new_raw_code_block("research", "  ", "```research\n  \n```");
        let malformed_info = parser.identify_malformed(&raw_block, &files);
        assert!(malformed_info.is_some());
        let info = malformed_info.unwrap();
        assert_eq!(info.parser_id, "research_block");
        assert!(info.fix_suggestion.contains("block is empty"));
    }

    #[test]
    fn test_identify_malformed_correct_keyword_valid_content_returns_none() {
        let parser = ResearchBlockParser;
        let files: Vec<LabeledFile> = vec![];
        let raw_block = new_raw_code_block(
            "research",
            "Valid research explanation.",
            "```research\nValid research explanation.\n```",
        );
        let malformed_info = parser.identify_malformed(&raw_block, &files);
        assert!(malformed_info.is_none());
    }
}
