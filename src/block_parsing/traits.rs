use crate::block_parsing::utils::RawCodeBlock;
use crate::files::file_handler::LabeledFile; // For method signatures needing file context
use std::error::Error;
use std::fmt::Debug; // Keep Debug for MalformedBlockInfo if it derives Debug

#[derive(<PERSON>bu<PERSON>, <PERSON>lone)]
pub struct MalformedBlockInfo {
    pub raw_text: String,       // The full text of the malformed block.
    pub fix_suggestion: String, // A parser-specific suggestion on how to fix it.
    pub parser_id: String,      // Identifier of the parser that flagged this block.
}

// Trait for any parsable block type
pub trait ParsableBlock: Send + Sync {
    // A unique identifier for this parser instance/type. Can be the keyword.
    fn id(&self) -> String;

    // Returns the primary keyword that identifies this block type (e.g., "range").
    // Used to quickly associate RawCodeBlocks with their potential parsers.
    fn keyword(&self) -> &'static str;

    // A human-readable description of the block, its purpose, and its expected format.
    // Used in prompts to guide the LLM. Example: "A 'range' block specifies a file path and line numbers (e.g., path/to/file.ext\nSTART-END)."
    fn block_format_description(&self) -> String;

    // Validates if a RawCodeBlock can be successfully parsed according to this parser's rules.
    // Returns Ok(()) if validation/parsing is successful.
    // Returns Err(Box<dyn Error>) if parsing fails, allowing the error to be inspected.
    // This should typically be called if raw_block.keyword matches self.keyword().
    // Implementers will call their concrete parsing logic internally.
    // `available_files` provides context for validation (e.g., file existence for range blocks).
    fn validate_raw_block(
        &self,
        raw_block: &RawCodeBlock,
        available_files: &[LabeledFile],
    ) -> Result<(), Box<dyn Error + Send + Sync>>;

    // Identifies if a RawCodeBlock (that might not have the correct keyword or failed parsing via validate_raw_block)
    // appears to be an attempt at this block type but is malformed.
    // This allows for more targeted error messages and retry prompts.
    // `available_files` provides context for identifying malformations (e.g., path checks).
    fn identify_malformed(
        &self,
        raw_block: &RawCodeBlock,
        available_files: &[LabeledFile],
    ) -> Option<MalformedBlockInfo>;

    // Constructs a part of the prompt to ask the LLM to fix specifically identified malformed blocks
    // relevant to *this* parser.
    // `available_files` provides context for fixer prompts (e.g., listing valid file paths).
    fn construct_fixer_prompt_segment(
        &self,
        malformed_blocks: &[MalformedBlockInfo],
        available_files: &[LabeledFile],
    ) -> Option<String>;

    // Provides a string detailing the block's specification and an example for use in prompts.
    fn example_prompt_text(&self) -> String;
}
