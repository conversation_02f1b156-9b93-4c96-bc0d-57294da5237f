use crate::block_parsing::traits::{MalformedBlockInfo, ParsableBlock};
use crate::block_parsing::utils::RawCodeBlock;
use crate::editor::types::EditTarget; // Reusing EditTarget from range_blocks
use crate::files::file_handler::LabeledFile;
use regex::Regex;
use std::error::Error;
use std::fmt;
use std::path::PathBuf;

#[derive(Debug, Clone, PartialEq, Eq)]
pub struct ParsedRangeReplace {
    pub edit_target: EditTarget,
    pub replacement_content: String,
}

#[derive(Debug, Clone, PartialEq, Eq)]
pub enum RangeReplaceParsingError {
    BlockEmpty,
    TooFewLines, // Expects path, range, and 3 markers
    PathMissing,
    RangeLineMissing,
    InvalidLinesFormat(String),
    StartLineZero,
    EndLineZero,
    StartGreaterThanEnd,
    RangeMarkerMissing,
    SeparatorMissing,
    EndMarkerMissing,
    FileDoesNotExist(PathBuf),
    FileAlreadyExistsForNew(PathBuf),
    IncompleteRangeSingleNumber,
}

impl fmt::Display for RangeReplaceParsingError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            RangeReplaceParsingError::BlockEmpty => write!(f, "The 'range-replace' block is empty."),
            RangeReplaceParsingError::TooFewLines => write!(f, "The 'range-replace' block has too few lines. Expected file path, line range, '<<<<<<< RANGE', '=======', and '>>>>>>> REPLACE' markers."),
            RangeReplaceParsingError::PathMissing => write!(f, "File path is missing from the first line of the 'range-replace' block."),
            RangeReplaceParsingError::RangeLineMissing => write!(f, "Line range is missing from the second line of the 'range-replace' block."),
            RangeReplaceParsingError::InvalidLinesFormat(lines) => write!(f, "Invalid line number format: '{}'. Expected START-END.", lines),
            RangeReplaceParsingError::StartLineZero => write!(f, "Start line cannot be 0 unless creating a new file (0-0)."),
            RangeReplaceParsingError::EndLineZero => write!(f, "End line cannot be 0 unless creating a new file (0-0)."),
            RangeReplaceParsingError::StartGreaterThanEnd => write!(f, "Start line cannot be greater than end line."),
            RangeReplaceParsingError::RangeMarkerMissing => write!(f, "The '<<<<<<< RANGE' marker is missing after the line range in the 'range-replace' block."),
            RangeReplaceParsingError::SeparatorMissing => write!(f, "The '=======' separator is missing in the 'range-replace' block."),
            RangeReplaceParsingError::EndMarkerMissing => write!(f, "The '>>>>>>> REPLACE' marker is missing at the end of the 'range-replace' block."),
            RangeReplaceParsingError::FileDoesNotExist(path) => write!(f, "File '{}' does not exist.", path.display()),
            RangeReplaceParsingError::FileAlreadyExistsForNew(path) => write!(f, "File '{}' already exists. Range 0-0 is for new files.", path.display()),
            RangeReplaceParsingError::IncompleteRangeSingleNumber => write!(f, "Range format is incomplete. Expected 'START-END', but only one number was found."),
        }
    }
}

impl Error for RangeReplaceParsingError {}

pub struct RangeReplaceBlockParser;

impl RangeReplaceBlockParser {
    // Regex for "START-END" format for lines.
    fn line_range_re() -> Regex {
        Regex::new(r"^\s*([^- \t\r\n]+)-([^\s\t\r\n]+)\s*$")
            .expect("Failed to compile line-range regex for RangeReplaceBlockParser")
    }

    // Regex for "NUMBER" format (missing range end).
    fn single_number_re() -> Regex {
        Regex::new(r"^\s*(\d+)\s*$")
            .expect("Failed to compile single-number regex for RangeReplaceBlockParser")
    }

    pub fn parse_to_target_and_content(
        &self,
        raw_block: &RawCodeBlock,
        // available_files is for validation, not strictly parsing structure
        _available_files: &[LabeledFile],
    ) -> Result<ParsedRangeReplace, RangeReplaceParsingError> {
        let content = raw_block.content_after_keyword.trim_start(); // Trim only leading whitespace
        if content.is_empty() {
            return Err(RangeReplaceParsingError::BlockEmpty);
        }

        let lines: Vec<&str> = content.lines().collect();

        if lines.len() < 5 {
            // path, range, <<<, ===, >>>
            return Err(RangeReplaceParsingError::TooFewLines);
        }

        let file_path_str = lines[0].trim();
        if file_path_str.is_empty() {
            return Err(RangeReplaceParsingError::PathMissing);
        }
        let file_path = PathBuf::from(file_path_str);

        let range_line_str = lines[1].trim();
        if range_line_str.is_empty() {
            return Err(RangeReplaceParsingError::RangeLineMissing);
        }

        let (start_line, end_line) =
            if let Some(caps) = Self::line_range_re().captures(range_line_str) {
                let start_str = caps.get(1).map_or("", |m| m.as_str());
                let end_str = caps.get(2).map_or("", |m| m.as_str());
                match (start_str.parse::<usize>(), end_str.parse::<usize>()) {
                    (Ok(s), Ok(e)) => (s, e),
                    _ => {
                        return Err(RangeReplaceParsingError::InvalidLinesFormat(
                            range_line_str.to_string(),
                        ))
                    }
                }
            } else if Self::single_number_re().is_match(range_line_str) {
                return Err(RangeReplaceParsingError::IncompleteRangeSingleNumber);
            } else {
                return Err(RangeReplaceParsingError::InvalidLinesFormat(
                    range_line_str.to_string(),
                ));
            };

        let mut validated_start_line = start_line;
        if validated_start_line == 0 && end_line != 0 {
            // Auto-correct 0-N to 1-N
            validated_start_line = 1;
        }

        if validated_start_line == 0 && end_line == 0 {
            // Valid: 0-0 for new file
        } else if validated_start_line == 0 {
            return Err(RangeReplaceParsingError::StartLineZero);
        } else if end_line == 0 {
            return Err(RangeReplaceParsingError::EndLineZero);
        } else if validated_start_line > end_line {
            return Err(RangeReplaceParsingError::StartGreaterThanEnd);
        }
        // All other N-M (0 < N <= M) are valid at this stage of parsing.

        let edit_target = EditTarget {
            file_path,
            start_line: validated_start_line,
            end_line,
        };

        if lines[2].trim() != "<<<<<<< RANGE" {
            return Err(RangeReplaceParsingError::RangeMarkerMissing);
        }

        let separator_line_index = lines.iter().position(|&l| l.trim() == "=======");
        if separator_line_index.is_none() {
            return Err(RangeReplaceParsingError::SeparatorMissing);
        }
        let separator_line_index = separator_line_index.unwrap();

        if separator_line_index <= 2 {
            return Err(RangeReplaceParsingError::SeparatorMissing);
        }

        let end_marker_line_index = lines.iter().rposition(|&l| l.trim() == ">>>>>>> REPLACE");
        if end_marker_line_index.is_none() {
            return Err(RangeReplaceParsingError::EndMarkerMissing);
        }
        let end_marker_line_index = end_marker_line_index.unwrap();

        if end_marker_line_index <= separator_line_index {
            return Err(RangeReplaceParsingError::EndMarkerMissing);
        }

        let replacement_content =
            lines[separator_line_index + 1..end_marker_line_index].join("\n");

        Ok(ParsedRangeReplace {
            edit_target,
            replacement_content,
        })
    }
}

impl ParsableBlock for RangeReplaceBlockParser {
    fn id(&self) -> String {
        "range_replace_block".to_string()
    }

    fn keyword(&self) -> &'static str {
        "range-replace"
    }

    fn block_format_description(&self) -> String {
        "A 'range-replace' block specifies a file, a 1-indexed line range (or 0-0 for new file), and the new content using special markers. Format:\n```range-replace\npath/to/file.ext\nSTART_LINE-END_LINE\n<<<<<<< RANGE\n(optional original code, which is ignored)\n=======\nnew_content_here\n>>>>>>> REPLACE\n```".to_string()
    }

    fn validate_raw_block(
        &self,
        raw_block: &RawCodeBlock,
        available_files: &[LabeledFile],
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        let parsed_data = self.parse_to_target_and_content(raw_block, available_files)?;
        let edit_target = parsed_data.edit_target;

        // File existence validation (similar to RangeBlockParser)
        let current_dir = std::env::current_dir()
            .map_err(|e| format!("Failed to get current directory: {}", e))?;
        let resolved_path_for_validation = if edit_target.file_path.is_absolute() {
            edit_target.file_path.clone()
        } else {
            current_dir.join(&edit_target.file_path)
        };

        if edit_target.start_line == 0 && edit_target.end_line == 0 {
            // New file or overwrite empty file
            if let Some(existing_labeled_file) = available_files
                .iter()
                .find(|f| f.path == resolved_path_for_validation)
            {
                // File is in context
                if !existing_labeled_file.get_original_lines().is_empty() {
                    // In context and not empty, this is an error for 0-0
                    return Err(Box::new(RangeReplaceParsingError::FileAlreadyExistsForNew(
                        resolved_path_for_validation.clone(),
                    )));
                }
                // In context and empty, 0-0 is allowed (overwrite empty file)
            } else if resolved_path_for_validation.exists() {
                // File exists on disk but not in context. For 0-0, this is an error.
                // We can't synchronously check if it's empty here.
                // The assumption is 0-0 is for new files or known-empty files.
                let path_for_error = dunce::canonicalize(&resolved_path_for_validation)
                    .unwrap_or_else(|_| resolved_path_for_validation.clone());
                return Err(Box::new(RangeReplaceParsingError::FileAlreadyExistsForNew(
                    path_for_error,
                )));
            }
            // If not in context and not on disk, it's a new file, which is fine for 0-0.
        } else {
            // Existing file, range is not 0-0
            match dunce::canonicalize(&resolved_path_for_validation) {
                Ok(canonical_path) => {
                    if !canonical_path.exists()
                        || !available_files.iter().any(|f| f.path == canonical_path)
                    {
                        return Err(Box::new(RangeReplaceParsingError::FileDoesNotExist(
                            canonical_path,
                        )));
                    }
                }
                Err(_) => {
                    return Err(Box::new(RangeReplaceParsingError::FileDoesNotExist(
                        resolved_path_for_validation,
                    )));
                }
            }
        }
        Ok(())
    }

    fn identify_malformed(
        &self,
        raw_block: &RawCodeBlock,
        available_files: &[LabeledFile],
    ) -> Option<MalformedBlockInfo> {
        let parser_id = self.id();
        // Attempt to parse; if it fails, use the error to generate a suggestion.
        // This covers cases where the keyword is correct but content is malformed.
        if raw_block.keyword == self.keyword() {
            return match self.parse_to_target_and_content(raw_block, available_files) {
                Ok(parsed_data) => {
                    // Parsed structurally, now validate file existence
                    let edit_target = parsed_data.edit_target;
                    let current_dir = std::env::current_dir().ok()?;
                    let resolved_path = if edit_target.file_path.is_absolute() {
                        edit_target.file_path.clone()
                    } else {
                        current_dir.join(&edit_target.file_path)
                    };
                    if edit_target.start_line == 0 && edit_target.end_line == 0 {
                        if resolved_path.exists() {
                            Some(MalformedBlockInfo {
                                raw_text: raw_block.full_block_text.clone(),
                                fix_suggestion: RangeReplaceParsingError::FileAlreadyExistsForNew(
                                    dunce::canonicalize(&resolved_path).unwrap_or(resolved_path),
                                )
                                .to_string(),
                                parser_id,
                            })
                        } else {
                            None
                        }
                    } else {
                        match dunce::canonicalize(&resolved_path) {
                            Ok(canonical_path) => {
                                if !canonical_path.exists()
                                    || !available_files.iter().any(|f| f.path == canonical_path)
                                {
                                    Some(MalformedBlockInfo {
                                        raw_text: raw_block.full_block_text.clone(),
                                        fix_suggestion: RangeReplaceParsingError::FileDoesNotExist(
                                            canonical_path,
                                        )
                                        .to_string(),
                                        parser_id,
                                    })
                                } else {
                                    None
                                }
                            }
                            Err(_) => Some(MalformedBlockInfo {
                                raw_text: raw_block.full_block_text.clone(),
                                fix_suggestion: RangeReplaceParsingError::FileDoesNotExist(
                                    resolved_path,
                                )
                                .to_string(),
                                parser_id,
                            }),
                        }
                    }
                }
                Err(e) => Some(MalformedBlockInfo {
                    // Structural parsing error
                    raw_text: raw_block.full_block_text.clone(),
                    fix_suggestion: e.to_string(),
                    parser_id,
                }),
            };
        }

        // Keyword is missing or misspelled. Check if content looks like a range-replace.
        if raw_block.keyword.is_empty() || raw_block.keyword.to_lowercase().starts_with("range-r") {
            let content = raw_block.content_after_keyword.trim_start();
            let lines: Vec<&str> = content.lines().collect();
            if lines.len() >= 5 {
                // Potential path, range, markers
                let path_looks_ok = !lines[0].trim().is_empty()
                    && (lines[0].contains('/')
                        || lines[0].contains('.')
                        || PathBuf::from(lines[0].trim()).components().count() > 0);
                let range_looks_ok = Self::line_range_re().is_match(lines[1].trim());
                let range_marker_ok = lines[2].trim() == "<<<<<<< RANGE";
                let separator_looks_ok = lines.iter().any(|l| l.trim() == "=======");
                let end_marker_ok = lines.iter().any(|l| l.trim() == ">>>>>>> REPLACE");

                if path_looks_ok && range_looks_ok && range_marker_ok && separator_looks_ok && end_marker_ok {
                    return Some(MalformedBlockInfo {
                        raw_text: raw_block.full_block_text.clone(),
                        fix_suggestion: format!("This block appears to be a 'range-replace' block but has a missing or misspelled keyword (expected 'range-replace'). Found '{}'.", raw_block.keyword),
                        parser_id,
                    });
                }
            }
        }
        None
    }

    fn construct_fixer_prompt_segment(
        &self,
        malformed_blocks: &[MalformedBlockInfo],
        available_files: &[LabeledFile],
    ) -> Option<String> {
        if malformed_blocks.is_empty() {
            return None;
        }
        let mut prompt_segment = String::new();
        prompt_segment.push_str(
            "For 'range-replace' blocks (specifying file, range, and replacement content):\n",
        );

        let show_files_hint = malformed_blocks.iter().any(|b| {
            b.fix_suggestion.contains("does not exist")
                || b.fix_suggestion.contains("already exists")
        });

        if show_files_hint && !available_files.is_empty() {
            prompt_segment
                .push_str("  Hint: Ensure file paths are correct. Available files for editing:\n");
            for lf in available_files {
                let display_path = lf
                    .path
                    .strip_prefix(std::env::current_dir().unwrap_or_default())
                    .unwrap_or(&lf.path)
                    .display();
                prompt_segment.push_str(&format!("    - {}\n", display_path));
            }
            prompt_segment.push_str(
                "  Use 'path/to/new/file.ext\\n0-0\\n<<<<<<< RANGE\\n=======\\ncontent\\n>>>>>>> REPLACE' to create a new file.\n",
            );
        }

        for (index, malformed) in malformed_blocks.iter().enumerate() {
            prompt_segment.push_str(&format!(
                "  Problematic range-replace block #{}:\n",
                index + 1
            ));
            prompt_segment.push_str("  ```text\n");
            prompt_segment.push_str(&malformed.raw_text.replace("```", "'''"));
            prompt_segment.push_str("\n  ```\n");
            prompt_segment.push_str(&format!("  Issue: {}\n\n", malformed.fix_suggestion));
        }
        prompt_segment.push_str(&format!(
            "  Correct format: {}\n",
            self.block_format_description()
        ));
        Some(prompt_segment)
    }

    fn example_prompt_text(&self) -> String {
        format!(
            "{}\\nExample for editing an existing file:\n```range-replace\\nsrc/main.rs\\n10-12\\n<<<<<<< RANGE\\n// old code here is ignored by the parser\\n=======\\nlet new_code = true;\\nprintln!(\"Updated!\");\\n>>>>>>> REPLACE\\n```\\nExample for creating a new file:\n```range-replace\\nsrc/new_module.rs\\n0-0\\n<<<<<<< RANGE\\n=======\\n// This is a new file\\npub fn hello() {{}}\\n>>>>>>> REPLACE\\n```",
            self.block_format_description()
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::env;
    use std::io::Write;
    use tempfile::TempDir;

    fn new_raw_code_block(
        keyword: &str,
        content_after_keyword: &str,
        full_block_text: &str,
    ) -> RawCodeBlock {
        RawCodeBlock {
            keyword: keyword.to_string(),
            content_after_keyword: content_after_keyword.to_string(),
            full_block_text: full_block_text.to_string(),
        }
    }

    // Helper to create a LabeledFile for tests.
    // Creates path_str relative to a *new* temp dir.
    // Returns the LabeledFile and the TempDir guard.
    fn new_test_labeled_file_in_new_temp_dir(
        path_str_in_temp: &str,
        content: &str,
    ) -> (LabeledFile, TempDir) {
        let temp_dir = tempfile::tempdir().expect("Failed to create temp dir");
        let test_path = temp_dir.path().join(path_str_in_temp);

        if let Some(parent) = test_path.parent() {
            std::fs::create_dir_all(parent).expect("Failed to create parent dirs for test file");
        }
        let mut file = std::fs::File::create(&test_path).unwrap();
        writeln!(file, "{}", content).unwrap();

        let labeled_file = LabeledFile::new(dunce::canonicalize(test_path).unwrap(), content);
        (labeled_file, temp_dir)
    }

    #[test]
    fn test_parse_valid_edit() {
        let parser = RangeReplaceBlockParser;
        let raw_block = new_raw_code_block(
            "range-replace",
            "src/main.rs\n10-12\n<<<<<<< RANGE\n// old code\n=======\nlet x = 5;\nprintln!(\"{}\", x);\n>>>>>>> REPLACE",
            "```range-replace\nsrc/main.rs\n10-12\n<<<<<<< RANGE\n// old code\n=======\nlet x = 5;\nprintln!(\"{}\", x);\n>>>>>>> REPLACE\n```",
        );
        let result = parser.parse_to_target_and_content(&raw_block, &[]);
        assert!(result.is_ok());
        let parsed = result.unwrap();
        assert_eq!(parsed.edit_target.file_path, PathBuf::from("src/main.rs"));
        assert_eq!(parsed.edit_target.start_line, 10);
        assert_eq!(parsed.edit_target.end_line, 12);
        assert_eq!(
            parsed.replacement_content,
            "let x = 5;\nprintln!(\"{}\", x);"
        );
    }

    #[test]
    fn test_parse_valid_new_file() {
        let parser = RangeReplaceBlockParser;
        let raw_block = new_raw_code_block(
            "range-replace",
            "new_file.txt\n0-0\n<<<<<<< RANGE\n=======\nHello, new file!\n>>>>>>> REPLACE",
            "```range-replace\nnew_file.txt\n0-0\n<<<<<<< RANGE\n=======\nHello, new file!\n>>>>>>> REPLACE\n```",
        );
        let result = parser.parse_to_target_and_content(&raw_block, &[]);
        assert!(result.is_ok());
        let parsed = result.unwrap();
        assert_eq!(parsed.edit_target.file_path, PathBuf::from("new_file.txt"));
        assert_eq!(parsed.edit_target.start_line, 0);
        assert_eq!(parsed.edit_target.end_line, 0);
        assert_eq!(parsed.replacement_content, "Hello, new file!");
    }

    #[test]
    fn test_parse_empty_replacement_content() {
        let parser = RangeReplaceBlockParser;
        let raw_block = new_raw_code_block(
            "range-replace",
            "src/main.rs\n5-5\n<<<<<<< RANGE\n=======\n>>>>>>> REPLACE",
            "```range-replace\nsrc/main.rs\n5-5\n<<<<<<< RANGE\n=======\n>>>>>>> REPLACE\n```",
        );
        let result = parser.parse_to_target_and_content(&raw_block, &[]);
        assert!(result.is_ok(), "Error: {:?}", result.err());
        let parsed = result.unwrap();
        assert_eq!(parsed.replacement_content, "");
    }

    #[test]
    fn test_parse_replacement_content_with_leading_trailing_newlines() {
        let parser = RangeReplaceBlockParser;
        let raw_block = new_raw_code_block(
            "range-replace",
            "src/main.rs\n5-5\n<<<<<<< RANGE\n=======\n\nActual content\n\n\n>>>>>>> REPLACE",
            "```range-replace\nsrc/main.rs\n5-5\n<<<<<<< RANGE\n=======\n\nActual content\n\n\n>>>>>>> REPLACE\n```",
        );
        let result = parser.parse_to_target_and_content(&raw_block, &[]);
        assert!(result.is_ok(), "Error: {:?}", result.err());
        let parsed = result.unwrap();
        assert_eq!(parsed.replacement_content, "\nActual content\n\n");
    }

    #[test]
    fn test_parse_error_too_few_lines() {
        let parser = RangeReplaceBlockParser;
        let raw_block = new_raw_code_block(
            "range-replace",
            "src/main.rs\n10-12\n<<<<<<< RANGE\n=======",
            "```range-replace\nsrc/main.rs\n10-12\n<<<<<<< RANGE\n=======\n```",
        );
        let result = parser.parse_to_target_and_content(&raw_block, &[]);
        assert_eq!(result, Err(RangeReplaceParsingError::TooFewLines));
    }

    #[test]
    fn test_parse_error_missing_range_marker() {
        let parser = RangeReplaceBlockParser;
        let raw_block = new_raw_code_block(
            "range-replace",
            "src/main.rs\n10-12\n=======\nContent\n>>>>>>> REPLACE",
            "```range-replace\nsrc/main.rs\n10-12\n=======\nContent\n>>>>>>> REPLACE\n```",
        );
        let result = parser.parse_to_target_and_content(&raw_block, &[]);
        assert_eq!(result, Err(RangeReplaceParsingError::RangeMarkerMissing));
    }

    #[test]
    fn test_parse_error_missing_separator() {
        let parser = RangeReplaceBlockParser;
        let raw_block = new_raw_code_block(
            "range-replace",
            "src/main.rs\n10-12\n<<<<<<< RANGE\nContent\n>>>>>>> REPLACE",
            "```range-replace\nsrc/main.rs\n10-12\n<<<<<<< RANGE\nContent\n>>>>>>> REPLACE\n```",
        );
        let result = parser.parse_to_target_and_content(&raw_block, &[]);
        assert_eq!(result, Err(RangeReplaceParsingError::SeparatorMissing));
    }

    #[test]
    fn test_parse_error_missing_end_marker() {
        let parser = RangeReplaceBlockParser;
        let raw_block = new_raw_code_block(
            "range-replace",
            "src/main.rs\n10-12\n<<<<<<< RANGE\n=======\nContent",
            "```range-replace\nsrc/main.rs\n10-12\n<<<<<<< RANGE\n=======\nContent\n```",
        );
        let result = parser.parse_to_target_and_content(&raw_block, &[]);
        assert_eq!(result, Err(RangeReplaceParsingError::EndMarkerMissing));
    }

    #[test]
    fn test_parse_error_invalid_range_format() {
        let parser = RangeReplaceBlockParser;
        let raw_block = new_raw_code_block(
            "range-replace",
            "src/main.rs\n10-abc\n<<<<<<< RANGE\n=======\nContent\n>>>>>>> REPLACE",
            "```range-replace\nsrc/main.rs\n10-abc\n<<<<<<< RANGE\n=======\nContent\n>>>>>>> REPLACE\n```",
        );
        let result = parser.parse_to_target_and_content(&raw_block, &[]);
        assert_eq!(
            result,
            Err(RangeReplaceParsingError::InvalidLinesFormat(
                "10-abc".to_string()
            ))
        );
    }

    #[test]
    fn test_parse_error_start_greater_than_end() {
        let parser = RangeReplaceBlockParser;
        let raw_block = new_raw_code_block(
            "range-replace",
            "src/main.rs\n15-10\n<<<<<<< RANGE\n=======\nContent\n>>>>>>> REPLACE",
            "```range-replace\nsrc/main.rs\n15-10\n<<<<<<< RANGE\n=======\nContent\n>>>>>>> REPLACE\n```",
        );
        let result = parser.parse_to_target_and_content(&raw_block, &[]);
        assert_eq!(result, Err(RangeReplaceParsingError::StartGreaterThanEnd));
    }

    // Validation tests
    #[test]
    fn test_validate_valid_edit_existing_file() {
        let parser = RangeReplaceBlockParser;
        let (labeled_file, _temp_dir_guard) =
            new_test_labeled_file_in_new_temp_dir("src/main.rs", "line1\nline2\nline3");
        let available_files = vec![labeled_file.clone()];

        let raw_block_content = format!("{}\n1-2\n<<<<<<< RANGE\n=======\nnew content\n>>>>>>> REPLACE", labeled_file.path.display());
        let raw_block_full_text = format!("```range-replace\n{}\n```", raw_block_content);
        let raw_block =
            new_raw_code_block("range-replace", &raw_block_content, &raw_block_full_text);

        let result = parser.validate_raw_block(&raw_block, &available_files);
        assert!(result.is_ok(), "Validation failed: {:?}", result.err());
    }

    #[test]
    fn test_validate_new_file_path_does_not_exist() {
        let parser = RangeReplaceBlockParser;
        let temp_dir = tempfile::tempdir().unwrap();
        let new_file_path = temp_dir.path().join("new_module.rs"); // Does not exist yet

        let raw_block_content = format!("{}\n0-0\n<<<<<<< RANGE\n=======\ncontent\n>>>>>>> REPLACE", new_file_path.display());
        let raw_block_full_text = format!("```range-replace\n{}\n```", raw_block_content);
        let raw_block =
            new_raw_code_block("range-replace", &raw_block_content, &raw_block_full_text);

        let result = parser.validate_raw_block(&raw_block, &[]);
        assert!(
            result.is_ok(),
            "Validation failed for new file: {:?}",
            result.err()
        );
    }

    #[test]
    fn test_validate_error_edit_file_not_exist() {
        let parser = RangeReplaceBlockParser;
        let non_existent_path = PathBuf::from("non_existent.rs");
        let raw_block = new_raw_code_block(
            "range-replace",
            "non_existent.rs\n1-2\n<<<<<<< RANGE\n=======\ncontent\n>>>>>>> REPLACE",
            "```range-replace\nnon_existent.rs\n1-2\n<<<<<<< RANGE\n=======\ncontent\n>>>>>>> REPLACE\n```",
        );
        let result = parser.validate_raw_block(&raw_block, &[]);
        assert!(result.is_err());
        match result
            .err()
            .unwrap()
            .downcast_ref::<RangeReplaceParsingError>()
        {
            Some(RangeReplaceParsingError::FileDoesNotExist(p)) => {
                let expected_abs_path = env::current_dir().unwrap().join(non_existent_path);
                assert_eq!(
                    p,
                    &expected_abs_path
                        .canonicalize()
                        .unwrap_or(expected_abs_path)
                );
            }
            _ => panic!("Incorrect error type"),
        }
    }

    #[test]
    fn test_validate_error_new_file_already_exists() {
        let parser = RangeReplaceBlockParser;
        let (labeled_file, _temp_dir_guard) =
            new_test_labeled_file_in_new_temp_dir("src/clashing_new.rs", "existing content");
        let available_files = vec![labeled_file.clone()];

        let raw_block_content = format!("{}\n0-0\n<<<<<<< RANGE\n=======\nnew content\n>>>>>>> REPLACE", labeled_file.path.display());
        let raw_block_full_text = format!("```range-replace\n{}\n```", raw_block_content);
        let raw_block =
            new_raw_code_block("range-replace", &raw_block_content, &raw_block_full_text);

        let result = parser.validate_raw_block(&raw_block, &available_files);
        assert!(result.is_err());
        match result
            .err()
            .unwrap()
            .downcast_ref::<RangeReplaceParsingError>()
        {
            Some(RangeReplaceParsingError::FileAlreadyExistsForNew(p)) => {
                assert_eq!(p, &labeled_file.path)
            }
            _ => panic!("Incorrect error type"),
        }
    }

    #[test]
    fn test_identify_malformed_missing_keyword() {
        let parser = RangeReplaceBlockParser;
        let raw_block = new_raw_code_block(
            "",
            "src/main.rs\n1-1\n<<<<<<< RANGE\n=======\ncontent\n>>>>>>> REPLACE",
            "```\nsrc/main.rs\n1-1\n<<<<<<< RANGE\n=======\ncontent\n>>>>>>> REPLACE\n```",
        );
        let malformed_info = parser.identify_malformed(&raw_block, &[]);
        assert!(malformed_info.is_some());
        let info = malformed_info.unwrap();
        assert!(info
            .fix_suggestion
            .contains("missing or misspelled keyword"));
    }

    #[test]
    fn test_identify_malformed_correct_keyword_bad_structure() {
        let parser = RangeReplaceBlockParser;
        let raw_block = new_raw_code_block(
            "range-replace",
            "src/main.rs\n<<<<<<< RANGE\n=======\ncontent\n>>>>>>> REPLACE",
            "```range-replace\nsrc/main.rs\n<<<<<<< RANGE\n=======\ncontent\n>>>>>>> REPLACE\n```",
        );
        let malformed_info = parser.identify_malformed(&raw_block, &[]);
        assert!(malformed_info.is_some());
        let info = malformed_info.unwrap();
        assert_eq!(
            info.fix_suggestion,
            RangeReplaceParsingError::InvalidLinesFormat("<<<<<<< RANGE".to_string()).to_string()
        );
    }
}
