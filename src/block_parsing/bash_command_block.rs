use crate::block_parsing::traits::{MalformedBlockInfo, ParsableBlock};
use crate::block_parsing::utils::RawCodeBlock;
use crate::files::file_handler::LabeledFile; // Required by ParsableBlock trait
use std::error::Error;
use std::fmt;

#[derive(Debug, <PERSON>lone, PartialEq, Eq)]
pub enum BashCommandParsingError {
    DisallowedCommand(String), // Stores the first word of the disallowed command segment
    EmptyCommand,              // The entire block content is empty
    InvalidPipeStructure,      // e.g., "cmd | | cmd" or leading/trailing pipes
}

impl fmt::Display for BashCommandParsingError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            BashCommandParsingError::DisallowedCommand(cmd) => write!(f, "Disallowed command: '{}'. In a piped command, each part must be one of 'grep', 'find', 'head', 'ls', 'tail', or 'xargs'.", cmd),
            BashCommandParsingError::EmptyCommand => write!(f, "Bash command block cannot be empty."),
            BashCommandParsingError::InvalidPipeStructure => write!(f, "Invalid pipe structure in bash command. Ensure segments are not empty and there are no leading/trailing pipes."),
        }
    }
}

impl Error for BashCommandParsingError {}

pub struct BashCommandBlockParser;

impl BashCommandBlockParser {
    // Helper function to split command string by pipes, respecting quotes.
    fn split_command_respecting_quotes(&self, command: &str) -> Vec<String> {
        let mut segments = Vec::new();
        if command.is_empty() {
            return segments;
        }

        let mut current_segment_start_index = 0;
        let mut in_single_quote = false;
        let mut in_double_quote = false;
        let mut last_char_was_escape = false; // To handle escaped quotes, though simple for now

        for (i, char_code) in command.char_indices() {
            match char_code {
                '\\' if !last_char_was_escape => {
                    last_char_was_escape = true;
                    continue;
                }
                '\'' if !in_double_quote && !last_char_was_escape => {
                    in_single_quote = !in_single_quote;
                }
                '"' if !in_single_quote && !last_char_was_escape => {
                    in_double_quote = !in_double_quote;
                }
                '|' if !in_single_quote && !in_double_quote && !last_char_was_escape => {
                    segments.push(command[current_segment_start_index..i].to_string());
                    current_segment_start_index = i + 1;
                }
                _ => {}
            }
            last_char_was_escape = false;
        }
        segments.push(command[current_segment_start_index..].to_string());
        segments
    }

    pub fn parse_to_string(
        &self,
        raw_block: &RawCodeBlock,
    ) -> Result<String, BashCommandParsingError> {
        let command_str = raw_block.content_after_keyword.trim();
        if command_str.is_empty() {
            return Err(BashCommandParsingError::EmptyCommand);
        }
        // Further validation for allowed commands is done in `validate_raw_block`.
        Ok(command_str.to_string())
    }

    // Renamed from is_allowed_command and updated to handle pipes + return Result
    fn check_command_segments(&self, command_str: &str) -> Result<(), BashCommandParsingError> {
        let overall_trimmed_command = command_str.trim();
        if overall_trimmed_command.is_empty() {
            return Err(BashCommandParsingError::EmptyCommand);
        }

        // Use the new splitter that respects quotes
        let segments: Vec<String> = self.split_command_respecting_quotes(overall_trimmed_command);

        if segments.is_empty() && !overall_trimmed_command.is_empty() {
            // This case should ideally not be hit if split_command_respecting_quotes always returns at least one segment for non-empty input.
            // However, as a safeguard:
            return Err(BashCommandParsingError::InvalidPipeStructure); // Or EmptyCommand, depending on interpretation
        }
        if segments.is_empty() && overall_trimmed_command.is_empty() {
            // Should be caught by the first check
            return Err(BashCommandParsingError::EmptyCommand);
        }

        // Check for empty segments resulting from pipes, e.g., "cmd1 | | cmd2" or leading/trailing pipes.
        // An empty overall_trimmed_command that was just "   |   " would result in segments like ["   ", "   "]
        // which then become empty when trimmed.
        // A command like "cmd | " would result in ["cmd ", " "].
        if segments.len() > 1 && segments.iter().any(|s| s.trim().is_empty()) {
            return Err(BashCommandParsingError::InvalidPipeStructure);
        }
        // Handles cases like a single segment that is just "   " or "  |  " (if not split)
        if segments.len() == 1 && segments[0].trim().is_empty() {
            return Err(BashCommandParsingError::EmptyCommand); // Or InvalidPipeStructure if it was e.g. " | "
        }

        for segment_str_owned in segments {
            let trimmed_segment = segment_str_owned.trim();
            // This check is vital: if a segment between pipes is empty, it's an invalid structure.
            // e.g., "grep foo | | find ." or "grep foo | " or " | grep foo"
            // This is now partially handled by the check above for `segments.iter().any(|s| s.trim().is_empty())`
            // but we keep it here for individual segment validation.
            if trimmed_segment.is_empty() {
                return Err(BashCommandParsingError::InvalidPipeStructure);
            }

            let first_word = trimmed_segment.split_whitespace().next().unwrap_or("");
            // first_word cannot be empty if trimmed_segment is not empty.

            if !matches!(
                first_word,
                "grep" | "find" | "head" | "ls" | "tail" | "xargs"
            ) {
                return Err(BashCommandParsingError::DisallowedCommand(
                    first_word.to_string(),
                ));
            }
        }
        Ok(())
    }
}

impl ParsableBlock for BashCommandBlockParser {
    fn id(&self) -> String {
        "bash_command_block".to_string()
    }

    fn keyword(&self) -> &'static str {
        "bash"
    }

    fn block_format_description(&self) -> String {
        "A 'bash' block executes a shell command. Format: ```bash\n<command_here>\n```. Allowed base commands are 'grep', 'find', 'head', 'ls', 'tail', 'xargs'. These can be piped (e.g., find ... | grep ...), but each command in the pipe must be one of the allowed base commands.".to_string()
    }

    fn validate_raw_block(
        &self,
        raw_block: &RawCodeBlock,
        _available_files: &[LabeledFile],
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        let command_str = raw_block.content_after_keyword.trim();
        self.check_command_segments(command_str)
            .map_err(|e| Box::new(e) as Box<dyn Error + Send + Sync>)
    }

    fn identify_malformed(
        &self,
        raw_block: &RawCodeBlock,
        _available_files: &[LabeledFile],
    ) -> Option<MalformedBlockInfo> {
        let content = raw_block.content_after_keyword.trim();
        let parser_id = self.id();

        if raw_block.keyword != self.keyword()
            && (raw_block.keyword.is_empty() || raw_block.keyword.to_lowercase().starts_with("ba"))
        {
            // Keyword is missing or misspelled, but content might look like a command
            if !content.is_empty()
                && (content.starts_with("grep")
                    || content.starts_with("find")
                    || content.starts_with("head")
                    || content.starts_with("ls")
                    || content.starts_with("tail")
                    || content.starts_with("xargs"))
            {
                return Some(MalformedBlockInfo {
                    raw_text: raw_block.full_block_text.clone(),
                    fix_suggestion: format!("This block appears to be a bash command but has a missing or misspelled keyword (expected 'bash'). Found '{}'.", raw_block.keyword),
                    parser_id,
                });
            }
        } else if raw_block.keyword == self.keyword() {
            // Keyword is correct, but validation failed (e.g., disallowed command or empty)
            match self.check_command_segments(content) {
                Ok(_) => {} // Should not happen if validate_raw_block failed, but handle defensively
                Err(BashCommandParsingError::EmptyCommand) => {
                    return Some(MalformedBlockInfo {
                        raw_text: raw_block.full_block_text.clone(),
                        fix_suggestion: "The 'bash' block is empty. Please provide a command."
                            .to_string(),
                        parser_id,
                    });
                }
                Err(BashCommandParsingError::DisallowedCommand(cmd)) => {
                    return Some(MalformedBlockInfo {
                        raw_text: raw_block.full_block_text.clone(),
                        fix_suggestion: format!("The command '{}' is not allowed. In a piped command, each part must be one of 'grep', 'find', 'head', 'ls', 'tail', or 'xargs'.", cmd),
                        parser_id,
                    });
                }
                Err(BashCommandParsingError::InvalidPipeStructure) => {
                    return Some(MalformedBlockInfo {
                        raw_text: raw_block.full_block_text.clone(),
                        fix_suggestion: "The bash command has an invalid pipe structure (e.g., empty segment like 'cmd | | cmd', or a leading/trailing pipe). Please ensure pipes connect valid commands.".to_string(),
                        parser_id,
                    });
                }
            }
        }
        None
    }

    fn construct_fixer_prompt_segment(
        &self,
        malformed_blocks: &[MalformedBlockInfo],
        _available_files: &[LabeledFile],
    ) -> Option<String> {
        if malformed_blocks.is_empty() {
            return None;
        }
        let mut prompt_segment = String::new();
        prompt_segment.push_str(
            "For 'bash' command blocks:
",
        );
        for (index, malformed) in malformed_blocks.iter().enumerate() {
            prompt_segment.push_str(&format!(
                "  Problematic bash block #{}:
",
                index + 1
            ));
            prompt_segment.push_str(
                "  ```text
",
            ); // Use text to avoid nested block interpretation
            prompt_segment.push_str(&malformed.raw_text.replace("```", "'''")); // Escape internal backticks
            prompt_segment.push_str(
                "
  ```
",
            );
            prompt_segment.push_str(&format!(
                "  Issue: {}

",
                malformed.fix_suggestion
            ));
        }
        prompt_segment.push_str(&format!("  Correct format: {}. Remember, only 'grep', 'find', 'head', 'ls', 'tail', and 'xargs' commands are allowed in each part of a pipe.
", self.block_format_description()));
        Some(prompt_segment)
    }

    fn example_prompt_text(&self) -> String {
        format!(
            "{}\nExamples:\n```bash\nfind . -name '*.rs' | grep -i 'example_function'\n```\n```bash\nls -la src\n```\n```bash\nhead -n 10 file.txt | grep 'error'\n```\n```bash\ntail -n 10 file.txt\n```\n```bash\nfind . -type f -print0 | xargs -0 grep 'pattern'\n```",
            self.block_format_description()
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::files::file_handler::LabeledFile; // Required for tests using validate_raw_block

    // Helper to create a RawCodeBlock for tests
    fn new_raw_code_block(
        keyword: &str,
        content_after_keyword: &str,
        full_block_text: &str,
    ) -> RawCodeBlock {
        RawCodeBlock {
            keyword: keyword.to_string(),
            content_after_keyword: content_after_keyword.to_string(),
            full_block_text: full_block_text.to_string(),
        }
    }

    #[test]
    fn test_parse_to_string_valid_command() {
        let parser = BashCommandBlockParser;
        let raw_block = new_raw_code_block(
            "bash",
            "grep -r 'todo' .",
            "```bash
grep -r 'todo' .
```",
        );
        let result = parser.parse_to_string(&raw_block);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "grep -r 'todo' .");
    }

    #[test]
    fn test_parse_to_string_empty_command() {
        let parser = BashCommandBlockParser;
        let raw_block = new_raw_code_block(
            "bash",
            "   ",
            "```bash
   
```",
        );
        let result = parser.parse_to_string(&raw_block);
        assert_eq!(result, Err(BashCommandParsingError::EmptyCommand));
    }

    #[test]
    fn test_validate_raw_block_allowed_commands() {
        let parser = BashCommandBlockParser;
        let files: Vec<LabeledFile> = vec![];
        let commands = vec![
            "grep foo",
            "find . -type f",
            "head -n 5 file.txt",
            "ls -la",
            "tail -n 5 file.txt",
            "xargs echo",
            "find . -name '*.txt' | grep 'pattern'",
            "ls -la | head -n 5",
            "grep 'test' file.txt | tail -n 1",
            "find . -print0 | xargs -0 head",
            "find . -print0 | xargs -0 tail -n 2",
        ];
        for cmd_str in commands {
            let raw_block =
                new_raw_code_block("bash", cmd_str, &format!("```bash\n{}\n```", cmd_str));
            let result = parser.validate_raw_block(&raw_block, &files);
            assert!(
                result.is_ok(),
                "Validation failed for command '{}': {:?}",
                cmd_str,
                result.err()
            );
        }
    }

    #[test]
    fn test_validate_raw_block_disallowed_command_single() {
        let parser = BashCommandBlockParser;
        let files: Vec<LabeledFile> = vec![];
        let raw_block = new_raw_code_block("bash", "rm -rf /", "```bash\nrm -rf /\n```");
        let result = parser.validate_raw_block(&raw_block, &files);
        assert!(result.is_err());
        match result
            .err()
            .unwrap()
            .downcast_ref::<BashCommandParsingError>()
        {
            Some(BashCommandParsingError::DisallowedCommand(cmd)) => assert_eq!(cmd, "rm"),
            other_err => panic!("Incorrect error type or value: {:?}", other_err),
        }
    }

    #[test]
    fn test_validate_raw_block_disallowed_command_in_pipe() {
        let parser = BashCommandBlockParser;
        let files: Vec<LabeledFile> = vec![];
        let raw_block = new_raw_code_block(
            "bash",
            "find . | rm -rf / | grep foo",
            "```bash\nfind . | rm -rf / | grep foo\n```",
        );
        let result = parser.validate_raw_block(&raw_block, &files);
        assert!(result.is_err());
        match result
            .err()
            .unwrap()
            .downcast_ref::<BashCommandParsingError>()
        {
            Some(BashCommandParsingError::DisallowedCommand(cmd)) => assert_eq!(cmd, "rm"),
            other_err => panic!("Incorrect error type or value: {:?}", other_err),
        }
    }

    #[test]
    fn test_validate_raw_block_invalid_pipe_structure() {
        let parser = BashCommandBlockParser;
        let files: Vec<LabeledFile> = vec![];
        let invalid_pipes = vec![
            "grep foo | | find .", // Double pipe
            "grep foo | ",         // Trailing pipe
            " | grep foo",         // Leading pipe
            "|",                   // Just a pipe
            "   |   ",             // Just a pipe with spaces
        ];
        for pipe_cmd in invalid_pipes {
            let raw_block =
                new_raw_code_block("bash", pipe_cmd, &format!("```bash\n{}\n```", pipe_cmd));
            let result = parser.validate_raw_block(&raw_block, &files);
            assert!(result.is_err(), "Expected error for: {}", pipe_cmd);
            match result
                .err()
                .unwrap()
                .downcast_ref::<BashCommandParsingError>()
            {
                Some(BashCommandParsingError::InvalidPipeStructure) => {} // Expected
                other_err => panic!("Incorrect error type for '{}': {:?}", pipe_cmd, other_err),
            }
        }
    }

    #[test]
    fn test_validate_raw_block_empty_command_content() {
        let parser = BashCommandBlockParser;
        let files: Vec<LabeledFile> = vec![];
        let raw_block = new_raw_code_block(
            "bash",
            "",
            "```bash
```",
        );
        let result = parser.validate_raw_block(&raw_block, &files);
        assert_eq!(
            result
                .err()
                .unwrap()
                .downcast_ref::<BashCommandParsingError>(),
            Some(&BashCommandParsingError::EmptyCommand)
        );
    }

    #[test]
    fn test_validate_raw_block_pipe_inside_quotes() {
        let parser = BashCommandBlockParser;
        let files: Vec<LabeledFile> = vec![];

        // Test case from user: pipe inside single quotes for grep pattern
        let cmd_str_user = "grep -n 'struct\\|enum' src/prompt_builder.rs";
        let raw_block_user = new_raw_code_block(
            "bash",
            cmd_str_user,
            &format!("```bash\n{}\n```", cmd_str_user),
        );
        let result_user = parser.validate_raw_block(&raw_block_user, &files);
        assert!(
            result_user.is_ok(),
            "Validation failed for command with escaped pipe in quotes: '{}': {:?}",
            cmd_str_user,
            result_user.err()
        );

        // This command should be treated as a single segment because '|' is inside quotes.
        let cmd_str = "grep -E 'foo|bar' file.txt";
        let raw_block = new_raw_code_block("bash", cmd_str, &format!("```bash\n{}\n```", cmd_str));
        let result = parser.validate_raw_block(&raw_block, &files);
        assert!(
            result.is_ok(),
            "Validation failed for command with pipe in quotes: '{}': {:?}",
            cmd_str,
            result.err()
        );

        // This command has a pipe outside quotes, and the second command is disallowed.
        let cmd_str_disallowed_pipe = "grep -E 'foo|bar' file.txt | sort";
        let raw_block_disallowed = new_raw_code_block(
            "bash",
            cmd_str_disallowed_pipe,
            &format!("```bash\n{}\n```", cmd_str_disallowed_pipe),
        );
        let result_disallowed = parser.validate_raw_block(&raw_block_disallowed, &files);
        assert!(
            result_disallowed.is_err(),
            "Validation should have failed for disallowed command in pipe: '{}'",
            cmd_str_disallowed_pipe
        );
        match result_disallowed
            .err()
            .unwrap()
            .downcast_ref::<BashCommandParsingError>()
        {
            Some(BashCommandParsingError::DisallowedCommand(cmd)) => assert_eq!(cmd, "sort"),
            other_err => panic!(
                "Incorrect error type or value for disallowed pipe: {:?}",
                other_err
            ),
        }

        // This command has a pipe outside quotes, and the second command is allowed.
        let cmd_str_allowed_pipe = "grep -E 'foo|bar' file.txt | head -n 1";
        let raw_block_allowed_pipe = new_raw_code_block(
            "bash",
            cmd_str_allowed_pipe,
            &format!("```bash\n{}\n```", cmd_str_allowed_pipe),
        );
        let result_allowed_pipe = parser.validate_raw_block(&raw_block_allowed_pipe, &files);
        assert!(
            result_allowed_pipe.is_ok(),
            "Validation failed for allowed command in pipe: '{}': {:?}",
            cmd_str_allowed_pipe,
            result_allowed_pipe.err()
        );

        // Test with double quotes
        let cmd_str_double_quotes = "grep -E \"foo|bar\" file.txt | head -n 1";
        let raw_block_double_quotes = new_raw_code_block(
            "bash",
            cmd_str_double_quotes,
            &format!("```bash\n{}\n```", cmd_str_double_quotes),
        );
        let result_double_quotes = parser.validate_raw_block(&raw_block_double_quotes, &files);
        assert!(
            result_double_quotes.is_ok(),
            "Validation failed for command with pipe in double quotes and valid pipe: '{}': {:?}",
            cmd_str_double_quotes,
            result_double_quotes.err()
        );

        // Test with mixed quotes (though less common for this specific problem)
        // This is more about the robustness of the quote handling in general.
        // Example: find . -name "*.txt" -exec grep -H "pattern with 'single|pipe'" {} \; | xargs head
        // For simplicity, let's test a direct grep with mixed internal quoting if the shell would even allow it directly.
        // A more realistic complex command:
        let cmd_str_complex_quotes =
            "find . -name '*.txt' | xargs grep -E 'complex-\"pattern\"|another'";
        let raw_block_complex_quotes = new_raw_code_block(
            "bash",
            cmd_str_complex_quotes,
            &format!("```bash\n{}\n```", cmd_str_complex_quotes),
        );
        let result_complex_quotes = parser.validate_raw_block(&raw_block_complex_quotes, &files);
        assert!(
            result_complex_quotes.is_ok(),
            "Validation failed for complex command: '{}': {:?}",
            cmd_str_complex_quotes,
            result_complex_quotes.err()
        );
    }

    #[test]
    fn test_validate_user_reported_command_grep_pipe_grep_with_internal_escaped_pipes() {
        let parser = BashCommandBlockParser;
        let files: Vec<LabeledFile> = vec![];
        let cmd_str = "grep -r 'decision\\|auto-researching\\|TUI' ./src | grep -v 'interactive/ui.rs\\|editor/cycle.rs\\|research/research_cycle.rs\\|notifications.rs\\|config/file_config.rs\\|llm/client.rs\\|research/auto_research.rs\\|logger.rs\\|interactive/runner.rs'";
        let raw_block = new_raw_code_block("bash", cmd_str, &format!("```bash\n{}\n```", cmd_str));
        let result = parser.validate_raw_block(&raw_block, &files);
        assert!(
            result.is_ok(),
            "Validation failed for user command with escaped pipes '{}': {:?}",
            cmd_str,
            result.err()
        );
    }

    #[test]
    fn test_validate_user_reported_command_find_xargs_grep() {
        let parser = BashCommandBlockParser;
        let files: Vec<LabeledFile> = vec![];
        let cmd_str = "find ./src -type f -name '*.rs' | xargs grep 'command'";
        let raw_block = new_raw_code_block("bash", cmd_str, &format!("```bash\n{}\n```", cmd_str));
        let result = parser.validate_raw_block(&raw_block, &files);
        assert!(
            result.is_ok(),
            "Validation failed for user command '{}': {:?}",
            cmd_str,
            result.err()
        );
    }

    #[test]
    fn test_split_command_respecting_quotes_edge_cases() {
        let parser = BashCommandBlockParser;
        assert_eq!(
            parser.split_command_respecting_quotes(""),
            Vec::<String>::new()
        );
        assert_eq!(parser.split_command_respecting_quotes("cmd1"), vec!["cmd1"]);
        assert_eq!(
            parser.split_command_respecting_quotes("cmd1 | cmd2"),
            vec!["cmd1 ", " cmd2"]
        );
        assert_eq!(
            parser.split_command_respecting_quotes("cmd1 'a|b' | cmd2 \"c|d\""),
            vec!["cmd1 'a|b' ", " cmd2 \"c|d\""]
        );
        assert_eq!(
            parser.split_command_respecting_quotes("cmd1 'a|b'"),
            vec!["cmd1 'a|b'"]
        );
        assert_eq!(
            parser.split_command_respecting_quotes("'a|b'"),
            vec!["'a|b'"]
        );
        assert_eq!(
            parser.split_command_respecting_quotes("grep -n 'struct\\|enum' src/f.rs"),
            vec!["grep -n 'struct\\|enum' src/f.rs"]
        );
        // Test unclosed quotes - current behavior is to treat them as literal until end or matching quote
        assert_eq!(
            parser.split_command_respecting_quotes("grep 'foo|bar | wc -l"),
            vec!["grep 'foo|bar | wc -l"]
        );
        assert_eq!(
            parser.split_command_respecting_quotes("grep \"foo|bar | wc -l"),
            vec!["grep \"foo|bar | wc -l"]
        );
    }

    #[test]
    fn test_identify_malformed_missing_keyword() {
        let parser = BashCommandBlockParser;
        let files: Vec<LabeledFile> = vec![];
        let raw_block = new_raw_code_block(
            "",
            "find . -name \"*.txt\"",
            "```\nfind . -name \"*.txt\"\n```",
        );
        let malformed_info = parser.identify_malformed(&raw_block, &files);
        assert!(malformed_info.is_some());
        let info = malformed_info.unwrap();
        assert_eq!(info.parser_id, "bash_command_block");
        assert!(info
            .fix_suggestion
            .contains("missing or misspelled keyword (expected 'bash')"));
    }

    #[test]
    fn test_identify_malformed_disallowed_command_correct_keyword() {
        let parser = BashCommandBlockParser;
        let files: Vec<LabeledFile> = vec![];
        let raw_block = new_raw_code_block("bash", "rm -rf /", "```bash\nrm -rf /\n```");
        let malformed_info = parser.identify_malformed(&raw_block, &files);
        assert!(malformed_info.is_some());
        let info = malformed_info.unwrap();
        assert_eq!(info.parser_id, "bash_command_block");
        assert!(info.fix_suggestion.contains("command 'rm' is not allowed"));
    }

    #[test]
    fn test_identify_malformed_invalid_pipe_structure_correct_keyword() {
        let parser = BashCommandBlockParser;
        let files: Vec<LabeledFile> = vec![];
        let raw_block = new_raw_code_block(
            "bash",
            "grep foo | | find .",
            "```bash\ngrep foo | | find .\n```",
        );
        let malformed_info = parser.identify_malformed(&raw_block, &files);
        assert!(malformed_info.is_some());
        let info = malformed_info.unwrap();
        assert_eq!(info.parser_id, "bash_command_block");
        assert!(info.fix_suggestion.contains("invalid pipe structure"));
    }
}
