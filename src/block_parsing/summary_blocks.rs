use crate::block_parsing::traits::{MalformedBlockInfo, ParsableBlock};
use crate::block_parsing::utils::RawCodeBlock;
use crate::files::file_handler::LabeledFile; // Required by ParsableBlock trait
use std::error::Error;
use std::fmt;

// SummaryParsingError is an empty enum as per plan, meaning parsing a `RawCodeBlock`
// identified as `summary` into its string content is considered infallible.
// Empty summaries are valid.
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum SummaryParsingError {} // Empty enum

impl fmt::Display for SummaryParsingError {
    fn fmt(&self, _f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match *self {}
    }
}

impl Error for SummaryParsingError {}

pub struct SummaryBlockParser;

impl SummaryBlockParser {
    /// Parses the content of a `RawCodeBlock` (assumed to be a summary block) into a string.
    /// This is infallible. Content is trimmed.
    pub fn parse_to_string(&self, raw_block: &RawCodeBlock) -> Result<String, SummaryParsingError> {
        Ok(raw_block.content_after_keyword.trim().to_string())
    }
}

impl ParsableBlock for SummaryBlockParser {
    fn id(&self) -> String {
        "summary_block".to_string()
    }

    fn keyword(&self) -> &'static str {
        "summary"
    }

    fn block_format_description(&self) -> String {
        "A 'summary' block provides a brief summary. Format: ```summary\\n<summary_text_here>\\n```"
            .to_string()
    }

    fn validate_raw_block(
        &self,
        raw_block: &RawCodeBlock,
        _available_files: &[LabeledFile], // Not used for summary block validation
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        match self.parse_to_string(raw_block) {
            Ok(_) => Ok(()),
            Err(e) => Err(Box::new(e)), // Should be unreachable
        }
    }

    fn identify_malformed(
        &self,
        raw_block: &RawCodeBlock,
        _available_files: &[LabeledFile], // Not used
    ) -> Option<MalformedBlockInfo> {
        // Simple check: if keyword is missing or a common misspelling.
        if raw_block.keyword != self.keyword()
            && (raw_block.keyword.is_empty()
                || raw_block.keyword.starts_with("sumar")
                || raw_block.keyword.starts_with("summry"))
            && !raw_block.content_after_keyword.is_empty()
        // Avoid flagging empty ``` ``` as malformed summary
        {
            // Heuristic: if it has content and a keyword that's close or empty.
            if raw_block.keyword.is_empty() {
                return Some(MalformedBlockInfo {
                    raw_text: raw_block.full_block_text.clone(),
                    fix_suggestion: "This block appears to contain summary text but is missing the 'summary' keyword. Please ensure it starts with '```summary'.".to_string(),
                    parser_id: self.id(),
                });
            } else {
                // Misspelled keyword
                return Some(MalformedBlockInfo {
                    raw_text: raw_block.full_block_text.clone(),
                    fix_suggestion: format!("This block has a keyword '{}' which might be a misspelling of 'summary'. Please ensure it starts with '```summary'.", raw_block.keyword),
                    parser_id: self.id(),
                });
            }
        }
        None
    }

    fn construct_fixer_prompt_segment(
        &self,
        malformed_blocks: &[MalformedBlockInfo],
        _available_files: &[LabeledFile], // Not used
    ) -> Option<String> {
        if malformed_blocks.is_empty() {
            return None;
        }
        let mut prompt_segment = String::new();
        prompt_segment.push_str("For 'summary' blocks (providing a brief summary of the plan):\n");
        for (index, malformed) in malformed_blocks.iter().enumerate() {
            prompt_segment.push_str(&format!("  Problematic summary block #{}:\n", index + 1));
            prompt_segment.push_str("  ```text\n");
            prompt_segment.push_str(&malformed.raw_text.replace("```", "'''"));
            prompt_segment.push_str("\n  ```\n");
            prompt_segment.push_str(&format!("  Issue: {}\n\n", malformed.fix_suggestion));
        }
        prompt_segment.push_str(&format!("  Correct format: {}. IMPORTANT: Your corrected response should contain *only* the 'summary' block and no other blocks or explanatory text.\n", self.block_format_description()));
        Some(prompt_segment)
    }

    fn example_prompt_text(&self) -> String {
        format!(
            "{}\nExample:\n```{}\nThis is a brief summary of the plan and the changes to be made.\n```",
            self.block_format_description(),
            self.keyword()
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::block_parsing::utils::RawCodeBlock;

    fn new_raw_code_block(
        keyword: &str,
        content_after_keyword: &str,
        full_block_text: &str,
    ) -> RawCodeBlock {
        RawCodeBlock {
            keyword: keyword.to_string(),
            content_after_keyword: content_after_keyword.to_string(),
            full_block_text: full_block_text.to_string(),
        }
    }

    #[test]
    fn test_parse_to_string_present() {
        let parser = SummaryBlockParser;
        let raw_block = new_raw_code_block(
            "summary",
            "This is the plan summary.\n",
            "```summary\nThis is the plan summary.\n```",
        );
        let summary = parser.parse_to_string(&raw_block).unwrap();
        assert_eq!(summary, "This is the plan summary.");
    }

    #[test]
    fn test_parse_to_string_empty_summary_block() {
        let parser = SummaryBlockParser;
        let raw_block = new_raw_code_block("summary", "\n\n", "```summary\n\n```");
        let summary = parser.parse_to_string(&raw_block).unwrap();
        assert_eq!(summary, "");
    }

    #[test]
    fn test_parse_to_string_empty_summary_block_no_newline() {
        let parser = SummaryBlockParser;
        let raw_block = new_raw_code_block("summary", "", "```summary```");
        let summary = parser.parse_to_string(&raw_block).unwrap();
        assert_eq!(summary, "");
    }

    #[test]
    fn test_parse_to_string_trimming() {
        let parser = SummaryBlockParser;
        let raw_block = new_raw_code_block(
            "summary",
            "  Spaced summary.  \n",
            "```summary\n  Spaced summary.  \n```",
        );
        let summary = parser.parse_to_string(&raw_block).unwrap();
        assert_eq!(summary, "Spaced summary.");
    }

    #[test]
    fn test_validate_raw_block_always_ok() {
        let parser = SummaryBlockParser;
        let raw_block =
            new_raw_code_block("summary", "some content", "```summary\nsome content\n```");
        let available_files: Vec<LabeledFile> = vec![];
        let result = parser.validate_raw_block(&raw_block, &available_files);
        assert!(result.is_ok());

        let raw_block_empty = new_raw_code_block("summary", "", "```summary```");
        let result_empty = parser.validate_raw_block(&raw_block_empty, &available_files);
        assert!(result_empty.is_ok());
    }

    #[test]
    fn test_identify_malformed_missing_keyword() {
        let parser = SummaryBlockParser;
        let raw_block =
            new_raw_code_block("", "This is a summary.\n", "```\nThis is a summary.\n```");
        let available_files: Vec<LabeledFile> = vec![];
        let malformed_info = parser.identify_malformed(&raw_block, &available_files);
        assert!(malformed_info.is_some());
        assert!(malformed_info
            .unwrap()
            .fix_suggestion
            .contains("missing the 'summary' keyword"));
    }

    #[test]
    fn test_identify_malformed_misspelled_keyword() {
        let parser = SummaryBlockParser;
        let raw_block = new_raw_code_block("sumary", "content", "```sumary\ncontent\n```"); // Misspelled
        let available_files: Vec<LabeledFile> = vec![];
        let malformed_info = parser.identify_malformed(&raw_block, &available_files);
        assert!(malformed_info.is_some());
        assert!(malformed_info
            .unwrap()
            .fix_suggestion
            .contains("misspelling of 'summary'"));
    }

    #[test]
    fn test_identify_malformed_correct_keyword_returns_none() {
        let parser = SummaryBlockParser;
        let raw_block = new_raw_code_block("summary", "content", "```summary\ncontent\n```");
        let available_files: Vec<LabeledFile> = vec![];
        let malformed_info = parser.identify_malformed(&raw_block, &available_files);
        assert!(malformed_info.is_none());
    }

    #[test]
    fn test_construct_fixer_prompt_segment() {
        let parser = SummaryBlockParser;
        let malformed_blocks = vec![MalformedBlockInfo {
            raw_text: "```\nThis is a summary.\n```".to_string(),
            fix_suggestion:
                "This block appears to contain summary text but is missing the 'summary' keyword."
                    .to_string(),
            parser_id: "summary_block".to_string(),
        }];
        let available_files: Vec<LabeledFile> = vec![];
        let prompt_segment = parser
            .construct_fixer_prompt_segment(&malformed_blocks, &available_files)
            .unwrap();

        assert!(prompt_segment.contains("For 'summary' blocks"));
        assert!(prompt_segment.contains("Problematic summary block #1"));
        assert!(prompt_segment.contains("'''\nThis is a summary.\n'''"));
        assert!(prompt_segment.contains("Issue: This block appears to contain summary text but is missing the 'summary' keyword."));
        assert!(
            prompt_segment.contains("Correct format: A 'summary' block provides a brief summary.")
        );
    }
}
