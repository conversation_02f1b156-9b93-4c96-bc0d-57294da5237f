use crate::block_parsing::traits::{MalformedBlockInfo, ParsableBlock};
use crate::block_parsing::utils::RawCodeBlock;
use crate::editor::types::EditTarget;
use crate::files::file_handler::LabeledFile; // Corrected path
use regex::Regex;
use std::error::Error;
use std::fmt;
use std::path::PathBuf;

#[derive(Debug, Clone, PartialEq, Eq)]
pub enum RangeParsingError {
    PathMissing,
    InvalidLinesFormat(String), // Stores the problematic line string
    StartLineZero,
    EndLineZero,
    StartGreaterThanEnd,
    FileDoesNotExist(PathBuf),
    FileAlreadyExistsForNew(PathBuf), // For 0-0 range on existing file
    BlockEmpty,
    MissingLineNumbers,
    UnrecognizedFormat,
    // IncompleteRangeSingleNumber, // No longer an error, handled by parsing logic
    MultiRangeContainsInvalid(String), // The problematic multi-range string
}

impl fmt::Display for RangeParsingError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            RangeParsingError::PathMissing => write!(f, "File path is missing."),
            RangeParsingError::InvalidLinesFormat(lines) => write!(f, "Invalid line number format: '{}'. Expected START-END.", lines),
            RangeParsingError::StartLineZero => write!(f, "Start line cannot be 0 unless creating a new file (0-0)."),
            RangeParsingError::EndLineZero => write!(f, "End line cannot be 0 unless creating a new file (0-0)."),
            RangeParsingError::StartGreaterThanEnd => write!(f, "Start line cannot be greater than end line."),
            RangeParsingError::FileDoesNotExist(path) => write!(f, "File '{}' does not exist.", path.display()),
            RangeParsingError::FileAlreadyExistsForNew(path) => write!(f, "File '{}' already exists. Range 0-0 is for new files.", path.display()),
            RangeParsingError::BlockEmpty => write!(f, "The 'range' block is empty after the 'range' keyword."),
            RangeParsingError::MissingLineNumbers => write!(f, "The 'range' block is missing line numbers after the file path."),
            RangeParsingError::UnrecognizedFormat => write!(f, "The 'range' block content has an unrecognized format after the 'range' keyword."),
            // RangeParsingError::IncompleteRangeSingleNumber => write!(f, "Range format is incomplete. Expected 'START-END' after the file path, but only one number was found."),
            RangeParsingError::MultiRangeContainsInvalid(original_spec) => write!(f, "The multi-range specification '{}' contains an invalid range or format. Each part of a comma-separated multi-range must be a valid 'START-END' range (e.g., '1-10' or '0-0').", original_spec),
        }
    }
}

impl Error for RangeParsingError {}

pub struct RangeBlockParser;

impl RangeBlockParser {
    // Regex for "path START-END" format.
    fn path_start_end_re() -> Regex {
        Regex::new(r"^\s*([\s\S]*?)\s*([^- \t\r\n]+)-([^\s\t\r\n]+)\s*$")
            .expect("Failed to compile path-start-end regex for RangeBlockParser")
    }

    // Regex for "path NUMBER" format (missing range end).
    // Also used for "NUMBER" directly when parsing range_spec_line_str
    fn single_number_re() -> Regex {
        Regex::new(r"^\s*(\d+)\s*$")
            .expect("Failed to compile single-number regex for RangeBlockParser")
    }

    pub fn parse_to_edit_target(
        &self,
        raw_block: &RawCodeBlock,
        _available_files: &[LabeledFile], // Used for validation, not direct parsing here
    ) -> Result<Vec<EditTarget>, RangeParsingError> {
        let content = &raw_block.content_after_keyword; // Use reference, do not trim here
        if content.trim().is_empty() {
            // Trim only for the empty check
            return Err(RangeParsingError::BlockEmpty);
        }

        // Try to parse as "path\nranges_spec" first
        let mut lines_iter = content.lines();
        let file_path_str = lines_iter.next().map_or("", |s| s.trim());
        let range_spec_line_str = lines_iter.next().map_or("", |s| s.trim());

        if file_path_str.is_empty() {
            return Err(RangeParsingError::PathMissing);
        }
        if range_spec_line_str.is_empty() {
            // This could happen if content is "path\n" or "path"
            // If it was just "path", path_start_end_re would not match.
            // If it was "path\n", range_spec_line_str would be empty.
            return Err(RangeParsingError::MissingLineNumbers);
        }

        let file_path = PathBuf::from(file_path_str);
        let mut edit_targets = Vec::new();

        // Check for multi-range (comma-separated) on the range_spec_line_str
        if range_spec_line_str.contains(',') {
            let range_parts: Vec<&str> = range_spec_line_str.split(',').map(|s| s.trim()).collect();
            if range_parts.iter().any(|p| p.is_empty()) {
                // e.g. "1-2,,3-4" or "1-2,"
                return Err(RangeParsingError::MultiRangeContainsInvalid(
                    range_spec_line_str.to_string(),
                ));
            }

            for part_str in range_parts {
                if let Some(caps) = Self::path_start_end_re().captures(part_str) {
                    // Re-use regex on part
                    // For multi-range, path_start_end_re is used to parse "START-END" directly.
                    // Group 1 would be empty, Group 2 start, Group 3 end.
                    let start_line_str = caps.get(2).map_or("", |m| m.as_str());
                    let end_line_str = caps.get(3).map_or("", |m| m.as_str());

                    match (
                        start_line_str.parse::<usize>(),
                        end_line_str.parse::<usize>(),
                    ) {
                        (Ok(mut parsed_start_line), Ok(parsed_end_line)) => {
                            if parsed_start_line == 0 && parsed_end_line != 0 {
                                parsed_start_line = 1;
                            }
                            if !((parsed_start_line == 0 && parsed_end_line == 0) || // 0-0
                                 (parsed_start_line != 0 && parsed_end_line != 0 && parsed_start_line <= parsed_end_line))
                            {
                                // N-M, N>0, M>0, N<=M
                                return Err(RangeParsingError::MultiRangeContainsInvalid(
                                    range_spec_line_str.to_string(),
                                ));
                            }
                            edit_targets.push(EditTarget {
                                file_path: file_path.clone(),
                                start_line: parsed_start_line,
                                end_line: parsed_end_line,
                            });
                        }
                        _ => {
                            return Err(RangeParsingError::MultiRangeContainsInvalid(
                                range_spec_line_str.to_string(),
                            ))
                        }
                    }
                } else {
                    // Part does not match START-END
                    return Err(RangeParsingError::MultiRangeContainsInvalid(
                        range_spec_line_str.to_string(),
                    ));
                }
            }
        } else {
            // Single range on range_spec_line_str
            if let Some(caps) = Self::path_start_end_re().captures(range_spec_line_str) {
                let start_line_str = caps.get(2).map_or("", |m| m.as_str());
                let end_line_str = caps.get(3).map_or("", |m| m.as_str());
                match (
                    start_line_str.parse::<usize>(),
                    end_line_str.parse::<usize>(),
                ) {
                    (Ok(mut parsed_start_line), Ok(parsed_end_line)) => {
                        if parsed_start_line == 0 && parsed_end_line != 0 {
                            parsed_start_line = 1;
                        }
                        if parsed_start_line == 0 && parsed_end_line == 0 {
                            edit_targets.push(EditTarget {
                                file_path,
                                start_line: 0,
                                end_line: 0,
                            });
                        } else if parsed_start_line == 0 {
                            return Err(RangeParsingError::StartLineZero);
                        } else if parsed_end_line == 0 {
                            return Err(RangeParsingError::EndLineZero);
                        } else if parsed_start_line > parsed_end_line {
                            return Err(RangeParsingError::StartGreaterThanEnd);
                        } else {
                            edit_targets.push(EditTarget {
                                file_path,
                                start_line: parsed_start_line,
                                end_line: parsed_end_line,
                            });
                        }
                    }
                    _ => {
                        return Err(RangeParsingError::InvalidLinesFormat(
                            range_spec_line_str.to_string(),
                        ))
                    }
                }
            } else if let Some(caps) = Self::single_number_re().captures(range_spec_line_str) {
                // Handle single number as N-N
                let line_num_str = caps.get(1).map_or("", |m| m.as_str());
                match line_num_str.parse::<usize>() {
                    Ok(line_num) => {
                        if line_num == 0 {
                            // 0 means 0-0 for new file
                            edit_targets.push(EditTarget {
                                file_path,
                                start_line: 0,
                                end_line: 0,
                            });
                        } else {
                            // N means N-N
                            edit_targets.push(EditTarget {
                                file_path,
                                start_line: line_num,
                                end_line: line_num,
                            });
                        }
                    }
                    _ => {
                        return Err(RangeParsingError::InvalidLinesFormat(
                            range_spec_line_str.to_string(),
                        ))
                    } // Should not happen if regex matches \d+
                }
            } else {
                return Err(RangeParsingError::InvalidLinesFormat(
                    range_spec_line_str.to_string(),
                ));
            }
        }

        if edit_targets.is_empty() && !content.is_empty() {
            // This case implies the overall format was not path\nrange(s)
            // but also didn't hit specific errors like BlockEmpty or PathMissing.
            // It might be a single line that doesn't match path_start_end_re,
            // or multiple lines not fitting the expected structure.
            if !content.contains('\n') {
                // Single line that wasn't path+range
                if Self::path_start_end_re().is_match(content) {
                    // e.g. "path 1-2" on one line. Treat as MissingLineNumbers.
                    return Err(RangeParsingError::MissingLineNumbers);
                } else if Self::single_number_re().is_match(content) {
                    // e.g. "path 55" on one line. Treat as MissingLineNumbers.
                    return Err(RangeParsingError::MissingLineNumbers);
                }
            }
            return Err(RangeParsingError::UnrecognizedFormat);
        }

        Ok(edit_targets)
    }
}

impl ParsableBlock for RangeBlockParser {
    fn id(&self) -> String {
        "range_blocks".to_string()
    }

    fn keyword(&self) -> &'static str {
        "range"
    }

    fn block_format_description(&self) -> String {
        "A 'range' block specifies a file path and 1-indexed line numbers (e.g., START_LINE-END_LINE) to be edited, or 'path/to/new/file.ext 0-0' for new file creation. Format: path/to/file.ext\\nSTART_LINE-END_LINE".to_string()
    }

    fn validate_raw_block(
        &self,
        raw_block: &RawCodeBlock,
        available_files: &[LabeledFile],
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        let edit_targets = self.parse_to_edit_target(raw_block, available_files)?;
        if edit_targets.is_empty() && !raw_block.content_after_keyword.trim().is_empty() {
            // This implies parse_to_edit_target returned Ok(vec![]) but there was content.
            // This shouldn't happen with current parsing logic if content exists.
            // If it does, it's an unrecognized format that didn't yield targets.
            return Err(Box::new(RangeParsingError::UnrecognizedFormat));
        }
        if edit_targets.is_empty() && raw_block.content_after_keyword.trim().is_empty() {
            // This is Ok if BlockEmpty was the parsing error, but validate_raw_block expects Ok or Err.
            // parse_to_edit_target would have returned Err(BlockEmpty).
            // This path in validate_raw_block implies parse_to_edit_target returned Ok(vec![]) for empty content,
            // which it doesn't. So, this specific condition is unlikely to be hit here.
            // If parse_to_edit_target returns Ok(vec![]) it means it successfully parsed zero targets,
            // which is not an error state for validate_raw_block itself unless expectations say otherwise.
        }

        let current_dir = std::env::current_dir()
            .map_err(|e| format!("Failed to get current directory for path validation: {}", e))?;

        for edit_target in edit_targets {
            let resolved_path_for_validation = if edit_target.file_path.is_absolute() {
                edit_target.file_path.clone()
            } else {
                current_dir.join(&edit_target.file_path)
            };

            if edit_target.start_line == 0 && edit_target.end_line == 0 {
                if resolved_path_for_validation.exists() {
                    let path_for_error = dunce::canonicalize(&resolved_path_for_validation)
                        .unwrap_or(resolved_path_for_validation);
                    return Err(Box::new(RangeParsingError::FileAlreadyExistsForNew(
                        path_for_error,
                    )));
                }
            } else {
                match dunce::canonicalize(&resolved_path_for_validation) {
                    Ok(canonical_path) => {
                        if !canonical_path.exists() {
                            return Err(Box::new(RangeParsingError::FileDoesNotExist(
                                canonical_path,
                            )));
                        }
                        if !available_files.iter().any(|f| f.path == canonical_path) {
                            return Err(Box::new(RangeParsingError::FileDoesNotExist(
                                canonical_path,
                            )));
                        }
                    }
                    Err(_) => {
                        return Err(Box::new(RangeParsingError::FileDoesNotExist(
                            resolved_path_for_validation,
                        )));
                    }
                }
            }
        }
        Ok(())
    }

    fn identify_malformed(
        &self,
        raw_block: &RawCodeBlock,
        _available_files: &[LabeledFile],
    ) -> Option<MalformedBlockInfo> {
        let content = raw_block.content_after_keyword.trim();
        let parser_id = self.id();

        if raw_block.keyword != self.keyword() && raw_block.keyword.is_empty() {
            // Keyword is missing. Check if content looks like a range.
            // Check for path\nSTART-END or path\nNUMBER
            let lines: Vec<&str> = content.lines().collect();
            if lines.len() == 2 {
                let path_part = lines[0].trim();
                let range_part = lines[1].trim();
                let path_looks_like_path = !path_part.is_empty()
                    && (path_part.contains('/')
                        || path_part.contains('.')
                        || PathBuf::from(path_part).components().count() > 0);
                let range_looks_like_range = Self::path_start_end_re().is_match(range_part)
                    || Self::single_number_re().is_match(range_part);

                if path_looks_like_path && range_looks_like_range {
                    return Some(MalformedBlockInfo {
                        raw_text: raw_block.full_block_text.clone(),
                        fix_suggestion: "This block appears to be a range specification but is missing the 'range' keyword after the opening ```. Please ensure it starts with '```range'.".to_string(),
                        parser_id,
                    });
                }
            }
        } else if raw_block.keyword == self.keyword() {
            // Keyword is correct, but parsing or validation failed.
            // The error from `parse_to_edit_target` or `validate_raw_block` can be used.
            // We call `validate_raw_block` as it encompasses parsing and file checks.
            // If `validate_raw_block` fails, its error message is usually good enough.
            match self.validate_raw_block(raw_block, _available_files) {
                Ok(_) => return None, // Valid, so not malformed.
                Err(e) => {
                    // Use the error message from the validation error directly.
                    // This simplifies logic as RangeParsingError::Display is already comprehensive.
                    return Some(MalformedBlockInfo {
                        raw_text: raw_block.full_block_text.clone(),
                        fix_suggestion: e.to_string(),
                        parser_id,
                    });
                }
            }
        }
        // Default return if none of the conditions above are met
        None
    }

    fn construct_fixer_prompt_segment(
        &self,
        malformed_blocks: &[MalformedBlockInfo],
        available_files: &[LabeledFile],
    ) -> Option<String> {
        if malformed_blocks.is_empty() {
            return None;
        }

        let mut prompt_segment = String::new();
        prompt_segment.push_str("For 'range' blocks (specifying file edits):\n");

        let show_available_files_hint = malformed_blocks.iter().any(|block| {
            block.fix_suggestion.contains("does not exist")
                || block.fix_suggestion.contains("already exists")
        });

        if show_available_files_hint && !available_files.is_empty() {
            prompt_segment
                .push_str("  Hint: Ensure file paths are correct. Available files for editing:\n");
            for lf in available_files {
                let display_path = lf
                    .path
                    .strip_prefix(std::env::current_dir().unwrap_or_default())
                    .unwrap_or(&lf.path)
                    .display();
                prompt_segment.push_str(&format!("    - {}\n", display_path));
            }
            prompt_segment.push_str("  Use 'path/to/new/file.ext 0-0' to create a new file.\n");
        }

        for (index, malformed) in malformed_blocks.iter().enumerate() {
            prompt_segment.push_str(&format!("  Problematic range block #{}:\n", index + 1));
            prompt_segment.push_str("  ```text\n");
            prompt_segment.push_str(&malformed.raw_text.replace("```", "'''"));
            prompt_segment.push_str("\n  ```\n");
            prompt_segment.push_str(&format!("  Issue: {}\n\n", malformed.fix_suggestion));
        }
        prompt_segment.push_str(&format!(
            "  Correct format: {}\n",
            self.block_format_description()
        ));
        Some(prompt_segment)
    }

    fn example_prompt_text(&self) -> String {
        format!(
            "{}\nExample:\n```{}\npath/to/your/file.ext\n10-15\n```\nFor new files, use range 0-0:\n```{}\npath/to/your/new_file.ext\n0-0\n```",
            self.block_format_description(),
            self.keyword(),
            self.keyword()
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::block_parsing::utils::RawCodeBlock;
    use std::env; // Required for current_dir in tests
    use tempfile::TempDir; // Added for TempDir guard

    // Helper to create a LabeledFile for tests.
    // Creates path_str relative to a *new* temp dir.
    // Returns the LabeledFile and the TempDir guard.
    fn new_test_labeled_file_in_new_temp_dir(
        path_str_in_temp: &str,
        content: &str,
    ) -> (LabeledFile, TempDir) {
        let temp_dir = tempfile::tempdir().expect("Failed to create temp dir");
        let test_path = temp_dir.path().join(path_str_in_temp);

        if let Some(parent) = test_path.parent() {
            std::fs::create_dir_all(parent).expect("Failed to create parent dirs for test file");
        }
        std::fs::write(&test_path, content).expect("Failed to write test file");

        let labeled_file = LabeledFile::new(dunce::canonicalize(test_path).unwrap(), content);
        (labeled_file, temp_dir)
    }

    // Helper to create a RawCodeBlock for tests
    fn new_raw_code_block(
        keyword: &str,
        content_after_keyword: &str,
        full_block_text: &str,
    ) -> RawCodeBlock {
        RawCodeBlock {
            keyword: keyword.to_string(),
            content_after_keyword: content_after_keyword.to_string(),
            full_block_text: full_block_text.to_string(),
        }
    }

    #[test]
    fn test_parse_to_edit_target_valid() {
        let parser = RangeBlockParser;
        let raw_block = new_raw_code_block(
            "range",
            "src/main.rs\n10-15",
            "```range\nsrc/main.rs\n10-15\n```",
        );
        let available_files: Vec<LabeledFile> = vec![];
        let result = parser.parse_to_edit_target(&raw_block, &available_files);
        assert!(result.is_ok());
        let targets = result.unwrap();
        assert_eq!(targets.len(), 1);
        assert_eq!(targets[0].file_path, PathBuf::from("src/main.rs"));
        assert_eq!(targets[0].start_line, 10);
        assert_eq!(targets[0].end_line, 15);
    }

    #[test]
    fn test_parse_to_edit_target_single_line_number() {
        let parser = RangeBlockParser;
        let raw_block =
            new_raw_code_block("range", "src/main.rs\n55", "```range\nsrc/main.rs\n55\n```");
        let available_files: Vec<LabeledFile> = vec![];
        let result = parser.parse_to_edit_target(&raw_block, &available_files);
        assert!(
            result.is_ok(),
            "Parsing single line number failed: {:?}",
            result.err()
        );
        let targets = result.unwrap();
        assert_eq!(targets.len(), 1);
        assert_eq!(targets[0].file_path, PathBuf::from("src/main.rs"));
        assert_eq!(targets[0].start_line, 55);
        assert_eq!(targets[0].end_line, 55);
    }

    #[test]
    fn test_parse_to_edit_target_single_line_number_zero_for_new_file() {
        let parser = RangeBlockParser;
        let raw_block =
            new_raw_code_block("range", "new_file.txt\n0", "```range\nnew_file.txt\n0\n```");
        let available_files: Vec<LabeledFile> = vec![];
        let result = parser.parse_to_edit_target(&raw_block, &available_files);
        assert!(
            result.is_ok(),
            "Parsing single '0' for new file failed: {:?}",
            result.err()
        );
        let targets = result.unwrap();
        assert_eq!(targets.len(), 1);
        assert_eq!(targets[0].file_path, PathBuf::from("new_file.txt"));
        assert_eq!(targets[0].start_line, 0);
        assert_eq!(targets[0].end_line, 0);
    }

    #[test]
    fn test_parse_to_edit_target_new_file() {
        let parser = RangeBlockParser;
        let raw_block = new_raw_code_block(
            "range",
            "new_file.txt\n0-0",
            "```range\nnew_file.txt\n0-0\n```",
        );
        let available_files: Vec<LabeledFile> = vec![];
        let result = parser.parse_to_edit_target(&raw_block, &available_files);
        assert!(result.is_ok());
        let targets = result.unwrap();
        assert_eq!(targets.len(), 1);
        assert_eq!(targets[0].file_path, PathBuf::from("new_file.txt"));
        assert_eq!(targets[0].start_line, 0);
        assert_eq!(targets[0].end_line, 0);
    }

    #[test]
    fn test_parse_to_edit_target_multi_range_valid() {
        let parser = RangeBlockParser;
        let raw_block = new_raw_code_block(
            "range",
            "src/main.rs\n1-2,5-6,10-10",
            "```range\nsrc/main.rs\n1-2,5-6,10-10\n```",
        );
        let available_files: Vec<LabeledFile> = vec![];
        let result = parser.parse_to_edit_target(&raw_block, &available_files);
        assert!(result.is_ok(), "Parsing failed: {:?}", result.err());
        let targets = result.unwrap();
        assert_eq!(targets.len(), 3);
        assert_eq!(
            targets[0],
            EditTarget {
                file_path: PathBuf::from("src/main.rs"),
                start_line: 1,
                end_line: 2
            }
        );
        assert_eq!(
            targets[1],
            EditTarget {
                file_path: PathBuf::from("src/main.rs"),
                start_line: 5,
                end_line: 6
            }
        );
        assert_eq!(
            targets[2],
            EditTarget {
                file_path: PathBuf::from("src/main.rs"),
                start_line: 10,
                end_line: 10
            }
        );
    }

    #[test]
    fn test_parse_to_edit_target_multi_range_invalid_format_in_part() {
        let parser = RangeBlockParser;
        let spec = "src/main.rs\n1-2,bad,5-6";
        let raw_block = new_raw_code_block("range", spec, &format!("```range\n{}\n```", spec));
        let available_files: Vec<LabeledFile> = vec![];
        let result = parser.parse_to_edit_target(&raw_block, &available_files);
        assert_eq!(
            result,
            Err(RangeParsingError::MultiRangeContainsInvalid(
                "1-2,bad,5-6".to_string()
            ))
        );
    }

    #[test]
    fn test_parse_to_edit_target_multi_range_invalid_logic_in_part() {
        let parser = RangeBlockParser;
        let spec = "src/main.rs\n1-2,6-5,10-10"; // 6-5 is invalid
        let raw_block = new_raw_code_block("range", spec, &format!("```range\n{}\n```", spec));
        let available_files: Vec<LabeledFile> = vec![];
        let result = parser.parse_to_edit_target(&raw_block, &available_files);
        assert_eq!(
            result,
            Err(RangeParsingError::MultiRangeContainsInvalid(
                "1-2,6-5,10-10".to_string()
            ))
        );
    }

    #[test]
    fn test_parse_to_edit_target_path_missing() {
        let parser = RangeBlockParser;
        let raw_block = new_raw_code_block("range", "\n10-15", "```range\n\n10-15\n```");
        let available_files: Vec<LabeledFile> = vec![];
        let result = parser.parse_to_edit_target(&raw_block, &available_files);
        assert_eq!(result, Err(RangeParsingError::PathMissing));
    }

    #[test]
    fn test_parse_to_edit_target_invalid_lines_format() {
        let parser = RangeBlockParser;
        let raw_block = new_raw_code_block(
            "range",
            "src/main.rs\n10-abc",
            "```range\nsrc/main.rs\n10-abc\n```",
        );
        let available_files: Vec<LabeledFile> = vec![];
        let result = parser.parse_to_edit_target(&raw_block, &available_files);
        assert_eq!(
            result,
            Err(RangeParsingError::InvalidLinesFormat("10-abc".to_string()))
        );
    }

    #[test]
    fn test_parse_to_edit_target_invalid_single_line_format() {
        let parser = RangeBlockParser;
        let raw_block = new_raw_code_block(
            "range",
            "src/main.rs\nabc",
            "```range\nsrc/main.rs\nabc\n```",
        );
        let available_files: Vec<LabeledFile> = vec![];
        let result = parser.parse_to_edit_target(&raw_block, &available_files);
        assert_eq!(
            result,
            Err(RangeParsingError::InvalidLinesFormat("abc".to_string()))
        );
    }

    #[test]
    fn test_parse_to_edit_target_start_greater_than_end() {
        let parser = RangeBlockParser;
        let raw_block = new_raw_code_block(
            "range",
            "src/main.rs\n15-10",
            "```range\nsrc/main.rs\n15-10\n```",
        );
        let available_files: Vec<LabeledFile> = vec![];
        let result = parser.parse_to_edit_target(&raw_block, &available_files);
        assert_eq!(result, Err(RangeParsingError::StartGreaterThanEnd));
    }

    #[test]
    fn test_validate_raw_block_valid_existing_file() {
        let temp_dir_obj = tempfile::tempdir().unwrap();
        let file_path_in_temp = temp_dir_obj.path().join("src").join("test.rs");
        std::fs::create_dir_all(file_path_in_temp.parent().unwrap()).unwrap();
        std::fs::write(&file_path_in_temp, "test content").unwrap();

        let parser = RangeBlockParser;
        // The block content should use the absolute path to the temporary file for validation.
        let raw_block_content = format!("{}\n1-1", file_path_in_temp.display());
        let raw_block_full = format!("```range\n{}\n```", raw_block_content);
        let raw_block = new_raw_code_block("range", &raw_block_content, &raw_block_full);

        let labeled_file = LabeledFile::new(
            dunce::canonicalize(&file_path_in_temp).unwrap(),
            "test content",
        );
        let available_files = vec![labeled_file];

        let result = parser.validate_raw_block(&raw_block, &available_files);
        assert!(result.is_ok(), "Validation failed: {:?}", result.err());
    }

    #[test]
    fn test_validate_raw_block_file_does_not_exist() {
        let parser = RangeBlockParser;
        let non_existent_path = PathBuf::from("non_existent.rs");
        let raw_block = new_raw_code_block(
            "range",
            "non_existent.rs\n1-2",
            "```range\nnon_existent.rs\n1-2\n```",
        );
        let available_files: Vec<LabeledFile> = vec![];
        let result = parser.validate_raw_block(&raw_block, &available_files);
        assert!(result.is_err());
        if let Err(e) = result {
            // Expect absolute path in error
            let expected_abs_path = env::current_dir().unwrap().join(non_existent_path);
            assert_eq!(
                e.to_string(),
                RangeParsingError::FileDoesNotExist(expected_abs_path).to_string()
            );
        }
    }

    #[test]
    fn test_validate_raw_block_new_file_already_exists() {
        let temp_dir_obj = tempfile::tempdir().unwrap();
        let file_path_in_temp = temp_dir_obj.path().join("existing_for_new.rs");
        std::fs::write(&file_path_in_temp, "content").unwrap();

        let parser = RangeBlockParser;
        let raw_block_content = format!("{}\n0-0", file_path_in_temp.display());
        let raw_block_full = format!("```range\n{}\n```", raw_block_content);
        let raw_block = new_raw_code_block("range", &raw_block_content, &raw_block_full);

        let labeled_file =
            LabeledFile::new(dunce::canonicalize(&file_path_in_temp).unwrap(), "content");
        let available_files = vec![labeled_file];

        let result = parser.validate_raw_block(&raw_block, &available_files);
        assert!(result.is_err());
        if let Err(e) = result {
            assert_eq!(
                e.to_string(),
                RangeParsingError::FileAlreadyExistsForNew(
                    dunce::canonicalize(&file_path_in_temp).unwrap()
                )
                .to_string()
            );
        }
    }

    #[test]
    fn test_identify_malformed_missing_keyword() {
        let parser = RangeBlockParser;
        let raw_block =
            new_raw_code_block("", "src/main.rs\n10-15", "```\nsrc/main.rs\n10-15\n```");
        let available_files: Vec<LabeledFile> = vec![];
        let malformed_info = parser.identify_malformed(&raw_block, &available_files);
        assert!(malformed_info.is_some());
        let info = malformed_info.unwrap();
        assert_eq!(info.parser_id, "range_blocks");
        assert!(info.fix_suggestion.contains("missing the 'range' keyword"));
    }

    #[test]
    fn test_identify_malformed_wrong_keyword_but_content_looks_like_range() {
        let parser = RangeBlockParser;
        // Keyword is "replace", but content is clearly for "range"
        let raw_block = new_raw_code_block(
            "replace",
            "src/main.rs\n10-15",
            "```replace\nsrc/main.rs\n10-15\n```",
        );
        let available_files: Vec<LabeledFile> = vec![];
        // identify_malformed for RangeBlockParser should return None if keyword is explicitly different and known (like "replace")
        let malformed_info = parser.identify_malformed(&raw_block, &available_files);
        assert!(malformed_info.is_none());
    }

    #[test]
    fn test_identify_malformed_correct_keyword_bad_format() {
        let parser = RangeBlockParser;
        let raw_block = new_raw_code_block(
            "range",
            "src/main.rs\n10-abc",
            "```range\nsrc/main.rs\n10-abc\n```",
        );
        let available_files: Vec<LabeledFile> = vec![];
        let malformed_info = parser.identify_malformed(&raw_block, &available_files);
        assert!(malformed_info.is_some());
        let info = malformed_info.unwrap();
        assert_eq!(info.parser_id, "range_blocks");
        // The suggestion will now come directly from the error's Display impl
        assert_eq!(
            info.fix_suggestion,
            RangeParsingError::InvalidLinesFormat("10-abc".to_string()).to_string()
        );
    }

    #[test]
    fn test_identify_malformed_correct_keyword_bad_single_line_format() {
        let parser = RangeBlockParser;
        let raw_block = new_raw_code_block(
            "range",
            "src/main.rs\nabc",
            "```range\nsrc/main.rs\nabc\n```",
        );
        let available_files: Vec<LabeledFile> = vec![]; // Mock available files
        let malformed_info = parser.identify_malformed(&raw_block, &available_files);
        assert!(malformed_info.is_some());
        let info = malformed_info.unwrap();
        assert_eq!(info.parser_id, "range_blocks");
        assert_eq!(
            info.fix_suggestion,
            RangeParsingError::InvalidLinesFormat("abc".to_string()).to_string()
        );
    }

    #[test]
    fn test_identify_malformed_correct_keyword_valid_single_line_no_error() {
        let temp_dir_obj = tempfile::tempdir().unwrap();
        let file_path_in_temp = temp_dir_obj.path().join("src").join("test.rs");
        std::fs::create_dir_all(file_path_in_temp.parent().unwrap()).unwrap();
        std::fs::write(&file_path_in_temp, "test content\nline2").unwrap();

        let parser = RangeBlockParser;
        let raw_block_content = format!("{}\n1", file_path_in_temp.display());
        let raw_block_full = format!("```range\n{}\n```", raw_block_content);
        let raw_block = new_raw_code_block("range", &raw_block_content, &raw_block_full);

        let labeled_file = LabeledFile::new(
            dunce::canonicalize(&file_path_in_temp).unwrap(),
            "test content\nline2",
        );
        let available_files = vec![labeled_file];

        let malformed_info = parser.identify_malformed(&raw_block, &available_files);
        assert!(
            malformed_info.is_none(),
            "Expected no malformed info for valid single line number, got: {:?}",
            malformed_info
        );
    }

    #[test]
    fn test_construct_fixer_prompt_segment() {
        let parser = RangeBlockParser;
        let malformed_blocks = vec![
            MalformedBlockInfo {
                raw_text: "```range\n1-2\n```".to_string(),
                fix_suggestion: "The file path is missing.".to_string(),
                parser_id: "range_blocks".to_string(),
            },
            MalformedBlockInfo {
                raw_text: "```range\nnon_existent.rs\n1-2\n```".to_string(),
                fix_suggestion:
                    "The file 'non_existent.rs' specified in this range block does not exist."
                        .to_string(),
                parser_id: "range_blocks".to_string(),
            },
        ];
        let (labeled_file, _temp_dir_guard) =
            new_test_labeled_file_in_new_temp_dir("src/main.rs", "fn main() {}");
        let available_files = vec![labeled_file];
        let prompt_segment = parser
            .construct_fixer_prompt_segment(&malformed_blocks, &available_files)
            .unwrap();

        assert!(prompt_segment.contains("For 'range' blocks"));
        assert!(prompt_segment.contains("Problematic range block #1"));
        assert!(prompt_segment.contains("'''range\n1-2\n'''"));
        assert!(prompt_segment.contains("Issue: The file path is missing."));
        assert!(prompt_segment.contains("Problematic range block #2"));
        assert!(prompt_segment.contains(
            "Issue: The file 'non_existent.rs' specified in this range block does not exist."
        ));
        assert!(prompt_segment.contains("Available files for editing:"));
        assert!(prompt_segment.contains(&format!(
            "- {}",
            available_files[0]
                .path
                .strip_prefix(std::env::current_dir().unwrap_or_default())
                .unwrap_or(&available_files[0].path)
                .display()
        )));
        assert!(prompt_segment.contains("Correct format: A 'range' block specifies a file path and 1-indexed line numbers (e.g., START_LINE-END_LINE) to be edited, or 'path/to/new/file.ext 0-0' for new file creation. Format: path/to/file.ext\\nSTART_LINE-END_LINE"));
    }
}
