use crate::block_parsing::traits::{MalformedBlockInfo, ParsableBlock};
use crate::block_parsing::utils::RawCodeBlock;
use crate::files::file_handler::LabeledFile; // Required by ParsableBlock trait, though not used by all methods
use std::error::Error;
use std::fmt;

// ReplaceParsingError is an empty enum as per plan, meaning parsing a `RawCodeBlock`
// identified as `replace` into its string content is considered infallible.
// The `validate_raw_block` will thus always return `Ok(())`.
// If, in the future, `replace` blocks have stricter content rules (e.g., not empty),
// this enum could be expanded.
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum ReplaceParsingError {} // Empty enum

impl fmt::Display for ReplaceParsingError {
    fn fmt(&self, _f: &mut fmt::Formatter<'_>) -> fmt::Result {
        // Unreachable, as there are no variants
        match *self {}
    }
}

impl Error for ReplaceParsingError {}

pub struct ReplaceBlockParser;

impl ReplaceBlockParser {
    /// Parses the content of a `RawCodeBlock` (assumed to be a replace block) into a string.
    /// This is infallible for now, as any content is accepted.
    pub fn parse_to_string(&self, raw_block: &RawCodeBlock) -> Result<String, ReplaceParsingError> {
        Ok(raw_block.content_after_keyword.clone())
    }
}

impl ParsableBlock for ReplaceBlockParser {
    fn id(&self) -> String {
        "replace_block".to_string()
    }

    fn keyword(&self) -> &'static str {
        "replace"
    }

    fn block_format_description(&self) -> String {
        "A 'replace' block provides the new code content. Format: ```replace\\n<new_code_here>\\n```".to_string()
    }

    fn validate_raw_block(
        &self,
        raw_block: &RawCodeBlock,
        _available_files: &[LabeledFile], // Not used for replace block validation
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        match self.parse_to_string(raw_block) {
            Ok(_) => Ok(()),
            Err(e) => Err(Box::new(e)), // Should be unreachable with current empty ReplaceParsingError
        }
    }

    fn identify_malformed(
        &self,
        raw_block: &RawCodeBlock,
        _available_files: &[LabeledFile], // Not used
    ) -> Option<MalformedBlockInfo> {
        // Simple check: if keyword is missing or a common misspelling, suggest it's a replace block.
        if raw_block.keyword != self.keyword()
            && (raw_block.keyword.is_empty()
                || raw_block.keyword.starts_with("replac")
                || raw_block.keyword.starts_with("repalc"))
            && !raw_block.content_after_keyword.is_empty()
        // Avoid flagging empty ``` ``` as malformed replace
        {
            // Heuristic: if it has content and a keyword that's close or empty, it might be a malformed replace block.
            // This is a weak heuristic. A stronger one might check if the content looks like code.
            if raw_block.keyword.is_empty()
                && raw_block.content_after_keyword.lines().any(|line| {
                    line.contains("fn") || line.contains("let") || line.contains("const")
                })
            {
                return Some(MalformedBlockInfo {
                    raw_text: raw_block.full_block_text.clone(),
                    fix_suggestion: "This block appears to contain code but is missing the 'replace' keyword. Please ensure it starts with '```replace'.".to_string(),
                    parser_id: self.id(),
                });
            } else if !raw_block.keyword.is_empty() {
                // Misspelled keyword
                return Some(MalformedBlockInfo {
                    raw_text: raw_block.full_block_text.clone(),
                    fix_suggestion: format!("This block has a keyword '{}' which might be a misspelling of 'replace'. Please ensure it starts with '```replace'.", raw_block.keyword),
                    parser_id: self.id(),
                });
            }
        }
        None
    }

    fn construct_fixer_prompt_segment(
        &self,
        malformed_blocks: &[MalformedBlockInfo],
        _available_files: &[LabeledFile], // Not used
    ) -> Option<String> {
        if malformed_blocks.is_empty() {
            return None;
        }
        let mut prompt_segment = String::new();
        prompt_segment.push_str("For 'replace' blocks (providing new code content):\n");
        for (index, malformed) in malformed_blocks.iter().enumerate() {
            prompt_segment.push_str(&format!("  Problematic replace block #{}:\n", index + 1));
            prompt_segment.push_str("  ```text\n");
            prompt_segment.push_str(&malformed.raw_text.replace("```", "'''"));
            prompt_segment.push_str("\n  ```\n");
            prompt_segment.push_str(&format!("  Issue: {}\n\n", malformed.fix_suggestion));
        }
        prompt_segment.push_str(&format!(
            "  Correct format: {}. IMPORTANT: If correcting this, your response should contain *only* the 'replace' block with the corrected content and no other blocks or explanatory text.\n",
            self.block_format_description()
        ));
        Some(prompt_segment)
    }

    fn example_prompt_text(&self) -> String {
        format!(
            "{}\nExample:\n```{}\nfn new_function_content() {{\n    // ... your code here ...\n}}\n```",
            self.block_format_description(),
            self.keyword()
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::block_parsing::utils::RawCodeBlock;

    fn new_raw_code_block(
        keyword: &str,
        content_after_keyword: &str,
        full_block_text: &str,
    ) -> RawCodeBlock {
        RawCodeBlock {
            keyword: keyword.to_string(),
            content_after_keyword: content_after_keyword.to_string(),
            full_block_text: full_block_text.to_string(),
        }
    }

    #[test]
    fn test_parse_to_string_success() {
        let parser = ReplaceBlockParser;
        let raw_block = new_raw_code_block(
            "replace",
            "fn main() {\n    println!(\"Hello, world!\");\n}\n",
            "```replace\nfn main() {\n    println!(\"Hello, world!\");\n}\n```",
        );
        let code = parser.parse_to_string(&raw_block).unwrap();
        assert_eq!(code, "fn main() {\n    println!(\"Hello, world!\");\n}\n");
    }

    #[test]
    fn test_parse_to_string_empty_content() {
        let parser = ReplaceBlockParser;
        let raw_block = new_raw_code_block("replace", "\n", "```replace\n```");
        let code = parser.parse_to_string(&raw_block).unwrap();
        assert_eq!(code, "\n");
    }

    #[test]
    fn test_parse_to_string_fully_empty_block() {
        let parser = ReplaceBlockParser;
        let raw_block = new_raw_code_block("replace", "", "```replace```");
        let code = parser.parse_to_string(&raw_block).unwrap();
        assert_eq!(code, "");
    }

    #[test]
    fn test_validate_raw_block_always_ok() {
        let parser = ReplaceBlockParser;
        let raw_block =
            new_raw_code_block("replace", "some content", "```replace\nsome content\n```");
        let available_files: Vec<LabeledFile> = vec![];
        let result = parser.validate_raw_block(&raw_block, &available_files);
        assert!(result.is_ok());

        let raw_block_empty = new_raw_code_block("replace", "", "```replace```");
        let result_empty = parser.validate_raw_block(&raw_block_empty, &available_files);
        assert!(result_empty.is_ok());
    }

    #[test]
    fn test_identify_malformed_missing_keyword_with_code_like_content() {
        let parser = ReplaceBlockParser;
        let raw_block = new_raw_code_block("", "fn test() {}\n", "```\nfn test() {}\n```");
        let available_files: Vec<LabeledFile> = vec![];
        let malformed_info = parser.identify_malformed(&raw_block, &available_files);
        assert!(malformed_info.is_some());
        assert!(malformed_info
            .unwrap()
            .fix_suggestion
            .contains("missing the 'replace' keyword"));
    }

    #[test]
    fn test_identify_malformed_misspelled_keyword() {
        let parser = ReplaceBlockParser;
        let raw_block = new_raw_code_block("replac", "content", "```replac\ncontent\n```");
        let available_files: Vec<LabeledFile> = vec![];
        let malformed_info = parser.identify_malformed(&raw_block, &available_files);
        assert!(malformed_info.is_some());
        assert!(malformed_info
            .unwrap()
            .fix_suggestion
            .contains("misspelling of 'replace'"));
    }

    #[test]
    fn test_identify_malformed_correct_keyword_returns_none() {
        let parser = ReplaceBlockParser;
        let raw_block = new_raw_code_block("replace", "content", "```replace\ncontent\n```");
        let available_files: Vec<LabeledFile> = vec![];
        let malformed_info = parser.identify_malformed(&raw_block, &available_files);
        assert!(malformed_info.is_none());
    }

    #[test]
    fn test_construct_fixer_prompt_segment() {
        let parser = ReplaceBlockParser;
        let malformed_blocks = vec![MalformedBlockInfo {
            raw_text: "```\nfn test() {}\n```".to_string(),
            fix_suggestion:
                "This block appears to contain code but is missing the 'replace' keyword."
                    .to_string(),
            parser_id: "replace_block".to_string(),
        }];
        let available_files: Vec<LabeledFile> = vec![];
        let prompt_segment = parser
            .construct_fixer_prompt_segment(&malformed_blocks, &available_files)
            .unwrap();

        assert!(prompt_segment.contains("For 'replace' blocks"));
        assert!(prompt_segment.contains("Problematic replace block #1"));
        assert!(prompt_segment.contains("'''\nfn test() {}\n'''"));
        assert!(prompt_segment.contains(
            "Issue: This block appears to contain code but is missing the 'replace' keyword."
        ));
        assert!(prompt_segment
            .contains("Correct format: A 'replace' block provides the new code content."));
    }
}
