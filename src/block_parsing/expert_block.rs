use crate::block_parsing::traits::{MalformedBlockInfo, ParsableBlock};
use crate::block_parsing::utils::RawCodeBlock;
use crate::files::file_handler::LabeledFile; // Required by ParsableBlock trait
use std::error::Error;
use std::fmt;

#[derive(Debug, <PERSON>lone, PartialEq, Eq)]
pub enum ExpertBlockParsingError {
    InvalidBooleanValue(String),
    EmptyContent,
}

impl fmt::Display for ExpertBlockParsingError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ExpertBlockParsingError::InvalidBooleanValue(val) => write!(
                f,
                "Invalid boolean value in 'expert' block: '{}'. Expected 'true' or 'false'.",
                val
            ),
            ExpertBlockParsingError::EmptyContent => write!(
                f,
                "The 'expert' block cannot be empty. It should contain 'true' or 'false'."
            ),
        }
    }
}

impl Error for ExpertBlockParsingError {}

pub struct ExpertBlockParser;

impl ExpertBlockParser {
    pub fn parse_to_bool(&self, raw_block: &RawCodeBlock) -> Result<bool, ExpertBlockParsingError> {
        let content = raw_block.content_after_keyword.trim().to_lowercase();
        if content.is_empty() {
            return Err(ExpertBlockParsingError::EmptyContent);
        }
        match content.as_str() {
            "true" => Ok(true),
            "false" => Ok(false),
            _ => Err(ExpertBlockParsingError::InvalidBooleanValue(content)),
        }
    }
}

impl ParsableBlock for ExpertBlockParser {
    fn id(&self) -> String {
        "expert_block".to_string()
    }

    fn keyword(&self) -> &'static str {
        "expert"
    }

    fn block_format_description(&self) -> String {
        "An 'expert' block indicates if expert intervention is needed. Format: ```expert\\ntrue\\n``` or ```expert\\nfalse\\n```".to_string()
    }

    fn validate_raw_block(
        &self,
        raw_block: &RawCodeBlock,
        _available_files: &[LabeledFile],
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        self.parse_to_bool(raw_block)
            .map(|_| ())
            .map_err(|e| Box::new(e) as Box<dyn Error + Send + Sync>)
    }

    fn identify_malformed(
        &self,
        raw_block: &RawCodeBlock,
        _available_files: &[LabeledFile],
    ) -> Option<MalformedBlockInfo> {
        let content = raw_block.content_after_keyword.trim();
        let parser_id = self.id();

        if raw_block.keyword != self.keyword()
            && (raw_block.keyword.is_empty() || raw_block.keyword.to_lowercase().starts_with("exp"))
        {
            if content.eq_ignore_ascii_case("true") || content.eq_ignore_ascii_case("false") {
                Some(MalformedBlockInfo {
                    raw_text: raw_block.full_block_text.clone(),
                    fix_suggestion: format!("This block appears to be an expert directive but has a missing or misspelled keyword (expected 'expert'). Found '{}'.", raw_block.keyword),
                    parser_id,
                })
            } else {
                None
            }
        } else if raw_block.keyword == self.keyword() {
            match self.parse_to_bool(raw_block) {
                Ok(_) => None,
                Err(e) => Some(MalformedBlockInfo {
                    raw_text: raw_block.full_block_text.clone(),
                    fix_suggestion: e.to_string(),
                    parser_id,
                }),
            }
        } else {
            None
        }
    }

    fn construct_fixer_prompt_segment(
        &self,
        malformed_blocks: &[MalformedBlockInfo],
        _available_files: &[LabeledFile],
    ) -> Option<String> {
        if malformed_blocks.is_empty() {
            return None;
        }
        let mut prompt_segment = String::new();
        prompt_segment.push_str("For 'expert' blocks (indicating if expert help is needed):\n");
        for (index, malformed) in malformed_blocks.iter().enumerate() {
            prompt_segment.push_str(&format!("  Problematic expert block #{}:\n", index + 1));
            prompt_segment.push_str("  ```text\n");
            prompt_segment.push_str(&malformed.raw_text.replace("```", "'''"));
            prompt_segment.push_str("\n  ```\n");
            prompt_segment.push_str(&format!("  Issue: {}\n\n", malformed.fix_suggestion));
        }
        prompt_segment.push_str(&format!(
            "  Correct format: {}. Content must be 'true' or 'false'.\n",
            self.block_format_description()
        ));
        Some(prompt_segment)
    }

    fn example_prompt_text(&self) -> String {
        format!(
            "{}\nExample:\n```expert\ntrue\n```",
            self.block_format_description()
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    fn new_raw_code_block(
        keyword: &str,
        content_after_keyword: &str,
        full_block_text: &str,
    ) -> RawCodeBlock {
        RawCodeBlock {
            keyword: keyword.to_string(),
            content_after_keyword: content_after_keyword.to_string(),
            full_block_text: full_block_text.to_string(),
        }
    }

    #[test]
    fn test_parse_to_bool_valid() {
        let parser = ExpertBlockParser;
        let raw_block_true = new_raw_code_block("expert", "true", "```expert\ntrue\n```");
        assert_eq!(parser.parse_to_bool(&raw_block_true), Ok(true));
        let raw_block_false = new_raw_code_block("expert", "false", "```expert\nfalse\n```");
        assert_eq!(parser.parse_to_bool(&raw_block_false), Ok(false));
        let raw_block_mixed_case = new_raw_code_block("expert", "TrUe", "```expert\nTrUe\n```");
        assert_eq!(parser.parse_to_bool(&raw_block_mixed_case), Ok(true));
    }

    #[test]
    fn test_parse_to_bool_empty_content() {
        let parser = ExpertBlockParser;
        let raw_block = new_raw_code_block("expert", " ", "```expert\n \n```");
        assert_eq!(
            parser.parse_to_bool(&raw_block),
            Err(ExpertBlockParsingError::EmptyContent)
        );
    }

    #[test]
    fn test_parse_to_bool_invalid_value() {
        let parser = ExpertBlockParser;
        let raw_block = new_raw_code_block("expert", "maybe", "```expert\nmaybe\n```");
        assert_eq!(
            parser.parse_to_bool(&raw_block),
            Err(ExpertBlockParsingError::InvalidBooleanValue(
                "maybe".to_string()
            ))
        );
    }
}
