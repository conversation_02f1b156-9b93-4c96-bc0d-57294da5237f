use crate::block_parsing::traits::{MalformedBlockInfo, ParsableBlock};
use crate::block_parsing::utils::RawCodeBlock;
use crate::files::file_handler::LabeledFile; // Required by ParsableBlock trait
use std::error::Error;
use std::fmt;
use std::path::PathBuf;

#[derive(Debug, Clone, PartialEq, Eq)]
pub enum CompleteFilesParsingError {
    EmptyContent, // If the block is empty after the keyword
}

impl fmt::Display for CompleteFilesParsingError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            CompleteFilesParsingError::EmptyContent => write!(
                f,
                "The 'complete-files' block cannot be empty. It should list file paths."
            ),
        }
    }
}

impl Error for CompleteFilesParsingError {}

pub struct CompleteFilesBlockParser;

impl CompleteFilesBlockParser {
    pub fn parse_to_file_list(
        &self,
        raw_block: &RawCodeBlock,
    ) -> Result<Vec<PathBuf>, CompleteFilesParsingError> {
        let content = raw_block.content_after_keyword.trim();
        if content.is_empty() {
            // Allow empty if the keyword is present, means LLM found no files.
            // This behavior might be preferable to an error.
            // If an error is desired for an empty list, uncomment the next line.
            // return Err(CompleteFilesParsingError::EmptyContent);
            return Ok(Vec::new());
        }
        let files: Vec<PathBuf> = content
            .lines()
            .map(|s| s.trim())
            .filter(|s| !s.is_empty())
            .map(PathBuf::from)
            .collect();
        Ok(files)
    }
}

impl ParsableBlock for CompleteFilesBlockParser {
    fn id(&self) -> String {
        "complete_files_block".to_string()
    }

    fn keyword(&self) -> &'static str {
        "complete-files"
    }

    fn block_format_description(&self) -> String {
        "A 'complete-files' block signals research completion and lists discovered relevant file paths, each on a new line. Format: ```complete-files\n<path1>\n<path2>\n...\n```".to_string()
    }

    fn validate_raw_block(
        &self,
        raw_block: &RawCodeBlock,
        _available_files: &[LabeledFile],
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        // The current parse_to_file_list allows an empty list if the block itself is not empty.
        // If the block's content (after keyword) is empty, parse_to_file_list returns Ok(Vec::new()).
        // If strict validation against an absolutely empty content (post-keyword) is needed,
        // parse_to_file_list could return an error, or it could be checked here.
        // For now, accepting an empty list of files is fine.
        match self.parse_to_file_list(raw_block) {
            Ok(_) => Ok(()),
            Err(e) => Err(Box::new(e)), // Should be unreachable if parse_to_file_list doesn't error on empty content
        }
    }

    fn identify_malformed(
        &self,
        raw_block: &RawCodeBlock,
        _available_files: &[LabeledFile],
    ) -> Option<MalformedBlockInfo> {
        let content = raw_block.content_after_keyword.trim();
        let parser_id = self.id();

        if raw_block.keyword != self.keyword()
            && (raw_block.keyword.is_empty()
                || raw_block.keyword.to_lowercase().starts_with("complete"))
        {
            // Keyword is missing or misspelled.
            // Check if content looks like a list of file paths.
            if !content.is_empty()
                && content
                    .lines()
                    .any(|line| line.contains('/') || line.contains('.') || line.contains('\\'))
            {
                return Some(MalformedBlockInfo {
                    raw_text: raw_block.full_block_text.clone(),
                    fix_suggestion: format!("This block appears to be a list of files but has a missing or misspelled keyword (expected 'complete-files'). Found '{}'.", raw_block.keyword),
                    parser_id,
                });
            }
        } else if raw_block.keyword == self.keyword() {
            // Keyword is correct, but content might be problematic if stricter rules were applied.
            // Currently, parse_to_file_list is very permissive.
            // If EmptyContent error was enabled in parse_to_file_list:
            // if content.is_empty() {
            //     return Some(MalformedBlockInfo {
            //         raw_text: raw_block.full_block_text.clone(),
            //         fix_suggestion: "The 'complete-files' block is empty. Please provide a list of file paths or remove the block if no files were found.".to_string(),
            //         parser_id,
            //     });
            // }
        }
        None
    }

    fn construct_fixer_prompt_segment(
        &self,
        malformed_blocks: &[MalformedBlockInfo],
        _available_files: &[LabeledFile],
    ) -> Option<String> {
        if malformed_blocks.is_empty() {
            return None;
        }
        let mut prompt_segment = String::new();
        prompt_segment.push_str(
            "For 'complete-files' blocks (listing discovered files):
",
        );
        for (index, malformed) in malformed_blocks.iter().enumerate() {
            prompt_segment.push_str(&format!(
                "  Problematic complete-files block #{}:
",
                index + 1
            ));
            prompt_segment.push_str(
                "  ```text
",
            );
            prompt_segment.push_str(&malformed.raw_text.replace("```", "'''"));
            prompt_segment.push_str(
                "
  ```
",
            );
            prompt_segment.push_str(&format!(
                "  Issue: {}

",
                malformed.fix_suggestion
            ));
        }
        prompt_segment.push_str(&format!(
            "  Correct format: {}. Each file path should be on a new line.
",
            self.block_format_description()
        ));
        Some(prompt_segment)
    }

    fn example_prompt_text(&self) -> String {
        format!("```complete-files\nsrc/main.rs\nsrc/utils/mod.rs\nREADME.md\n```")
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    fn new_raw_code_block(
        keyword: &str,
        content_after_keyword: &str,
        full_block_text: &str,
    ) -> RawCodeBlock {
        RawCodeBlock {
            keyword: keyword.to_string(),
            content_after_keyword: content_after_keyword.to_string(),
            full_block_text: full_block_text.to_string(),
        }
    }

    #[test]
    fn test_parse_to_file_list_valid() {
        let parser = CompleteFilesBlockParser;
        let raw_block = new_raw_code_block(
            "complete-files",
            "path/to/file1.rs
  another/path/file2.txt  
file3.md",
            "```complete-files
path/to/file1.rs
  another/path/file2.txt  
file3.md
```",
        );
        let result = parser.parse_to_file_list(&raw_block);
        assert!(result.is_ok());
        let files = result.unwrap();
        assert_eq!(files.len(), 3);
        assert_eq!(files[0], PathBuf::from("path/to/file1.rs"));
        assert_eq!(files[1], PathBuf::from("another/path/file2.txt"));
        assert_eq!(files[2], PathBuf::from("file3.md"));
    }

    #[test]
    fn test_parse_to_file_list_empty_content_yields_empty_vec() {
        let parser = CompleteFilesBlockParser;
        let raw_block = new_raw_code_block(
            "complete-files",
            "   
   ",
            "```complete-files
   
   
```",
        );
        let result = parser.parse_to_file_list(&raw_block);
        assert!(result.is_ok());
        assert!(result.unwrap().is_empty());
    }

    #[test]
    fn test_parse_to_file_list_truly_empty_block_content_yields_empty_vec() {
        let parser = CompleteFilesBlockParser;
        let raw_block = new_raw_code_block(
            "complete-files",
            "",
            "```complete-files
```",
        );
        let result = parser.parse_to_file_list(&raw_block);
        assert!(result.is_ok());
        assert!(result.unwrap().is_empty());
    }

    #[test]
    fn test_validate_raw_block_always_ok_for_now() {
        let parser = CompleteFilesBlockParser;
        let files: Vec<LabeledFile> = vec![];
        let raw_block_with_files = new_raw_code_block(
            "complete-files",
            "file1.txt
file2.txt",
            "```complete-files
file1.txt
file2.txt
```",
        );
        assert!(parser
            .validate_raw_block(&raw_block_with_files, &files)
            .is_ok());

        let raw_block_empty_content = new_raw_code_block(
            "complete-files",
            "  ",
            "```complete-files
  
```",
        );
        assert!(parser
            .validate_raw_block(&raw_block_empty_content, &files)
            .is_ok());
    }

    #[test]
    fn test_identify_malformed_missing_keyword_looks_like_paths() {
        let parser = CompleteFilesBlockParser;
        let files: Vec<LabeledFile> = vec![];
        let raw_block = new_raw_code_block(
            "",
            "src/file.rs
README.md",
            "```
src/file.rs
README.md
```",
        );
        let malformed_info = parser.identify_malformed(&raw_block, &files);
        assert!(malformed_info.is_some());
        let info = malformed_info.unwrap();
        assert_eq!(info.parser_id, "complete_files_block");
        assert!(info
            .fix_suggestion
            .contains("missing or misspelled keyword (expected 'complete-files')"));
    }

    #[test]
    fn test_identify_malformed_correct_keyword_returns_none() {
        // Since current validation is very permissive, this should return None
        let parser = CompleteFilesBlockParser;
        let files: Vec<LabeledFile> = vec![];
        let raw_block = new_raw_code_block(
            "complete-files",
            "src/file.rs
README.md",
            "```complete-files
src/file.rs
README.md
```",
        );
        let malformed_info = parser.identify_malformed(&raw_block, &files);
        assert!(malformed_info.is_none());
    }
}
