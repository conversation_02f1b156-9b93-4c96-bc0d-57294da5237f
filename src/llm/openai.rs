use async_trait::async_trait;
use futures::StreamExt;
use reqwest::{header, Client, StatusCode};
use serde::{Deserialize, Serialize};
use thiserror::Error;

use super::{ChatMessage, ChatRole, LLMClient}; // Use ChatMessage and ChatRole from parent module, MessageType removed

// --- Structs for OpenAI API Request ---
#[derive(Serialize, Debug)]
struct OpenAIMessage {
    role: String,
    content: String,
}

impl From<&ChatMessage> for OpenAIMessage {
    fn from(msg: &ChatMessage) -> Self {
        let role_str = match msg.role {
            ChatRole::User => "user".to_string(),
            ChatRole::Assistant => "assistant".to_string(),
            ChatRole::System => "system".to_string(),
        };
        OpenAIMessage {
            role: role_str,
            content: msg.content.clone(),
        }
    }
}

#[derive(Serialize, Debug)]
struct ChatCompletionRequest {
    model: String,
    messages: Vec<OpenAIMessage>,
    stream: bool,
    #[serde(skip_serializing_if = "Option::is_none")]
    cache_prompt: Option<bool>,
}

// --- Structs for OpenAI API Streaming Response ---
#[derive(Deserialize, Debug)]
struct ChatCompletionChunkDelta {
    content: Option<String>,
}

#[derive(Deserialize, Debug)]
struct ChatCompletionChunkChoice {
    delta: ChatCompletionChunkDelta,
    // finish_reason: Option<String>,
}

#[derive(Deserialize, Debug)]
struct ChatCompletionChunk {
    choices: Vec<ChatCompletionChunkChoice>,
}

// #[derive(Deserialize, Debug)]
// struct Usage {
//     prompt_tokens: u32,
//     completion_tokens: u32,
//     total_tokens: u32,
// }

#[derive(Error, Debug)]
pub enum OpenAIClientError {
    #[error("Reqwest error: {0}")]
    ReqwestError(#[from] reqwest::Error),
    #[error("JSON deserialization error: {0}")]
    SerdeError(#[from] serde_json::Error),
    #[error("OpenAI API error: Status {status}, Body: {body}")]
    APIError { status: StatusCode, body: String },
}

pub struct OpenAIClient {
    api_key: String,
    base_url: String,
    model: String,
    http_client: Client,
}

impl OpenAIClient {
    pub fn new(api_key: String, base_url: String, model: String) -> Self {
        let effective_base_url: String;

        if base_url.is_empty() {
            // Default to OpenAI's standard v1 endpoint
            effective_base_url = "https://api.openai.com/v1/".to_string();
        } else {
            // base_url is not empty and AppConfig ensures it ends with a '/'
            if base_url.ends_with("v1/") {
                // Already correctly formatted, e.g., "http://custom_host/api/v1/"
                effective_base_url = base_url;
            } else {
                // Ends with '/', but not 'v1/', e.g., "http://custom_host/api/"
                // We need to make it "http://custom_host/api/v1/"
                let mut temp_url = base_url; // Clone to modify
                temp_url.pop(); // Remove the existing trailing slash, e.g., "http://custom_host/api"
                effective_base_url = format!("{}/v1/", temp_url); // Append "/v1/"
            }
        }

        // Explicitly build a client that does not use connection pooling and forces HTTP/1.1.
        // This, combined with sending a `Connection: close` header on each request,
        // helps ensure that when a request is cancelled, the underlying TCP
        // connection is dropped, signaling the server to stop work. This is important
        // for compatibility with various OpenAI-compatible servers that might not
        // correctly handle HTTP/2 stream cancellations or keep processing requests
        // for disconnected clients.
        let http_client = Client::builder()
            .pool_max_idle_per_host(0) // Disables connection pooling.
            .http1_only()
            .build()
            .expect("Failed to build reqwest::Client");
        OpenAIClient {
            api_key,
            base_url: effective_base_url,
            model,
            http_client,
        }
    }
}

#[async_trait]
impl LLMClient for OpenAIClient {
    async fn chat(
        &self,
        messages: &[ChatMessage],
    ) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        let api_messages: Vec<OpenAIMessage> = messages.iter().map(OpenAIMessage::from).collect();

        let request_payload = ChatCompletionRequest {
            model: self.model.clone(),
            messages: api_messages,
            stream: true,
            cache_prompt: Some(true),
        };

        let request_url = format!("{}chat/completions", self.base_url);

        let response = self
            .http_client
            .post(&request_url)
            .bearer_auth(&self.api_key)
            .header(header::CONNECTION, "close")
            .json(&request_payload)
            .send()
            .await
            .map_err(OpenAIClientError::ReqwestError)?;

        if !response.status().is_success() {
            let status = response.status();
            let body = response
                .text()
                .await
                .unwrap_or_else(|_| "Failed to read error body".to_string());
            return Err(Box::new(OpenAIClientError::APIError { status, body }));
        }

        let mut stream = response.bytes_stream();
        let mut full_content = String::new();
        let mut buffer = Vec::new();

        while let Some(item) = stream.next().await {
            let chunk = item.map_err(OpenAIClientError::ReqwestError)?;
            buffer.extend_from_slice(&chunk);

            // Process all complete lines in the buffer
            while let Some(i) = buffer.iter().position(|&b| b == b'\n') {
                let line_bytes = buffer.drain(..=i).collect::<Vec<u8>>();
                let line = String::from_utf8_lossy(&line_bytes);

                if line.starts_with("data: ") {
                    let data = line.strip_prefix("data: ").unwrap().trim();
                    if data == "[DONE]" {
                        return Ok(full_content);
                    }
                    if data.is_empty() {
                        continue;
                    }

                    match serde_json::from_str::<ChatCompletionChunk>(data) {
                        Ok(chunk_data) => {
                            if let Some(choice) = chunk_data.choices.into_iter().next() {
                                if let Some(content_part) = choice.delta.content {
                                    full_content.push_str(&content_part);
                                }
                            }
                        }
                        Err(e) => {
                            // Can happen with malformed JSON from server.
                            // We will log it and continue, which is robust.
                            log::info!(
                                "lledit: Failed to parse OpenAI stream chunk JSON: '{}', error: {}",
                                data, e
                            );
                        }
                    }
                }
            }
        }

        Ok(full_content)
    }

    fn get_model_name(&self) -> &str {
        &self.model
    }
}
