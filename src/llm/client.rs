// Use our new LLMClient trait and ChatMessage
use crate::llm::{ChatMessage, ChatRole, LLMClient, MessageType};
use thiserror::Error;

#[derive(Error, Debug)]
pub enum LLMClientError {
    #[allow(dead_code)] // This variant is currently not constructed by existing code.
    #[error("Failed to parse LLM response: {0}")]
    ParsingError(String),

    #[error("Regex compilation error: {0}")]
    RegexError(#[from] regex::Error),

    #[error("I/O error: {0}")]
    IoError(#[from] std::io::Error),

    // This will now wrap the error from our custom LLMClient trait
    #[error("LLM client error: {0}")]
    ClientError(Box<dyn std::error::Error + Send + Sync>),

    #[error("LLM chat response was empty or malformed.")]
    // This might be redundant if client handles it
    EmptyOrMalformedChatResponse,
}

/// Sends a list of chat messages to the LLM and returns the text response.
pub async fn chat_inference(
    llm_instance: &dyn LLMClient,
    messages: &[ChatMessage],
) -> Result<String, LLMClientError> {
    // Debug file writing logic has been removed.
    // Processed messages logic related to no_think has been removed.

    // TODO: Consider making parameters like max_tokens, temperature configurable if needed.
    // These would be passed to the specific LLMClient implementation.

    match llm_instance.chat(messages).await {
        Ok(text_content) => {
            if text_content.is_empty() {
                // The client itself should ideally ensure non-empty or return specific error
                Err(LLMClientError::EmptyOrMalformedChatResponse)
            } else {
                Ok(text_content)
            }
        }
        Err(e) => Err(LLMClientError::ClientError(e)), // Wrap the error from the LLMClient trait
    }
}

/// Performs a temporal chat inference without adding the temporary message or response to history.
///
/// This function sends a new temporary user message along with existing history to the LLM
/// and returns the LLM's response. Neither the temporary message nor its response
/// are permanently recorded.
pub async fn temporal_chat_inference(
    llm_instance: &dyn LLMClient,
    existing_messages: &[ChatMessage],
    temporal_user_message_content: &str,
) -> Result<String, LLMClientError> {
    let mut temp_messages = existing_messages.to_vec();

    // The caller is now responsible for prepending /no_think to temporal_user_message_content if needed.
    // The temporal_user_message_content itself might already contain /no_think if the caller decided so.
    temp_messages.push(ChatMessage {
        role: ChatRole::User, // The temporal message is a new instruction from the "user"
        content: temporal_user_message_content.to_string(),
        message_type: MessageType::Text,
    });

    // no_think processing logic removed from here.
    // The `messages` argument to `llm_instance.chat` will be `temp_messages` directly.

    match llm_instance.chat(&temp_messages).await {
        Ok(text_content) => {
            if text_content.is_empty() {
                Err(LLMClientError::EmptyOrMalformedChatResponse)
            } else {
                Ok(text_content)
            }
        }
        Err(e) => Err(LLMClientError::ClientError(e)),
    }
}
