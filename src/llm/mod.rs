pub mod client;
pub mod genai_adapter;
pub mod no_think;
pub mod openai; // Re-add openai module

pub use no_think::apply_prefix_based_on_mode;
use serde::{Deserialize, Serialize};

// Define our own ChatMessage and ChatRole, which will be converted to genai's types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ChatRole {
    User,
    Assistant,
    System,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub enum MessageType {
    Text,
    // Potentially other types if we expand beyond text, but genai handles content types within its own ChatMessageContent
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct ChatMessage {
    pub role: ChatRole,
    pub content: String,
    #[serde(default = "default_message_type")]
    // Ensure existing structs without this deserialize correctly
    pub message_type: MessageType,
}

// Helper function for serde default
fn default_message_type() -> MessageType {
    MessageType::Text
}

impl ChatMessage {
    // Helper to convert to gena<PERSON>'s ChatMessage
    // pub(crate) fn to_genai(&self) -> genai::chat::ChatMessage { // Method removed
    // ... implementation removed ...
    // }
}

#[async_trait::async_trait]
pub trait LLMClient: Send + Sync {
    async fn chat(
        &self,
        messages: &[ChatMessage],
    ) -> Result<String, Box<dyn std::error::Error + Send + Sync>>;

    fn get_model_name(&self) -> &str;
}