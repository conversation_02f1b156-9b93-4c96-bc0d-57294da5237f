use async_trait::async_trait;
use genai::chat::{
    ChatMessage as GenAIChatMessage, ChatRequest, ChatRole as GenAIChatRole, MessageContent,
};
use genai::Client as GenAIPlatformClient;
// AdapterKind might still be needed if we want to be very specific for genai's OpenAI provider
// use genai::adapter::AdapterKind;
// ServiceTargetResolver related imports are no longer needed here as CustomOpenAICompatibleResolver is removed.
// use genai::resolver::{AuthData, ServiceTargetResolverAsyncFn, ServiceTargetResolver as ServiceTargetResolverEnum};
use crate::llm::{ChatMessage, ChatRole as InternalChatRole, LLMClient};
use genai::Error as GenAIError; // ModelIden and ServiceTarget are not directly used by GenAIAdapter anymore
use std::env;
// Arc, Future, Pin are not needed without the custom resolver
// use std::sync::Arc;
// use std::future::Future;
// use std::pin::Pin;
use log::{info, warn};
use thiserror::Error; // debug might not be needed if resolver is gone
use url::Url; // Added for URL parsing

// CustomOpenAICompatibleResolver and its implementation are removed.

#[derive(Error, Debug)]
pub enum GenAIAdapterError {
    #[error("GenAI client error: {0}")]
    ClientError(#[from] GenAIError),
    #[error("No content in GenAI response message.")]
    NoContentInResponse,
    #[error("API key not configured for provider: {0}")]
    #[allow(dead_code)]
    ApiKeyNotConfigured(String),
    #[error("Unsupported provider for environment variable setup: {0}")]
    #[allow(dead_code)]
    UnsupportedProviderForEnvSetup(String),
}

pub struct GenAIAdapter {
    client: GenAIPlatformClient,
    model_id: String,
}

impl GenAIAdapter {
    fn set_api_key_env_var(provider_name: &str, api_key: Option<String>) {
        if let Some(key_val) = api_key {
            let provider_lower = provider_name.to_lowercase();
            let provider_upper = provider_name.to_uppercase();

            let env_key_name = if provider_lower == "openai" {
                "OPENAI_API_KEY".to_string()
            } else if provider_lower == "ollama" {
                // Ollama doesn't typically use an API key set via this kind of env var.
                // GenAI's Ollama support primarily relies on OLLAMA_HOST.
                String::new()
            } else {
                // For other GenAI supported providers like Anthropic, Google.
                // GenAI might pick these up automatically if named e.g. ANTHROPIC_API_KEY.
                format!("{}_API_KEY", provider_upper)
            };

            if !env_key_name.is_empty() {
                info!(
                    "Setting environment variable {} for GenAI client for provider '{}'",
                    env_key_name, provider_name
                );
                env::set_var(env_key_name, key_val);
            }
        } else if provider_name.to_lowercase() != "ollama" {
            // Check if the relevant env var is already set, otherwise warn.
            let provider_lower = provider_name.to_lowercase();
            let provider_upper = provider_name.to_uppercase();
            let env_key_name_check = if provider_lower == "openai" {
                "OPENAI_API_KEY".to_string()
            } else {
                format!("{}_API_KEY", provider_upper)
            };

            if env::var(&env_key_name_check).is_err() {
                warn!(
                    "API key for GenAI provider '{}' not provided directly and {} env var not set. GenAI client might fail if key is required and not found through other means.",
                    provider_name, env_key_name_check
                );
            }
        }
    }

    fn set_base_url_env_var(provider_name: &str, base_url: Option<String>) {
        if let Some(ref url_val) = base_url {
            // Use `ref url_val` to borrow the content
            let provider_lower = provider_name.to_lowercase();
            let mut env_base_url_name = String::new();
            let mut value_to_set = url_val.clone(); // Clone the borrowed String

            if provider_lower == "openai" {
                env_base_url_name = "OPENAI_API_BASE".to_string();
            } else if provider_lower == "ollama" {
                env_base_url_name = "OLLAMA_HOST".to_string();
                // OLLAMA_HOST expects just hostname:port, not full http scheme or path
                if let Ok(parsed_url) = Url::parse(&value_to_set) {
                    // Changed url::Url to Url
                    if let Some(host_str) = parsed_url.host_str() {
                        let port = parsed_url.port().unwrap_or(11434); // Default Ollama port
                        value_to_set = format!("{}:{}", host_str, port);
                    } else {
                        warn!("Could not parse host from provided Ollama URL: {}. Using as is for OLLAMA_HOST.", value_to_set);
                    }
                } else if value_to_set.contains('/') || value_to_set.starts_with("http") {
                    // If not a full URL but looks like it might have scheme or path, try to strip
                    value_to_set = value_to_set
                        .trim_start_matches("http://")
                        .trim_start_matches("https://")
                        .split('/')
                        .next()
                        .unwrap_or(&value_to_set)
                        .to_string();
                    warn!("Provided Ollama URL '{}' was heuristically processed to '{}' for OLLAMA_HOST. Ensure it is in 'hostname:port' format.", base_url.as_ref().unwrap_or(&String::new()), value_to_set);
                }
                // If it's already "localhost:11434", it will be used as is.
            }
            // Add other genai-supported provider specific base URL env vars here if needed.
            // e.g., ANTHROPIC_API_BASE, GOOGLE_API_BASE, etc. if genai supports them.

            if !env_base_url_name.is_empty() {
                info!(
                    "Setting environment variable {} to '{}' for GenAI client for provider '{}'",
                    env_base_url_name, value_to_set, provider_name
                );
                env::set_var(env_base_url_name, value_to_set);
            } else {
                warn!(
                    "Base URL provided for GenAI provider '{}', but a specific environment variable for its base URL is not explicitly handled by this setup (only 'openai' and 'ollama' have specific base URL env var logic). GenAI client will use its default endpoint resolution or other config methods if applicable.",
                    provider_name
                );
            }
        }
    }

    pub fn new(
        api_key: Option<String>,
        base_url: Option<String>,
        model_id: String,
        provider_name: &str, // e.g., "openai", "anthropic", "ollama"
    ) -> Result<Self, GenAIAdapterError> {
        info!(
            "Initializing GenAIAdapter for provider '{}' with model '{}'",
            provider_name, model_id
        );

        // This adapter is for genai-native providers.
        // "openai-compatible" and "openrouter" are handled by OpenAIClient.
        if provider_name == "openai-compatible" || provider_name == "openrouter" {
            // This case should ideally not be reached if AppConfig.setup_llm_client directs correctly.
            warn!("GenAIAdapter received provider '{}', which should be handled by OpenAIClient. Proceeding, but this might indicate a misconfiguration.", provider_name);
        }

        Self::set_api_key_env_var(provider_name, api_key);
        Self::set_base_url_env_var(provider_name, base_url);

        let client = GenAIPlatformClient::default();
        Ok(Self { client, model_id })
    }

    fn convert_messages(messages: &[ChatMessage]) -> Vec<GenAIChatMessage> {
        messages
            .iter()
            .map(|msg| {
                let role = match msg.role {
                    InternalChatRole::User => GenAIChatRole::User,
                    InternalChatRole::Assistant => GenAIChatRole::Assistant,
                    InternalChatRole::System => GenAIChatRole::System,
                };
                // Constructing GenAIChatMessage directly.
                GenAIChatMessage {
                    role,
                    content: MessageContent::Text(msg.content.clone()), // Use MessageContent
                    // name is not a field in genai 0.3.5 ChatMessage.
                    // options is a field.
                    options: None,
                }
            })
            .collect()
    }
}

#[async_trait]
impl LLMClient for GenAIAdapter {
    async fn chat(
        &self,
        messages: &[ChatMessage],
    ) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        let genai_messages = Self::convert_messages(messages);
        let chat_request = ChatRequest::new(genai_messages);
        // TODO: Expose ChatOptions (temperature, max_tokens) if needed, pass as Some(&options)
        let chat_options = None;

        let response = self
            .client
            .exec_chat(&self.model_id, chat_request, chat_options)
            .await
            .map_err(|e| Box::new(GenAIAdapterError::ClientError(e)))?;

        // Extract text content. genai might have multiple parts or types of content.
        // We are interested in the primary text response.
        // Use content_text_as_str() as suggested by the compiler.
        if let Some(text_content) = response.content_text_as_str() {
            Ok(text_content.to_string()) // content_text_as_str returns &str, so convert to String
        } else {
            // Check if there are other parts, e.g. for tool use, though we don't support that yet.
            // For now, assume if content_text_as_str() is None, it's an issue for simple chat.
            Err(Box::new(GenAIAdapterError::NoContentInResponse))
        }
    }
    fn get_model_name(&self) -> &str {
        &self.model_id
    }
}