use clap::Parser;
use serde_json::to_string_pretty;
// ResultHandler and LLMClient are no longer directly used in main.rs
use crate::api::types::APIMessage;
use crate::task::Task; // Task is used by AppConfig loading
use std::collections::VecDeque;
use std::net::TcpListener;
use tokio::sync::mpsc;

mod api;
mod block_parsing;
mod cli;
use cli::Args;
mod config;
mod editor;
mod expert;
// mod file_handler; // Removed top-level module
mod files;
mod history_pruner;
mod http_server;
mod interactive;
mod language_integrations;
mod llm;
mod logger;
mod notifications;
mod prompt_builder;
pub mod research;
mod result;
mod task;

// log::{debug, info} and colored::* are not directly used in main's scope after refactor.
// Logging happens within called functions or interactive runner.

/// Finds an available port, starting from the preferred port and incrementing.
fn find_available_port(preferred_port: u16) -> u16 {
    let mut port = preferred_port;
    while let Err(_) = TcpListener::bind(format!("127.0.0.1:{}", port)) {
        port += 1;
    }
    port
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let args = Args::parse();

    // Load YAML configuration file (or create default)
    let file_config = match config::load_or_create_config() {
        Ok(fc) => fc,
        Err(e) => {
            eprintln!("Critical error loading configuration: {}. Exiting.", e);
            return Err(e.into());
        }
    };

    // Create the final AppConfig by merging Args and FileConfig
    let mut app_config = config::AppConfig::new(args, file_config);

    // Initialize logger using the determined log level and timestamp setting from AppConfig
    logger::init_logger(app_config.log_level, app_config.timestamps); // interactive flag removed

    // Validate arguments
    if app_config.user_prompt.is_empty() {
        // Prompt is optional if starting fresh, but good to note if it's missing.
        // The interactive mode will allow user to input it.
        // No error exit here, as interactive mode handles this.
    }

    let (api_sender, mut api_receiver) = mpsc::channel::<APIMessage>(100);
    let mut api_message_queue: VecDeque<APIMessage> = VecDeque::new();

    // Find an available port and update the config
    let available_port = find_available_port(app_config.http_api_port);
    app_config.http_api_port = available_port;

    http_server::start_server(app_config.http_api_port, api_sender);

    // Always run in interactive mode.
    let (result_data, last_command_type) = interactive::run_interactive_mode(
        &mut app_config,
        &mut api_receiver,
        &mut api_message_queue,
    )
    .await?;

    // Output results if requested, after TUI has closed.
    if let Some(filepath) = &app_config.results_output_file {
        // If output file is specified, always write advanced JSON to it.
        let json_result = to_string_pretty(&result_data.to_json())?;
        std::fs::write(filepath, json_result)?;
        eprintln!("Results written to: {}", filepath.display());
    } else {
        // No output file, check mode for stdout.
        match app_config.results_output_mode {
            config::app_config::ResultsOutputMode::Advanced => {
                let json_result = to_string_pretty(&result_data.to_json())?;
                println!("{}", json_result);
            }
            config::app_config::ResultsOutputMode::Simple => {
                let simple_output = result_data.to_simple_output_string(last_command_type);
                println!("{}", simple_output);
            }
            config::app_config::ResultsOutputMode::None => {
                // No output to stdout
            }
        }
    }

    Ok(())
}
