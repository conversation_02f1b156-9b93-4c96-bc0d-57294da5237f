use crate::config::app_config::{
    AppConfig, AutoExpertMode, AutoExpertSwitch, AutoResearchMode, NoThinkMode, ResultsOutputMode,
};
use crate::files::ordered_files::OrderedFiles;
use crate::result::SerializableTask;
use crate::task::Task;
use log::{debug, info, warn};
use serde::{Deserialize, Serialize};
use std::collections::HashSet;
use std::env;
use std::path::PathBuf;

pub const PREVIOUS_SESSION_FILENAME: &str = ".lledit.previous_session";

#[derive(Serialize, Deserialize, Debug, Default, Clone)]
#[serde(default)]
struct RestorableAppConfig {
    default_model: Option<String>,
    task_info: Option<String>,
    no_think: Option<HashSet<NoThinkMode>>,
    results_output_mode: Option<ResultsOutputMode>,
    results_output_file: Option<PathBuf>,
    exit_on_success: Option<bool>,
    timestamps: Option<bool>,
    auto_test_command: Option<String>,
    auto_test_toggle: Option<bool>,
    max_task_retries: Option<usize>,
    auto_research_mode: Option<AutoResearchMode>,
    auto_expert_switch: Option<AutoExpertSwitch>,
    expert_model: Option<String>,
    auto_expert_mode: Option<AutoExpertMode>,
    expert_model_auto_research_loop_max: Option<usize>,
    notification_command: Option<String>,
    decision_model_auto_research_loop_max: Option<usize>,
    retry_model_max_attempts: Option<usize>,
    retry_model: Option<String>,
    summary_model: Option<String>,
    research_model: Option<String>,
    decision_model: Option<String>,
    http_api_port: Option<u16>,
    advanced_language_features: Option<HashSet<String>>,
    restore_previous_session_models: Option<bool>,
}

#[derive(Serialize, Deserialize, Debug, Default)]
#[serde(default)]
pub struct SessionState {
    config: RestorableAppConfig,
    tasks: Vec<SerializableTask>,
    file_paths: Vec<PathBuf>,
    #[serde(default)]
    input_history: Vec<String>,
}

pub async fn save_session(
    app_config: &AppConfig,
    tasks: &Vec<Task>,
    ordered_files: &OrderedFiles,
    input_history: &Vec<String>,
) -> Result<(), Box<dyn std::error::Error>> {
    if !app_config.restore_previous_session_on_startup {
        return Ok(());
    }

    let session_file_path = env::current_dir()?.join(PREVIOUS_SESSION_FILENAME);

    let restorable_config = RestorableAppConfig {
        default_model: Some(app_config.default_model.clone()),
        task_info: app_config.task_info.clone(),
        no_think: Some(app_config.no_think.clone()),
        results_output_mode: Some(app_config.results_output_mode),
        results_output_file: app_config.results_output_file.clone(),
        exit_on_success: Some(app_config.exit_on_success),
        timestamps: Some(app_config.timestamps),
        auto_test_command: Some(app_config.auto_test_command.clone()),
        auto_test_toggle: Some(app_config.auto_test_toggle),
        max_task_retries: Some(app_config.max_task_retries),
        auto_research_mode: Some(app_config.auto_research_mode),
        auto_expert_switch: Some(app_config.auto_expert_switch),
        expert_model: app_config.expert_model.clone(),
        auto_expert_mode: Some(app_config.auto_expert_mode),
        expert_model_auto_research_loop_max: Some(app_config.expert_model_auto_research_loop_max),
        notification_command: Some(app_config.notification_command.clone()),
        decision_model_auto_research_loop_max: Some(
            app_config.decision_model_auto_research_loop_max,
        ),
        retry_model: app_config.retry_model.clone(),
        retry_model_max_attempts: Some(app_config.retry_model_max_attempts),
        summary_model: app_config.summary_model.clone(),
        research_model: app_config.research_model.clone(),
        decision_model: app_config.decision_model.clone(),
        http_api_port: Some(app_config.http_api_port),
        advanced_language_features: Some(app_config.advanced_language_features.clone()),
        restore_previous_session_models: Some(app_config.restore_previous_session_models),
    };

    let serializable_tasks = tasks.iter().map(SerializableTask::from).collect();

    let session_state = SessionState {
        config: restorable_config,
        tasks: serializable_tasks,
        file_paths: ordered_files.get_all_labeled_files_map().keys().cloned().collect(),
        input_history: input_history.clone(),
    };

    let json_data = serde_json::to_string_pretty(&session_state)?;
    tokio::fs::write(&session_file_path, json_data).await?;
    info!("Session state saved to {}", session_file_path.display());

    Ok(())
}

pub async fn load_and_apply_session(
    app_config: &mut AppConfig,
    tasks: &mut Vec<Task>,
    ordered_files: &mut OrderedFiles,
    input_history: &mut Vec<String>,
) -> Result<(), Box<dyn std::error::Error>> {
    if !app_config.restore_previous_session_on_startup {
        return Ok(());
    }

    let session_file_path = env::current_dir()?.join(PREVIOUS_SESSION_FILENAME);

    if !session_file_path.exists() {
        debug!("No previous session file found. Starting fresh.");
        return Ok(());
    }

    let content = tokio::fs::read_to_string(&session_file_path).await?;
    let session_state: SessionState = match serde_json::from_str(&content) {
        Ok(state) => state,
        Err(e) => {
            warn!(
                "Failed to parse previous session file: {}. Starting fresh.",
                e
            );
            return Ok(());
        }
    };

    info!("Restoring state from previous session.");

    // Apply config
    let config = session_state.config;

    // Check if we should restore models from the session
    let should_restore_models = config.restore_previous_session_models.unwrap_or(false);

    if should_restore_models {
        if let Some(val) = config.default_model {
            app_config.default_model = val;
        }
    }
    if config.task_info.is_some() {
        app_config.task_info = config.task_info;
    }
    if let Some(val) = config.no_think {
        app_config.no_think = val;
    }
    if let Some(val) = config.results_output_mode {
        app_config.results_output_mode = val;
    }
    if config.results_output_file.is_some() {
        app_config.results_output_file = config.results_output_file;
    }
    if let Some(val) = config.exit_on_success {
        app_config.exit_on_success = val;
    }
    if let Some(val) = config.timestamps {
        app_config.timestamps = val;
    }
    if let Some(val) = config.auto_test_command {
        app_config.auto_test_command = val;
    }
    if let Some(val) = config.auto_test_toggle {
        app_config.auto_test_toggle = val;
    }
    if let Some(val) = config.max_task_retries {
        app_config.max_task_retries = val;
    }
    if let Some(val) = config.auto_research_mode {
        app_config.auto_research_mode = val;
    }
    if let Some(val) = config.auto_expert_switch {
        app_config.auto_expert_switch = val;
    }
    if should_restore_models && config.expert_model.is_some() {
        app_config.expert_model = config.expert_model;
    }
    if let Some(val) = config.auto_expert_mode {
        app_config.auto_expert_mode = val;
    }
    if let Some(val) = config.expert_model_auto_research_loop_max {
        app_config.expert_model_auto_research_loop_max = val;
    }
    if let Some(val) = config.notification_command {
        app_config.notification_command = val;
    }
    if let Some(val) = config.decision_model_auto_research_loop_max {
        app_config.decision_model_auto_research_loop_max = val;
    }
    if should_restore_models && config.retry_model.is_some() {
        app_config.retry_model = config.retry_model;
    }
    if let Some(val) = config.retry_model_max_attempts {
        app_config.retry_model_max_attempts = val;
    }
    if should_restore_models && config.summary_model.is_some() {
        app_config.summary_model = config.summary_model;
    }
    if should_restore_models && config.research_model.is_some() {
        app_config.research_model = config.research_model;
    }
    if should_restore_models && config.decision_model.is_some() {
        app_config.decision_model = config.decision_model;
    }
    if let Some(val) = config.http_api_port {
        app_config.http_api_port = val;
    }
    if let Some(val) = config.advanced_language_features {
        app_config.advanced_language_features = val;
    }
    if let Some(val) = config.restore_previous_session_models {
        app_config.restore_previous_session_models = val;
    }

    // Apply tasks
    *tasks = session_state.tasks.into_iter().map(Task::from).collect();

    // Apply input history
    *input_history = session_state.input_history;

    // Apply file paths to OrderedFiles
    // Apply file paths to OrderedFiles
    *ordered_files = OrderedFiles::new();
    for path in session_state.file_paths {
        if let Err(e) = ordered_files.add_user_file(&path).await {
            warn!(
                "Failed to restore file from session {}: {}",
                path.display(),
                e
            );
        }
    }

    // Remove the session file after successful load to prevent accidental reloads on restart
    if let Err(e) = tokio::fs::remove_file(&session_file_path).await {
        warn!("Failed to remove session file after loading: {}", e);
    }

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::app_config::{AutoExpertMode, AutoExpertSwitch, AutoResearchMode, ResultsOutputMode};
    use crate::logger::LogLevel;
    use std::collections::HashSet;
    use tokio;

    fn create_test_app_config() -> AppConfig {
        AppConfig {
            user_prompt: "test".to_string(),
            file_paths: Vec::new(),
            default_model: "test-model".to_string(),
            provider: "test-provider".to_string(),
            model: "test-model".to_string(),
            effective_provider_api_key: "test-key".to_string(),
            provider_url: "test-url".to_string(),
            models_list: None,
            results_input_file: None,
            results_input: None,
            task_info: None,
            no_think: HashSet::new(),
            results_output_mode: ResultsOutputMode::None,
            results_output_file: None,
            log_level: LogLevel::Info,
            exit_on_success: false,
            timestamps: false,
            restore_previous_session_on_startup: true,
            restore_previous_session_models: false, // This is the key setting we're testing
            auto_test_command: "test".to_string(),
            auto_test_toggle: false,
            max_task_retries: 3,
            auto_research_mode: AutoResearchMode::False,
            auto_expert_switch: AutoExpertSwitch::False,
            expert_model: Some("expert-model".to_string()),
            auto_expert_mode: AutoExpertMode::Planning,
            expert_model_auto_research_loop_max: 2,
            notification_command: "test".to_string(),
            forced_research_for_next_cycle: None,
            decision_model_auto_research_loop_max: 2,
            retry_model: Some("retry-model".to_string()),
            retry_model_max_attempts: 3,
            summary_model: Some("summary-model".to_string()),
            research_model: Some("research-model".to_string()),
            decision_model: Some("decision-model".to_string()),
            http_api_port: 8080,
            advanced_language_features: HashSet::new(),
        }
    }

    #[tokio::test]
    async fn test_restore_previous_session_models_false_skips_model_restoration() {
        // Create a test that doesn't rely on changing the global current directory
        // Instead, we'll create a session file in a temp directory and test the logic directly

        // Create initial config with models set and restore_previous_session_models = false
        let mut initial_config = create_test_app_config();
        initial_config.restore_previous_session_models = false;

        // Create session state manually
        let session_state = SessionState {
            config: RestorableAppConfig {
                default_model: Some(initial_config.default_model.clone()),
                task_info: initial_config.task_info.clone(),
                no_think: Some(initial_config.no_think.clone()),
                results_output_mode: Some(initial_config.results_output_mode.clone()),
                results_output_file: initial_config.results_output_file.clone(),
                exit_on_success: Some(initial_config.exit_on_success),
                timestamps: Some(initial_config.timestamps),
                auto_test_command: Some(initial_config.auto_test_command.clone()),
                auto_test_toggle: Some(initial_config.auto_test_toggle),
                max_task_retries: Some(initial_config.max_task_retries),
                auto_research_mode: Some(initial_config.auto_research_mode.clone()),
                auto_expert_switch: Some(initial_config.auto_expert_switch.clone()),
                expert_model: initial_config.expert_model.clone(),
                auto_expert_mode: Some(initial_config.auto_expert_mode.clone()),
                expert_model_auto_research_loop_max: Some(initial_config.expert_model_auto_research_loop_max),
                notification_command: Some(initial_config.notification_command.clone()),
                decision_model_auto_research_loop_max: Some(initial_config.decision_model_auto_research_loop_max),
                retry_model: initial_config.retry_model.clone(),
                retry_model_max_attempts: Some(initial_config.retry_model_max_attempts),
                summary_model: initial_config.summary_model.clone(),
                research_model: initial_config.research_model.clone(),
                decision_model: initial_config.decision_model.clone(),
                http_api_port: Some(initial_config.http_api_port),
                advanced_language_features: Some(initial_config.advanced_language_features.clone()),
                restore_previous_session_models: Some(initial_config.restore_previous_session_models),
            },
            tasks: Vec::new(),
            file_paths: Vec::new(),
            input_history: Vec::new(),
        };

        // Create a new config with different model values
        let mut new_config = create_test_app_config();
        new_config.default_model = "different-model".to_string();
        new_config.expert_model = Some("different-expert".to_string());
        new_config.retry_model = Some("different-retry".to_string());
        new_config.summary_model = Some("different-summary".to_string());
        new_config.research_model = Some("different-research".to_string());
        new_config.decision_model = Some("different-decision".to_string());
        new_config.restore_previous_session_models = true; // Start with true, should be overridden by session

        // Apply the session state directly (simulating what load_and_apply_session does)
        let config = session_state.config;
        let should_restore_models = config.restore_previous_session_models.unwrap_or(false);

        // Apply the session restoration logic
        if should_restore_models {
            if let Some(val) = config.default_model {
                new_config.default_model = val;
            }
        }
        if should_restore_models && config.expert_model.is_some() {
            new_config.expert_model = config.expert_model;
        }
        if should_restore_models && config.retry_model.is_some() {
            new_config.retry_model = config.retry_model;
        }
        if should_restore_models && config.summary_model.is_some() {
            new_config.summary_model = config.summary_model;
        }
        if should_restore_models && config.research_model.is_some() {
            new_config.research_model = config.research_model;
        }
        if should_restore_models && config.decision_model.is_some() {
            new_config.decision_model = config.decision_model;
        }

        // Apply the flag itself
        if let Some(val) = config.restore_previous_session_models {
            new_config.restore_previous_session_models = val;
        }

        // Verify that models were NOT restored (should keep the "different-*" values)
        assert_eq!(new_config.default_model, "different-model");
        assert_eq!(new_config.expert_model, Some("different-expert".to_string()));
        assert_eq!(new_config.retry_model, Some("different-retry".to_string()));
        assert_eq!(new_config.summary_model, Some("different-summary".to_string()));
        assert_eq!(new_config.research_model, Some("different-research".to_string()));
        assert_eq!(new_config.decision_model, Some("different-decision".to_string()));

        // Verify that the flag itself was restored from the session (should be false)
        assert_eq!(new_config.restore_previous_session_models, false);
    }

    #[tokio::test]
    async fn test_restore_previous_session_models_true_restores_models() {
        // Create a test that doesn't rely on changing the global current directory
        // Instead, we'll test the logic directly

        // Create initial config with models set and flag enabled
        let mut initial_config = create_test_app_config();
        initial_config.restore_previous_session_models = true;

        // Create session state manually
        let session_state = SessionState {
            config: RestorableAppConfig {
                default_model: Some(initial_config.default_model.clone()),
                task_info: initial_config.task_info.clone(),
                no_think: Some(initial_config.no_think.clone()),
                results_output_mode: Some(initial_config.results_output_mode.clone()),
                results_output_file: initial_config.results_output_file.clone(),
                exit_on_success: Some(initial_config.exit_on_success),
                timestamps: Some(initial_config.timestamps),
                auto_test_command: Some(initial_config.auto_test_command.clone()),
                auto_test_toggle: Some(initial_config.auto_test_toggle),
                max_task_retries: Some(initial_config.max_task_retries),
                auto_research_mode: Some(initial_config.auto_research_mode.clone()),
                auto_expert_switch: Some(initial_config.auto_expert_switch.clone()),
                expert_model: initial_config.expert_model.clone(),
                auto_expert_mode: Some(initial_config.auto_expert_mode.clone()),
                expert_model_auto_research_loop_max: Some(initial_config.expert_model_auto_research_loop_max),
                notification_command: Some(initial_config.notification_command.clone()),
                decision_model_auto_research_loop_max: Some(initial_config.decision_model_auto_research_loop_max),
                retry_model: initial_config.retry_model.clone(),
                retry_model_max_attempts: Some(initial_config.retry_model_max_attempts),
                summary_model: initial_config.summary_model.clone(),
                research_model: initial_config.research_model.clone(),
                decision_model: initial_config.decision_model.clone(),
                http_api_port: Some(initial_config.http_api_port),
                advanced_language_features: Some(initial_config.advanced_language_features.clone()),
                restore_previous_session_models: Some(initial_config.restore_previous_session_models),
            },
            tasks: Vec::new(),
            file_paths: Vec::new(),
            input_history: Vec::new(),
        };

        // Create a new config with different model values
        let mut new_config = create_test_app_config();
        new_config.default_model = "different-model".to_string();
        new_config.expert_model = Some("different-expert".to_string());
        new_config.retry_model = Some("different-retry".to_string());
        new_config.summary_model = Some("different-summary".to_string());
        new_config.research_model = Some("different-research".to_string());
        new_config.decision_model = Some("different-decision".to_string());
        new_config.restore_previous_session_models = false; // Start with false, should be overridden by session

        // Apply the session state directly (simulating what load_and_apply_session does)
        let config = session_state.config;
        let should_restore_models = config.restore_previous_session_models.unwrap_or(false);

        // Apply the session restoration logic
        if should_restore_models {
            if let Some(val) = config.default_model {
                new_config.default_model = val;
            }
        }
        if should_restore_models && config.expert_model.is_some() {
            new_config.expert_model = config.expert_model;
        }
        if should_restore_models && config.retry_model.is_some() {
            new_config.retry_model = config.retry_model;
        }
        if should_restore_models && config.summary_model.is_some() {
            new_config.summary_model = config.summary_model;
        }
        if should_restore_models && config.research_model.is_some() {
            new_config.research_model = config.research_model;
        }
        if should_restore_models && config.decision_model.is_some() {
            new_config.decision_model = config.decision_model;
        }

        // Apply the flag itself
        if let Some(val) = config.restore_previous_session_models {
            new_config.restore_previous_session_models = val;
        }

        // Verify that models WERE restored (should have the original "test-*" values)
        assert_eq!(new_config.default_model, "test-model");
        assert_eq!(new_config.expert_model, Some("expert-model".to_string()));
        assert_eq!(new_config.retry_model, Some("retry-model".to_string()));
        assert_eq!(new_config.summary_model, Some("summary-model".to_string()));
        assert_eq!(new_config.research_model, Some("research-model".to_string()));
        assert_eq!(new_config.decision_model, Some("decision-model".to_string()));

        // Verify that the flag itself was restored from the session (should be true)
        assert_eq!(new_config.restore_previous_session_models, true);
    }
}