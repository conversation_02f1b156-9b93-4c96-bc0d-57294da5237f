use crate::cli::Args;
use crate::config::file_config::{FileConfig, ModelConfigEntry};
use crate::llm::genai_adapter::GenAIAdapter;
use crate::llm::openai::OpenAIClient; // Add OpenAIClient
use crate::llm::LLMClient;
use crate::logger::LogLevel;
// SerializableChatMessage and SerializableTask are used by NewHistoryFileFormat, keep them.
use crate::result::{SerializableChatMessage, SerializableTask};
use crate::task::Task;
use log::{debug, error, info, warn};
use serde::{Deserialize, Serialize};
use serde_json;
use std::collections::HashSet; // Added for HashSet
use std::env;
use std::fmt;
use std::path::PathBuf;
use std::str::FromStr;
use std::sync::Arc;

// Helper structs for deserializing message history
#[derive(serde::Deserialize, Debug)]
struct OldHistoryFileFormat {
    message_history: Vec<SerializableChatMessage>,
}

#[derive(serde::Deserialize, serde::Serialize, Debug)] // Added Serialize
struct NewHistoryFileFormat {
    tasks: Vec<SerializableTask>,
}

// Enum to represent either old or new history structure
#[derive(serde::Deserialize, Debug)]
#[serde(untagged)] // Allows trying to deserialize as one variant then the other
enum AnyHistoryFileFormat {
    New(NewHistoryFileFormat),
    Old(OldHistoryFileFormat),
}

#[derive(Debug, Clone)]
pub struct AppConfig {
    // CLI-provided or default
    pub user_prompt: String,
    pub file_paths: Vec<PathBuf>,

    // Merged: CLI > YAML > Env (for some) > Default
    pub default_model: String, // The alias of the model configuration used
    pub provider: String,      // Provider from the selected model configuration
    pub model: String,         // Model name from the selected model configuration
    // pub cli_provider_api_key: Option<String>, // Removed: API key from CLI arguments
    pub effective_provider_api_key: String, // Resolved API key for the current default_model
    pub provider_url: String, // Effective URL from selected model config or defaults/env
    pub models_list: Option<Vec<ModelConfigEntry>>, // Store the list for dynamic changes

    // Merged: CLI > YAML > Default
    pub results_input_file: Option<PathBuf>, // Added
    pub results_input: Option<String>,       // Added
    pub task_info: Option<String>,
    pub no_think: HashSet<NoThinkMode>, // Changed to HashSet<NoThinkMode>
    // pub results_output: bool, // Removed
    pub results_output_mode: ResultsOutputMode, // Added
    pub results_output_file: Option<PathBuf>,
    pub log_level: LogLevel, // Enum type
    // pub interactive: bool, // Removed
    pub exit_on_success: bool, // Added
    pub timestamps: bool,
    pub restore_previous_session_on_startup: bool, // Added
    pub restore_previous_session_models: bool, // Added

    // Auto-test feature
    pub auto_test_command: String,
    pub auto_test_toggle: bool,
    pub max_task_retries: usize,

    // Auto-research feature
    pub auto_research_mode: AutoResearchMode,

    // Auto-expert feature
    pub auto_expert_switch: AutoExpertSwitch,
    pub expert_model: Option<String>, // Renamed from expert_model_alias
    pub auto_expert_mode: AutoExpertMode,
    pub expert_model_auto_research_loop_max: usize, // Added

    // Notification command
    pub notification_command: String,
    pub forced_research_for_next_cycle: Option<String>, // Added for pending research

    // General research loop settings
    pub decision_model_auto_research_loop_max: usize, // Added

    // Retry model
    pub retry_model: Option<String>, // Renamed from retry_model_alias
    pub retry_model_max_attempts: usize, // Maximum number of retry attempts for block parsing failures

    // Summary model
    pub summary_model: Option<String>, // Renamed from summary_model_alias

    // Research model
    pub research_model: Option<String>, // Renamed from research_model_alias

    // Decision model
    pub decision_model: Option<String>,

    pub http_api_port: u16,

    // Advanced Language Features
    pub advanced_language_features: HashSet<String>,

    // Sound System Configuration
    pub sounds_master_volume: u8, // 0-100, clamped to range
    pub submit_sound: SubmitSoundConfig, // Enum for different sound options
}

const DEFAULT_PROVIDER: &str = "openai-compatible";
const DEFAULT_MODEL: &str = "unknown";
const DEFAULT_LOG_LEVEL_U8: u8 = 2; // Corresponds to LogLevel::Info
const DEFAULT_RESULTS_OUTPUT_MODE: ResultsOutputMode = ResultsOutputMode::None; // Added default

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ResultsOutputMode {
    None,
    Simple,
    Advanced,
}

#[derive(Debug, Clone, PartialEq, Eq)]
pub enum SubmitSoundConfig {
    Disabled,
    SystemDefault,
    CustomPath(String),
}

impl fmt::Display for ResultsOutputMode {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ResultsOutputMode::None => write!(f, "none"),
            ResultsOutputMode::Simple => write!(f, "simple"),
            ResultsOutputMode::Advanced => write!(f, "advanced"),
        }
    }
}

impl FromStr for ResultsOutputMode {
    type Err = String;
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "none" => Ok(ResultsOutputMode::None),
            "simple" => Ok(ResultsOutputMode::Simple),
            "advanced" => Ok(ResultsOutputMode::Advanced),
            _ => Err(format!(
                "Invalid ResultsOutputMode: '{}'. Must be 'none', 'simple', or 'advanced'.",
                s
            )),
        }
    }
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum NoThinkMode {
    None,                 // Overrides all other modes
    All,                  // Applies to all contexts (unless None is present)
    EditingPlanning,      // For initial planning prompt (renamed from EditingPlan)
    EditingCoding,        // For specific code generation prompts
    ResearchAutoDecision, // For the prompt asking LLM to decide if auto-research is needed
    ResearchPlanning,     // For initial research planning prompt (renamed from Research)
    ResearchSearching,    // For subsequent prompts in a research cycle
    Ask,                  // For question/answer prompts (renamed from Question)
    Summaries,            // For prompts requesting summaries (plan summary, task summary)
    ExpertAutoDecision,   // For the prompt asking LLM to decide if expert intervention is needed
    ExpertPlanning,       // For the expert model's planning prompt
    ExpertEditing,        // For the expert model's editing prompt (if implemented)
}

// --- AutoExpertSwitch Enum ---
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AutoExpertSwitch {
    False,                // Never run auto-expert
    True,                 // LLM decides if auto-expert is needed
    Forced,               // Always run auto-expert
    First,                // LLM decides, but only on the first run of a task
    FirstForcedThenFalse, // Always run auto-expert on first run, then False on retries.
    FirstForcedThenTrue,  // Always run auto-expert on first run, then True on retries.
}

impl fmt::Display for AutoExpertSwitch {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            AutoExpertSwitch::False => write!(f, "false"),
            AutoExpertSwitch::True => write!(f, "true"),
            AutoExpertSwitch::Forced => write!(f, "forced"),
            AutoExpertSwitch::First => write!(f, "first"),
            AutoExpertSwitch::FirstForcedThenFalse => write!(f, "first-forced-then-false"),
            AutoExpertSwitch::FirstForcedThenTrue => write!(f, "first-forced-then-true"),
        }
    }
}

impl FromStr for AutoExpertSwitch {
    type Err = String;
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "false" => Ok(AutoExpertSwitch::False),
            "true" => Ok(AutoExpertSwitch::True),
            "forced" | "force" => Ok(AutoExpertSwitch::Forced),
            "first" => Ok(AutoExpertSwitch::First),
            "first-forced-then-false" => Ok(AutoExpertSwitch::FirstForcedThenFalse),
            "first-forced-then-true" => Ok(AutoExpertSwitch::FirstForcedThenTrue),
            // Legacy "first-forced" / "first-force" maps to FirstForcedThenFalse for backward compatibility
            "first-forced" | "first-force" => {
                warn!("Legacy 'first-forced' / 'first-force' for AutoExpertSwitch is deprecated. Use 'first-forced-then-false' or 'first-forced-then-true'. Defaulting to 'first-forced-then-false'.");
                Ok(AutoExpertSwitch::FirstForcedThenFalse)
            }
            _ => Err(format!(
                "Invalid AutoExpertSwitch: '{}'. Must be one of 'false', 'true', 'forced', 'force', 'first', 'first-forced-then-false', 'first-forced-then-true'.", s
            )),
        }
    }
}
// --- End AutoExpertSwitch Enum ---

// --- AutoExpertMode Enum ---
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AutoExpertMode {
    Planning,
    Editing,
}

impl fmt::Display for AutoExpertMode {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            AutoExpertMode::Planning => write!(f, "planning"),
            AutoExpertMode::Editing => write!(f, "editing"),
        }
    }
}

impl FromStr for AutoExpertMode {
    type Err = String;
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "planning" => Ok(AutoExpertMode::Planning),
            "editing" => Ok(AutoExpertMode::Editing),
            _ => Err(format!(
                "Invalid AutoExpertMode: '{}'. Must be one of 'planning', 'editing'.",
                s
            )),
        }
    }
}
// --- End AutoExpertMode Enum ---

// --- AutoResearchMode Enum ---
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AutoResearchMode {
    False,                // Never run auto-research
    True,                 // Run auto-research if no files in context, otherwise LLM decides
    Forced,               // Always run auto-research
    First,                // LLM decides, but only on the first run of a task
    FirstForcedThenFalse, // Always run auto-research on first run, then False on retries.
    FirstForcedThenTrue,  // Always run auto-research on first run, then True on retries.
    ExpertOnly, // Auto-research decision/initiation is skipped for default model; only expert model can trigger it.
    NoFiles,    // Default LLM researches only if no files in context. Expert LLM cannot research.
    ExpertAndNoFilesOnly, // Default LLM researches only if no files in context. Expert LLM can research.
}

impl fmt::Display for AutoResearchMode {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            AutoResearchMode::False => write!(f, "false"),
            AutoResearchMode::True => write!(f, "true"),
            AutoResearchMode::Forced => write!(f, "forced"),
            AutoResearchMode::First => write!(f, "first"),
            AutoResearchMode::FirstForcedThenFalse => write!(f, "first-forced-then-false"),
            AutoResearchMode::FirstForcedThenTrue => write!(f, "first-forced-then-true"),
            AutoResearchMode::ExpertOnly => write!(f, "expert-only"),
            AutoResearchMode::NoFiles => write!(f, "no-files"),
            AutoResearchMode::ExpertAndNoFilesOnly => write!(f, "expert-and-no-files-only"),
        }
    }
}

impl FromStr for AutoResearchMode {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "false" => Ok(AutoResearchMode::False),
            "true" => Ok(AutoResearchMode::True),
            "forced" | "force" => Ok(AutoResearchMode::Forced),
            "first" => Ok(AutoResearchMode::First),
            "first-forced-then-false" => Ok(AutoResearchMode::FirstForcedThenFalse),
            "first-forced-then-true" => Ok(AutoResearchMode::FirstForcedThenTrue),
            // Legacy "first-forced" / "first-force" maps to FirstForcedThenFalse for backward compatibility
            "first-forced" | "first-force" => {
                warn!("Legacy 'first-forced' / 'first-force' for AutoResearchMode is deprecated. Use 'first-forced-then-false' or 'first-forced-then-true'. Defaulting to 'first-forced-then-false'.");
                Ok(AutoResearchMode::FirstForcedThenFalse)
            }
            "expert-only" => Ok(AutoResearchMode::ExpertOnly),
            "no-files" => Ok(AutoResearchMode::NoFiles),
            "expert-and-no-files-only" => Ok(AutoResearchMode::ExpertAndNoFilesOnly),
            _ => Err(format!(
                "Invalid AutoResearchMode: '{}'. Must be one of 'false', 'true', 'forced', 'force', 'first', 'first-forced-then-false', 'first-forced-then-true', 'expert-only', 'no-files', 'expert-and-no-files-only'.", s
            )),
        }
    }
}
// --- End AutoResearchMode Enum ---

impl fmt::Display for NoThinkMode {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            NoThinkMode::None => write!(f, "none"),
            NoThinkMode::All => write!(f, "all"),
            NoThinkMode::EditingPlanning => write!(f, "editing-planning"),
            NoThinkMode::EditingCoding => write!(f, "editing-coding"),
            NoThinkMode::ResearchAutoDecision => write!(f, "research-auto-decision"),
            NoThinkMode::ResearchPlanning => write!(f, "research-planning"),
            NoThinkMode::ResearchSearching => write!(f, "research-searching"),
            NoThinkMode::Ask => write!(f, "ask"),
            NoThinkMode::Summaries => write!(f, "summaries"),
            NoThinkMode::ExpertAutoDecision => write!(f, "expert-auto-decision"),
            NoThinkMode::ExpertPlanning => write!(f, "expert-planning"),
            NoThinkMode::ExpertEditing => write!(f, "expert-editing"),
        }
    }
}

impl FromStr for NoThinkMode {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "none" => Ok(NoThinkMode::None),
            "all" => Ok(NoThinkMode::All),
            "editing-planning" | "editing-plan" | "planning" => Ok(NoThinkMode::EditingPlanning), // "editing-plan", "planning" for backward compatibility
            "editing-coding" | "editing" => Ok(NoThinkMode::EditingCoding), // "editing" for backward compatibility
            "research-auto-decision" => Ok(NoThinkMode::ResearchAutoDecision),
            "research-planning" | "research" => Ok(NoThinkMode::ResearchPlanning), // "research" for backward compatibility
            "research-searching" => Ok(NoThinkMode::ResearchSearching),
            "ask" | "question" => Ok(NoThinkMode::Ask), // "question" for backward compatibility
            "summaries" => Ok(NoThinkMode::Summaries),
            "expert-auto-decision" => Ok(NoThinkMode::ExpertAutoDecision),
            "expert-planning" => Ok(NoThinkMode::ExpertPlanning),
            "expert-editing" => Ok(NoThinkMode::ExpertEditing),
            _ => Err(format!(
                "Invalid NoThinkMode: '{}'. Must be one of 'none', 'all', 'editing-planning', 'editing-coding', 'research-auto-decision', 'research-planning', 'research-searching', 'ask', 'summaries', 'expert-auto-decision', 'expert-planning', 'expert-editing'.", s
            )),
        }
    }
}

impl AppConfig {
    fn determine_api_key(
        model_specific_key: Option<&String>, // API key from ModelConfigEntry (highest priority)
        provider_name: &str,                 // Provider name for environment variable lookup
    ) -> String {
        if let Some(key) = model_specific_key {
            if !key.is_empty() {
                // Ensure "none" which becomes None (due to custom deserializer) isn't returned as empty string here
                return key.clone();
            }
        }
        // Removed cli_global_key check here. Fallback is directly to environment variables.

        // Determine environment variable name based on provider
        // This part remains the same as it's the final fallback.
        let env_var_name = match provider_name.to_lowercase().as_str() {
            "openai-compatible" => "OPENAI_API_KEY".to_string(),
            "openrouter" => "OPENROUTER_API_KEY".to_string(),
            // For GenAI native providers, GenAI itself often checks provider-specific env vars
            // like OPENAI_API_KEY, ANTHROPIC_API_KEY, GOOGLE_API_KEY, OLLAMA_HOST.
            // We can also set them explicitly if needed, or rely on GenAI's internal handling.
            // For simplicity here, we'll check for a generic pattern if not explicitly handled.
            // GenAIAdapter's `new` method also sets these based on its logic.
            // This function is more about *retrieving* a key if one isn't directly provided.
            _ => format!("{}_API_KEY", provider_name.to_uppercase()),
        };

        env::var(env_var_name).unwrap_or_default()
    }

    pub fn determine_effective_provider_url(
        provider_name: &str,
        url_from_model_config: Option<String>, // URL from the specific ModelConfigEntry
    ) -> String {
        let provider_lower = provider_name.to_lowercase();

        if provider_lower == "openrouter" {
            // OpenRouter has a fixed base URL.
            // Warn if user tried to set it, but always use the fixed one.
            if url_from_model_config.is_some()
                && url_from_model_config.as_deref() != Some("https://openrouter.ai/api/v1/")
            {
                warn!(
                    "Provider URL for 'openrouter' is fixed to 'https://openrouter.ai/api/v1/' and cannot be changed. Ignoring configured URL: {:?}",
                    url_from_model_config
                );
            }
            return "https://openrouter.ai/api/v1/".to_string();
        }

        // For other providers, check model config, then environment variable
        let env_var_name = match provider_lower.as_str() {
            "openai-compatible" => "OPENAI_API_BASE".to_string(),
            // GenAIAdapter handles env vars like OLLAMA_HOST, OPENAI_API_BASE internally.
            // This function is primarily for `openai-compatible` or if we need to override GenAI's logic.
            _ => format!("{}_API_BASE", provider_name.to_uppercase()),
        };
        let url_from_env = env::var(env_var_name).ok();

        let mut raw_url = url_from_model_config // URL from ModelConfigEntry has precedence
            .or(url_from_env) // Then environment variable
            .unwrap_or_else(|| {
                // Default to empty string; specific clients (OpenAIClient, GenAIAdapter)
                // will handle their own defaults or use what's set in env by GenAIAdapter.
                "".to_string()
            });

        if !raw_url.is_empty() && !raw_url.ends_with('/') {
            raw_url.push('/');
        }
        raw_url
    }

    fn determine_log_level(args_level: Option<u8>, file_level: Option<u8>) -> LogLevel {
        LogLevel::from(args_level.or(file_level).unwrap_or(DEFAULT_LOG_LEVEL_U8))
    }

    // Updated to handle string list from CLI and Vec<String> from FileConfig
    fn determine_no_think_mode(
        args_mode_str: &Option<String>, // Space-separated string from CLI
        file_modes_vec: &Option<Vec<String>>, // Vec of strings from FileConfig
    ) -> HashSet<NoThinkMode> {
        let mut modes = HashSet::new();
        let mut modes_to_parse: Vec<String> = Vec::new();

        if let Some(cli_str) = args_mode_str {
            modes_to_parse.extend(cli_str.split_whitespace().map(String::from));
        } else if let Some(yaml_vec) = file_modes_vec {
            modes_to_parse.extend(yaml_vec.clone());
        }

        if modes_to_parse.is_empty() {
            return modes; // Default is an empty set (no /no_think)
        }

        for s in modes_to_parse {
            match NoThinkMode::from_str(&s) {
                Ok(mode) => {
                    modes.insert(mode);
                }
                Err(e) => {
                    warn!(
                        "Invalid no_think mode '{}' in configuration: {}. Ignoring.",
                        s, e
                    );
                }
            }
        }
        modes
    }

    // Helper for AutoResearchMode
    fn determine_auto_research_mode(
        args_mode_str: &Option<String>, // String from CLI
        file_mode_str: &Option<String>, // String from FileConfig
        default_mode: AutoResearchMode,
    ) -> AutoResearchMode {
        let mode_str_to_parse = args_mode_str.as_ref().or(file_mode_str.as_ref());

        match mode_str_to_parse {
            Some(s) => {
                match AutoResearchMode::from_str(s) {
                    Ok(mode) => mode,
                    Err(e) => {
                        warn!("Invalid auto_research mode '{}' in configuration: {}. Using default ({}).", s, e, default_mode);
                        default_mode
                    }
                }
            }
            None => default_mode,
        }
    }

    // Helper for AutoExpertSwitch
    fn determine_auto_expert_switch(
        args_switch_str: &Option<String>,
        file_switch_str: &Option<String>,
        default_switch: AutoExpertSwitch,
    ) -> AutoExpertSwitch {
        let switch_str_to_parse = args_switch_str.as_ref().or(file_switch_str.as_ref());
        match switch_str_to_parse {
            Some(s) => {
                match AutoExpertSwitch::from_str(s) {
                    Ok(switch) => switch,
                    Err(e) => {
                        warn!("Invalid auto_expert switch '{}' in configuration: {}. Using default ({}).", s, e, default_switch);
                        default_switch
                    }
                }
            }
            None => default_switch,
        }
    }

    // Helper for AutoExpertMode
    fn determine_auto_expert_mode(
        args_mode_str: &Option<String>,
        file_mode_str: &Option<String>,
        default_mode: AutoExpertMode,
    ) -> AutoExpertMode {
        let mode_str_to_parse = args_mode_str.as_ref().or(file_mode_str.as_ref());
        match mode_str_to_parse {
            Some(s) => {
                match AutoExpertMode::from_str(s) {
                    Ok(mode) => mode,
                    Err(e) => {
                        warn!("Invalid auto_expert mode '{}' in configuration: {}. Using default ({}).", s, e, default_mode);
                        default_mode
                    }
                }
            }
            None => default_mode,
        }
    }

    fn determine_boolean_flag(
        cli_flag: bool,
        file_flag: Option<bool>,
        default_value: bool,
    ) -> bool {
        if cli_flag {
            // This assumes cli_flag being true means "activate the feature"
            true
        } else {
            file_flag.unwrap_or(default_value)
        }
    }

    // Specific logic for timestamps where CLI flag --no-timestamps disables it (default true)
    fn determine_timestamps_setting(
        cli_no_timestamps_flag: bool,
        file_timestamps_setting: Option<bool>,
    ) -> bool {
        if cli_no_timestamps_flag {
            // If --no-timestamps is present
            false // Timestamps are disabled
        } else {
            file_timestamps_setting.unwrap_or(true) // Otherwise, use file setting or default to true
        }
    }

    pub fn new(args: Args, file_conf: FileConfig) -> Self {
        let chosen_alias_opt: Option<String> = args
            .default_model
            .clone()
            .or(file_conf.default_model.clone());
        let mut effective_model_alias_name = DEFAULT_MODEL.to_string();
        let mut selected_provider = DEFAULT_PROVIDER.to_string();
        let mut selected_model_name = DEFAULT_MODEL.to_string();
        let mut selected_model_config_api_key: Option<String> = None;
        let mut selected_model_config_provider_url: Option<String> = None;

        if let Some(list) = &file_conf.models_list {
            if !list.is_empty() {
                let alias_to_use = chosen_alias_opt
                    .clone()
                    .or_else(|| list.first().map(|entry| entry.alias.clone()));

                if let Some(alias) = alias_to_use {
                    effective_model_alias_name = alias.clone();
                    if let Some(entry) = list.iter().find(|e| e.alias == alias) {
                        info!(
                            "Using model configuration for alias '{}': provider='{}', model='{}'",
                            alias, entry.provider, entry.model
                        );
                        selected_provider = entry.provider.clone();
                        selected_model_name = entry.model.clone();
                        selected_model_config_api_key = entry.provider_api_key.clone(); // Changed to provider_api_key
                        selected_model_config_provider_url = entry.provider_url.clone();
                    } else {
                        warn!(
                            "Model alias '{}' not found in models_list. Falling back to default provider/model.",
                            alias
                        );
                    }
                }
            } else if chosen_alias_opt.is_some() {
                warn!(
                    "Default model alias '{}' was specified, but models_list is empty. Falling back to default provider/model.",
                    chosen_alias_opt.as_ref().unwrap() // Used as_ref() here
                );
                effective_model_alias_name = chosen_alias_opt.unwrap_or(DEFAULT_MODEL.to_string());
            }
        } else if chosen_alias_opt.is_some() {
            warn!(
                "Default model alias '{}' was specified, but models_list is missing. Falling back to default provider/model.",
                chosen_alias_opt.as_ref().unwrap() // Used as_ref() here
            );
            effective_model_alias_name = chosen_alias_opt.unwrap_or(DEFAULT_MODEL.to_string());
        }

        // let final_cli_provider_api_key = args.provider_api_key.clone(); // Removed CLI API key

        let final_effective_provider_api_key = Self::determine_api_key(
            selected_model_config_api_key.as_ref(), // Key from the selected ModelConfigEntry
            // final_cli_provider_api_key.as_ref(),    // Removed CLI key argument
            &selected_provider, // Provider name for env var lookup
        );

        let final_provider_url = Self::determine_effective_provider_url(
            &selected_provider,
            selected_model_config_provider_url,
        );

        let final_log_level_enum = Self::determine_log_level(args.log_level, file_conf.log_level);

        // Use determine_no_think_mode for the new no_think logic
        let final_no_think_modes_set =
            Self::determine_no_think_mode(&args.no_think, &file_conf.no_think);

        // Determine ResultsOutputMode
        let final_results_output_mode = if args.results_output_file.is_some() {
            ResultsOutputMode::Advanced // If output file is specified, mode is Advanced (to file)
        } else {
            args.results_output_mode // CLI flag for mode
                .as_deref()
                .map_or_else(
                    || file_conf.results_output_mode.as_deref().map_or(DEFAULT_RESULTS_OUTPUT_MODE, |s| ResultsOutputMode::from_str(s).unwrap_or_else(|e| {
                        warn!("Invalid results_output_mode '{}' in file config: {}. Using default.", s, e);
                        DEFAULT_RESULTS_OUTPUT_MODE
                    })),
                    |s| ResultsOutputMode::from_str(s).unwrap_or_else(|e| {
                        warn!("Invalid results_output_mode '{}' from CLI: {}. Using default.", s, e);
                        DEFAULT_RESULTS_OUTPUT_MODE
                    })
                )
        };

        // let final_results_output = Self::determine_boolean_flag(args.results_output, file_conf.results_output, false); // Removed
        // let final_interactive = Self::determine_boolean_flag(args.interactive, file_conf.interactive, false); // Removed
        let final_exit_on_success =
            Self::determine_boolean_flag(args.exit_on_success, file_conf.exit_on_success, false); // Added
        let final_timestamps =
            Self::determine_timestamps_setting(args.no_timestamps, file_conf.timestamps);
        let final_restore_previous_session = Self::determine_boolean_flag(
            false,
            file_conf.restore_previous_session_on_startup,
            false,
        );
        let final_restore_previous_session_models = Self::determine_boolean_flag(
            false,
            file_conf.restore_previous_session_models,
            false,
        );

        let final_auto_test_command = args
            .auto_test_command
            .clone()
            .or(file_conf.auto_test_command.clone())
            .unwrap_or_default(); // Default to empty string if not set anywhere
        let final_auto_test_toggle =
            Self::determine_boolean_flag(args.auto_test_toggle, file_conf.auto_test_toggle, false); // This is for the boolean on/off for tests
        let final_max_task_retries = file_conf.max_task_retries.unwrap_or(99999);

        // Determine AutoResearchMode
        let final_auto_research_mode = Self::determine_auto_research_mode(
            &args.auto_research,      // CLI arg for auto-research mode
            &file_conf.auto_research, // FileConfig field for auto-research mode
            AutoResearchMode::True,   // Default to True (equivalent to old "on")
        );

        let final_notification_command = args
            .notification_command
            .clone()
            .or(file_conf.notification_command.clone())
            .unwrap_or_default();

        // Determine AutoExpert settings
        let final_auto_expert_switch = Self::determine_auto_expert_switch(
            &args.auto_expert,
            &file_conf.auto_expert,
            AutoExpertSwitch::False, // Default to False
        );
        let final_expert_model = args.expert_model.clone().or(file_conf.expert_model.clone()); // Default to None if not set

        let final_auto_expert_mode = Self::determine_auto_expert_mode(
            &args.auto_expert_mode,
            &file_conf.auto_expert_mode,
            AutoExpertMode::Planning, // Default to Planning
        );

        let final_expert_model_auto_research_loop_max =
            file_conf.expert_model_auto_research_loop_max.unwrap_or(2); // Added, default 2

        let final_results_output_file = args
            .results_output_file
            .clone()
            .or(file_conf.results_output_file.clone());

        let final_results_input_file = args
            .results_input_file
            .clone() // Changed from message_history_file
            .or(file_conf.results_input_file.clone());

        let final_task_info = args.task_info.clone().or(file_conf.task_info.clone());

        let final_decision_model_auto_research_loop_max =
            file_conf.decision_model_auto_research_loop_max.unwrap_or(2); // Added, default 2

        let final_retry_model = args.retry_model.clone().or(file_conf.retry_model.clone());
        let final_retry_model_max_attempts = args.retry_model_max_attempts
            .or(file_conf.retry_model_max_attempts)
            .unwrap_or(3); // Default to 3 attempts
        let final_summary_model = args
            .summary_model
            .clone()
            .or(file_conf.summary_model.clone()); // Added

        let final_research_model = args
            .research_model
            .clone()
            .or(file_conf.research_model.clone());

        let final_decision_model = args
            .decision_model
            .clone()
            .or(file_conf.decision_model.clone());

        let final_http_api_port = args
            .http_api_port
            .or(file_conf.http_api_port)
            .unwrap_or(11991);

        let final_advanced_language_features: HashSet<String> =
            file_conf.advanced_language_features.into_iter().collect();

        // Sound system configuration
        let final_sounds_master_volume = file_conf
            .sounds_master_volume
            .unwrap_or(25) // Default to 25
            .clamp(0, 100); // Clamp to valid range

        let final_submit_sound = match file_conf.submit_sound.as_deref() {
            Some("true") => SubmitSoundConfig::SystemDefault,
            Some("false") | None => SubmitSoundConfig::Disabled,
            Some(path) => SubmitSoundConfig::CustomPath(path.to_string()),
        };

        AppConfig {
            user_prompt: args.message.clone().unwrap_or_default(), // Populate from -m/--message
            file_paths: args.file_paths.clone(),
            default_model: effective_model_alias_name,
            provider: selected_provider, // Use directly
            model: selected_model_name,  // Use directly
            // cli_provider_api_key: final_cli_provider_api_key, // Removed
            effective_provider_api_key: final_effective_provider_api_key,
            provider_url: final_provider_url,
            models_list: file_conf.models_list.clone(), // Store the models_list
            results_input_file: final_results_input_file, // Changed from message_history_file
            results_input: args.results_input.clone(),  // Changed from message_history
            task_info: final_task_info,
            no_think: final_no_think_modes_set, // Assign the determined set of modes
            // results_output: final_results_output, // Removed
            results_output_mode: final_results_output_mode, // Added
            results_output_file: final_results_output_file,
            log_level: final_log_level_enum,
            // interactive: final_interactive, // Removed
            exit_on_success: final_exit_on_success, // Added
            timestamps: final_timestamps,
            restore_previous_session_on_startup: final_restore_previous_session,
            restore_previous_session_models: final_restore_previous_session_models,
            auto_test_command: final_auto_test_command,
            auto_test_toggle: final_auto_test_toggle,
            max_task_retries: final_max_task_retries,
            auto_research_mode: final_auto_research_mode,
            auto_expert_switch: final_auto_expert_switch,
            expert_model: final_expert_model, // Renamed
            auto_expert_mode: final_auto_expert_mode,
            expert_model_auto_research_loop_max: final_expert_model_auto_research_loop_max, // Added
            notification_command: final_notification_command,
            forced_research_for_next_cycle: None, // Initialize as None
            decision_model_auto_research_loop_max: final_decision_model_auto_research_loop_max, // Added
            retry_model: final_retry_model,       // Renamed
            retry_model_max_attempts: final_retry_model_max_attempts, // Added
            summary_model: final_summary_model,   // Renamed
            research_model: final_research_model, // Renamed
            decision_model: final_decision_model,
            advanced_language_features: final_advanced_language_features,
            http_api_port: final_http_api_port,
            sounds_master_volume: final_sounds_master_volume,
            submit_sound: final_submit_sound,
        }
    }

    // Helper to get model configuration by alias
    pub fn get_model_config_by_alias(&self, alias: &str) -> Option<&ModelConfigEntry> {
        self.models_list
            .as_ref()
            .and_then(|list| list.iter().find(|entry| entry.alias == alias))
    }
}

// Moved and adapted from llm_config.rs
pub fn setup_llm_client(app_config: &AppConfig) -> Result<Arc<dyn LLMClient>, String> {
    let provider_lower = app_config.provider.to_lowercase();
    let api_key = app_config.effective_provider_api_key.clone(); // Use effective_provider_api_key
    let base_url = app_config.provider_url.clone(); // This is already determined with provider logic
    let model_name = app_config.model.clone();

    match provider_lower.as_str() {
        "openai-compatible" | "openrouter" => {
            info!(
                "Setting up OpenAIClient for provider '{}' with model '{}' at base URL '{}'",
                provider_lower, model_name, base_url
            );
            Ok(Arc::new(OpenAIClient::new(api_key, base_url, model_name)))
        }
        _ => {
            // For other providers (e.g., "openai", "anthropic", "ollama"), use GenAIAdapter
            info!(
                "Setting up GenAIAdapter for provider '{}' with model '{}'",
                app_config.provider, model_name
            );
            // GenAIAdapter's `new` method expects Option<String> for api_key and base_url.
            // It will then set environment variables that the underlying GenAI client picks up.
            // The `api_key` and `base_url` here are the *effective* ones after all config resolution.
            let api_key_opt = if api_key.is_empty() {
                None
            } else {
                Some(api_key)
            };
            let base_url_opt = if base_url.is_empty() {
                None
            } else {
                Some(base_url)
            };

            match GenAIAdapter::new(
                api_key_opt,
                base_url_opt,
                model_name,
                &provider_lower, // Pass the original provider name for GenAI logic
            ) {
                Ok(adapter) => Ok(Arc::new(adapter)),
                Err(e) => {
                    let err_msg = format!(
                        "Failed to setup GenAIAdapter for provider '{}' with model '{}': {}",
                        app_config.provider, app_config.model, e
                    );
                    error!("{}", err_msg);
                    Err(err_msg)
                }
            }
        }
    }
}

// Setup a specific LLM client based on a model alias
pub fn setup_specific_llm_client(
    app_config: &AppConfig, // Main app config for global settings like CLI overrides
    model_alias: &str,
) -> Result<Arc<dyn LLMClient>, String> {
    if let Some(model_entry) = app_config.get_model_config_by_alias(model_alias) {
        let provider_name_lower = model_entry.provider.to_lowercase();
        let model_name_specific = model_entry.model.clone();

        // Determine API key for the specific model_entry
        let api_key_specific = AppConfig::determine_api_key(
            model_entry.provider_api_key.as_ref(), // Key from the specific ModelConfigEntry
            // app_config.cli_provider_api_key.as_ref(), // Removed CLI key argument
            &model_entry.provider, // Provider name for env var lookup
        );

        // Determine effective URL for this specific model
        let effective_url_specific = AppConfig::determine_effective_provider_url(
            &model_entry.provider,
            model_entry.provider_url.clone(),
        );

        match provider_name_lower.as_str() {
            "openai-compatible" | "openrouter" => {
                info!(
                    "Setting up OpenAIClient for specific alias '{}', provider '{}', model '{}' at base URL '{}'",
                    model_alias, provider_name_lower, model_name_specific, effective_url_specific
                );
                Ok(Arc::new(OpenAIClient::new(
                    api_key_specific,
                    effective_url_specific,
                    model_name_specific,
                )))
            }
            _ => {
                // For other GenAI native providers
                info!(
                    "Setting up GenAIAdapter for specific alias '{}', provider '{}', model '{}'",
                    model_alias, model_entry.provider, model_name_specific
                );
                let api_key_opt = if api_key_specific.is_empty() {
                    None
                } else {
                    Some(api_key_specific)
                };
                let base_url_opt = if effective_url_specific.is_empty() {
                    None
                } else {
                    Some(effective_url_specific)
                };

                match GenAIAdapter::new(
                    api_key_opt,
                    base_url_opt,
                    model_name_specific,
                    &provider_name_lower,
                ) {
                    Ok(adapter) => Ok(Arc::new(adapter)),
                    Err(e) => {
                        let err_msg = format!("Failed to setup specific GenAIAdapter for alias '{}' (provider '{}', model '{}'): {}", model_alias, model_entry.provider, model_entry.model, e);
                        error!("{}", err_msg);
                        Err(err_msg)
                    }
                }
            }
        }
    } else {
        Err(format!(
            "Model alias '{}' not found in configuration.",
            model_alias
        ))
    }
}

// Moved and adapted from llm_config.rs
pub async fn load_data_from_results_input(
    // Renamed from load_message_history
    app_config: &AppConfig,
    tasks: &mut Vec<Task>,
    qna_history: &mut Vec<crate::interactive::app::QuestionAnswerPair>, // Added qna_history
) -> Result<(), Box<dyn std::error::Error>> {
    let mut results_content: Option<String> = None;

    if let Some(file_path) = &app_config.results_input_file {
        // Changed from message_history_file
        match tokio::fs::read_to_string(file_path).await {
            Ok(content) => results_content = Some(content),
            Err(e) => warn!(
                "Could not read results input file {}: {}",
                file_path.display(),
                e
            ),
        }
    } else if let Some(json_string) = &app_config.results_input {
        // Changed from message_history
        results_content = Some(json_string.clone());
    }

    if let Some(json_data) = results_content {
        // Attempt to deserialize directly into crate::result::Result
        match serde_json::from_str::<crate::result::Result>(&json_data) {
            Ok(loaded_result_data) => {
                let num_tasks = loaded_result_data.tasks.len();
                for st_task in loaded_result_data.tasks {
                    tasks.push(st_task.into());
                }
                info!("Loaded {} tasks from results input.", num_tasks);

                let num_qna = loaded_result_data.question_answer_history.len();
                for sqa_pair in loaded_result_data.question_answer_history {
                    qna_history.push(crate::interactive::app::QuestionAnswerPair {
                        question: sqa_pair.question,
                        answer: sqa_pair.answer,
                    });
                }
                if num_qna > 0 {
                    info!(
                        "Loaded {} question/answer pairs from results input.",
                        num_qna
                    );
                }
                // files_edited are not loaded back into context for now.
            }
            Err(e_direct_result) => {
                // If direct deserialization fails, try the old history formats as a fallback
                warn!("Could not parse results input as full Result struct: {}. Attempting legacy history formats.", e_direct_result);
                match serde_json::from_str::<AnyHistoryFileFormat>(&json_data) {
                    Ok(AnyHistoryFileFormat::New(new_history)) => {
                        let count = new_history.tasks.len();
                        for st_task in new_history.tasks {
                            tasks.push(st_task.into());
                        }
                        info!(
                            "Loaded {} tasks from legacy (task-only) history format.",
                            count
                        );
                    }
                    Ok(AnyHistoryFileFormat::Old(old_history)) => {
                        let count = old_history.message_history.len();
                        if count > 0 {
                            let first_user_message_content = old_history
                                .message_history
                                .iter()
                                .find(|msg| {
                                    matches!(msg.role, crate::result::SerializableChatRole::User)
                                })
                                .map(|msg| msg.content.clone())
                                .unwrap_or_else(|| {
                                    "Loaded from legacy (message-only) history".to_string()
                                });

                            let mut single_task = Task::new(first_user_message_content, None);
                            for s_msg in old_history.message_history {
                                single_task.add_message(s_msg.into());
                            }
                            tasks.push(single_task);
                            info!("Loaded {} messages from legacy (message-only) history into a single task.", count);
                        } else {
                            info!("Legacy (message-only) history was empty.");
                        }
                    }
                    Err(e_legacy) => {
                        warn!("Could not parse results input as legacy history format either: {}. Attempting to parse as raw message list (very old format).", e_legacy);
                        // Fallback for very old format: just a list of SerializableChatMessage
                        match serde_json::from_str::<Vec<SerializableChatMessage>>(&json_data) {
                            Ok(raw_messages) => {
                                let count = raw_messages.len();
                                if count > 0 {
                                    let first_user_message_content = raw_messages
                                        .iter()
                                        .find(|msg| {
                                            matches!(
                                                msg.role,
                                                crate::result::SerializableChatRole::User
                                            )
                                        })
                                        .map(|msg| msg.content.clone())
                                        .unwrap_or_else(|| {
                                            "Loaded from very old history format".to_string()
                                        });

                                    let mut single_task =
                                        Task::new(first_user_message_content, None);
                                    for s_msg in raw_messages {
                                        single_task.add_message(s_msg.into());
                                    }
                                    tasks.push(single_task);
                                    info!("Loaded {} messages from very old history format into a single task.", count);
                                } else {
                                    info!("Very old message history format was empty.");
                                }
                            }
                            Err(e_raw) => {
                                warn!(
                                    "Failed to parse results input as any known format: {}",
                                    e_raw
                                );
                            }
                        }
                    }
                }
            }
        }
    }
    Ok(())
}

// Function to save main message history
pub async fn save_message_history(
    tasks: &Vec<Task>,
    file_path: &Option<PathBuf>,
) -> Result<(), Box<dyn std::error::Error>> {
    if let Some(path) = file_path {
        if tasks.is_empty() {
            info!(
                "No main tasks to save to {}. Writing empty task list.",
                path.display()
            );
        }

        let serializable_tasks: Vec<SerializableTask> = tasks
            .iter()
            .map(|task| SerializableTask::from(task)) // Use From<&Task>
            .collect();

        let history_to_save = NewHistoryFileFormat {
            tasks: serializable_tasks,
        };

        match serde_json::to_string_pretty(&history_to_save) {
            Ok(json_data) => match tokio::fs::write(path, json_data).await {
                Ok(_) => info!("Main message history saved to {}", path.display()),
                Err(e) => {
                    error!(
                        "Failed to write main message history to {}: {}",
                        path.display(),
                        e
                    );
                    return Err(Box::new(e));
                }
            },
            Err(e) => {
                error!("Failed to serialize main message history: {}", e);
                return Err(Box::new(e));
            }
        }
    } else {
        debug!("Main message history file path not set. Skipping save.");
    }
    Ok(())
}